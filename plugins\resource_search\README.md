# 资源搜索插件

## 功能介绍

资源搜索插件支持搜索音乐、教辅、电影、短剧、电视剧、动漫、综艺等各类资源。

## 使用说明

### 支持的命令格式

- `搜 [关键词]` - 普通搜索（短剧、教辅资源、教程、软件等）
- `搜剧 [关键词]` - 搜索剧集资源
- `全网搜 [关键词]` - 全网搜索（电影电视剧动漫综艺）
- `搜资源 [关键词]` - 资源搜索

### 使用示例

```
搜 流浪地球
搜剧 你好李焕英
全网搜 三体
```

### 帮助命令

发送以下任一命令可获取帮助信息：
- `帮助搜索`
- `搜索帮助`
- `资源搜索帮助`

## 搜索结果

搜索结果包含：
- 资源名称
- 下载链接
- 资源分类信息

💡 提示：点击链接即可获取资源

## 配置说明

插件配置文件为 `config.json`，包含以下配置项：

- `enable`: 是否启用插件（true/false）
- `api_url`: API服务地址
- `timeout`: 请求超时时间（秒）
- `search_keywords`: 搜索关键词列表
- `max_results`: 最大返回结果数

## 注意事项

- 全网搜索可能需要较长时间，请耐心等待
- 资源来源于网络，请注意及时保存
- 如遇搜索失败，可尝试更换关键词或稍后重试
