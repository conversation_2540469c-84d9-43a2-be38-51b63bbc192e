{"accounts": [{"sessionid": "67d5e4b738d735f7f9d8863af63c22d4", "description": "账号1"}, {"sessionid": "", "description": "账号2"}], "commands": {"draw": "即梦"}, "params": {"ratios": {"1:1": {"width": 1024, "height": 1024}, "4:3": {"width": 1024, "height": 768}, "3:4": {"width": 768, "height": 1024}, "16:9": {"width": 1024, "height": 576}, "9:16": {"width": 576, "height": 1024}}, "default_ratio": "1:1", "models": {"3.0": {"name": "图片 3.0", "model_req_key": "high_aes_general_v30l:general_v3.0_18b", "description": "影视质感，文字更准，直出2k高清图", "features": ["新模型", "默认场景", "人脸交换", "背景绘画", "文本到图像生成", "ETTA", "角色生成"]}, "2.1": {"name": "图片 2.1", "model_req_key": "high_aes_general_v21_L:general_v2.1_L", "description": "稳定的结构和更强的影视质感，支持生成中、英文文字", "features": ["新模型", "默认场景", "人脸交换", "背景绘画", "文本到图像生成", "ETTA", "角色生成"]}, "2.0p": {"name": "图片 2.0 Pro", "model_req_key": "high_aes_general_v20_L:general_v2.0_L", "description": "大幅提升了多样性和真实的照片质感，开启创新与设计的视觉梦境", "features": ["默认场景", "文本到图像生成", "人脸交换", "背景绘画", "限时折扣", "字节编辑", "风格参考"]}, "2.0": {"name": "图片 2.0", "model_req_key": "high_aes_general_v20:general_v2.0", "description": "更精准的描述词响应和多样的风格组合，模型极具想象力！", "features": ["默认场景", "文本到图像生成", "人脸交换", "背景绘画", "Canny边缘检测", "深度图", "姿态估计", "风格参考", "IP保持"]}, "xl": {"name": "图片 XL Pro", "model_req_key": "text2img_xl_sft", "description": "增强英文生成能力和参考图可控能力，使用引号强化文字效果", "features": ["默认场景", "人脸交换", "背景绘画", "Canny边缘检测", "深度图", "姿态估计", "文本到图像生成", "风格参考", "图像到图像生成"]}}, "default_model": "3.0"}, "storage": {"retention_days": 7}, "video_models": {"s2.0": {"name": "视频 S2.0", "model_req_key": "dreamina_ic_generate_video_model_vgfm_lite", "description": "更快的生成速度, 兼顾高品质效果", "features": ["快速生成", "高品质"]}, "s2.0p": {"name": "视频 S2.0 Pro", "model_req_key": "dreamina_ic_generate_video_model_vgfm1.0", "description": "更合理的动效, 更生动自然的运镜", "features": ["合理动效", "自然运镜"]}, "p2.0p": {"name": "视频 P2.0 Pro", "model_req_key": "dreamina_ailab_generate_video_model_v1.4", "description": "精准响应提示词, 支持生成多镜头", "features": ["精准响应", "多镜头"]}, "1.2": {"name": "视频 1.2", "model_req_key": "dreamina_ailab_generate_video_model_v1.2", "description": "各方面都有较平衡的表现", "features": ["平衡表现"]}}, "video_ratios": {"21:9": {"width": 1024, "height": 394}, "16:9": {"width": 1024, "height": 576}, "4:3": {"width": 1024, "height": 768}, "1:1": {"width": 1024, "height": 1024}, "3:4": {"width": 768, "height": 1024}, "9:16": {"width": 576, "height": 1024}}, "default_video_model": "s2.0", "default_video_ratio": "4:3"}