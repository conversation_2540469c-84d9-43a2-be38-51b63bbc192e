# 猜歌曲游戏插件

## 功能介绍
猜歌曲游戏插件允许用户在微信中玩猜歌曲游戏。

主要功能：
1. 支持通过歌手名搜索歌曲
2. 随机播放歌曲片段，让用户猜测歌曲名称
3. 支持提示系统（包括智能AI提示）
4. 支持群聊和私聊
5. 多轮游戏机制
6. 历史排行榜

## 使用方法
1. 输入 `猜歌曲 歌手名` 开始游戏 (例如: `猜歌曲 周杰伦`)
2. 输入 `提示` 获取歌曲提示
3. 输入 `下一题` 跳过当前题目
4. 输入 `结束游戏` 结束当前游戏
5. 输入 `排行榜` 查看历史排行榜
6. 输入 `我猜 歌名` 或 `猜 歌名` 提交你的答案

## 配置说明
```json
{
    "api_url": "https://api.dragonlongzhu.cn/api/joox/juhe_music.php",  // 歌曲API地址
    "cache_timeout": 300,  // 缓存超时时间（秒）
    "questions_per_round": 5,  // 每轮题目数量
    "leaderboard_size": 10,  // 排行榜显示人数
    "auth_password": "1122",  // 管理员密码
    "enable_group_mode": true,  // 启用群组模式
    "openai": {
        "enabled": false,  // 是否启用OpenAI
        "api_key": "",  // OpenAI API密钥 
        "model": "gpt-3.5-turbo",  // 模型名称
        "api_base": "https://api.openai.com/v1",  // API基础URL
        "timeout": 10  // 超时时间（秒）
    },
    "game_settings": {
        "time_limit": 20,  // 答题时限（秒）
        "auto_next_delay": 1,  // 自动下一题延迟（秒）
        "correct_answer_delay": 3,  // 答对后下一题延迟（秒）
        "clip_duration": 10,  // 歌曲片段时长（秒）
        "use_ai_hints": true  // 是否使用AI提示
    }
}
```

## AI智能提示系统
插件支持使用OpenAI生成智能提示，按照难度级别提供不同层次的提示信息：

### 1级提示（隐晦）
- 解释歌名的大致含义或背后的故事
- 如果歌名是多字词语，提示其中一个字的意思（不直接给出）
- 描述与歌名相关的场景或情感

### 2级提示（中等）
- 明确提示歌名中的一个字（如果是中文歌名）或关键词（英文歌名）
- 解释歌名整体的含义或相关场景
- 引用能暗示歌名的歌词片段

### 3级提示（明显）
- 直接给出歌名中的部分字（但不是完整歌名）
- 明确解释歌名的完整含义
- 使用近义词或形近词来描述歌名中其他部分

使用AI提示需要在配置中启用OpenAI并提供有效的API密钥。

## 猜错反馈功能
当玩家猜错歌曲名称时，系统会提供反馈信息，告知玩家答案不正确，并鼓励继续尝试。这一功能增强了游戏的互动性和用户体验。

## 管理员命令
1. `auth 密码` - 认证为管理员
2. `重置排行榜` - 清空排行榜数据 (需管理员权限)
3. `清理排行榜` - 清理排行榜中低分或不活跃用户 (需管理员权限)
4. `猜歌曲设置 片段时长=10` - 设置音频片段长度 (需管理员权限)

## 游戏规则
- 每轮默认5道题
- 答题时限默认120秒
- 音频片段长度默认10秒
- 使用提示不会影响得分
- 猜对歌名得1分，答题时间不影响得分
- 猜错会有提示，可以继续尝试

## 注意事项
- 游戏需要下载音频文件，请确保网络连接良好
- 歌曲资源基于API提供，如遇API服务不稳定可能影响游戏体验
- 使用AI提示需要配置有效的OpenAI API密钥，否则将使用传统提示
- OpenAI API调用需要消耗API额度，请合理配置使用

## 更新日志
最新更新：
- 优化了AI提示系统，现在提示会针对歌名的字和含义进行更精确的引导
- 添加了猜错反馈功能，提供更好的用户体验
- 修复了配置文件加载和保存的问题
- 修复了其他已知问题 