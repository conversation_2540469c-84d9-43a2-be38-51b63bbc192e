# 豆包AI绘画插件

这是一个用于与豆包AI进行交互的插件，支持AI绘画功能。

## 功能特点

- 支持文本生成图片
- 支持参考图生成
- 支持抠图功能
- 支持区域重绘
- 支持图片编辑、扩图和重新生成
- 支持豆包最新的图像生成API

## 配置说明

在 `config.json` 中配置以下参数：

```json
{
    "commands": [
        {
            "name": "draw",
            "aliases": ["豆包"]
        },
        ...
    ],
    "auth": {
        "cookie": "你的豆包cookie",
        "msToken": "你的msToken",
        "a_bogus": "你的a_bogus"
    },
    "storage": {
        "retention_days": 7
    },
    "api": {
        "version": 2,
        "base_url": "https://www.doubao.com"
    },
    "styles": ["人像摄影", "电影写真", ...],
    "params": {
        "ratios": ["1:1", "2:3", "4:3", "9:16", "16:9"],
        "default_ratio": "4:3"
    }
}
```

### 认证信息获取方法

1. 登录豆包网站 https://www.doubao.com
2. 打开浏览器开发者工具（F12）
3. 切换到网络（Network）标签
4. 刷新页面，找到任意一个请求
5. 在请求头（Headers）中找到 Cookie、msToken 和 x-bogus（对应 a_bogus）的值

## 使用方法

### 基本命令

- `豆包 [提示词] [-风格] [-比例]`: 生成图片
- `豆包新建会话`: 强制新建会话
- `参考图 [提示词] [-风格] [-比例]`: 使用参考图生成图片
- `抠图`: 抠出图片主体
- `重绘 [描述词]`: 区域重绘图片
- `豆包换背景 [背景描述词]`: 自动识别主体并替换背景
- `豆包换主体 [主体描述词]`: 自动识别主体并替换主体

### 图片编辑命令

- `$u 图片ID 序号(1-4)`: 放大图片
- `$v 图片ID 序号(1-4) 编辑提示词`: 编辑图片（首次编辑需要序号）
- `$v 图片ID 编辑提示词`: 编辑图片（二次编辑直接编辑）
- `$k 图片ID 序号(1-4) 比例(1:1/4:3/16:9/9:16/max)`: 扩图（首次扩图需要序号）
- `$k 图片ID 比例`: 扩图（二次扩图直接扩图）
- `$r 图片ID`: 重新生成图片

## 更新日志

### v1.1.0 (2023-11-01)
- 添加对豆包最新图像生成API的支持
- 优化图像生成流程
- 修复已知问题

### v1.0.0 (2023-10-01)
- 初始版本发布
