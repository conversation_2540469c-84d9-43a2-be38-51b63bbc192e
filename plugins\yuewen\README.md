# 跃问AI助手插件 (<PERSON><PERSON><PERSON> Plugin)

**版本:** 0.1

**作者:** 八戒

跃问AI助手插件是一款集成多模态交互能力的AI工具，支持文本对话、模型管理、图片识别、视频生成及对话分享等功能，通过简洁指令实现高效人机交互。

## 功能特点

- 多模态交互：支持文本对话、图片识别、视频生成，满足多样化需求。
- 模型管理：实时查看、切换可用模型，支持联网/非联网模式动态切换。
- 安全登录：通过手机验证码完成登录验证，保障账户安全。
- 状态同步：自动同步服务器模型及网络状态，保持本地与云端配置一致。
- 对话分享：支持将最近对话生成图片分享，便于快速传播关键内容。
- 错误处理：内置重试机制和清晰错误提示，提升稳定性。

## 依赖安装

```bash
pip install httpx requests opencv-python numpy Pillow
```

## 依赖说明

| 库名           | 功能描述                                        |
| -------------- | ----------------------------------------------- |
| `httpx`        | 高性能HTTP客户端，支持HTTP/2和超时控制。         |
| `requests`     | 辅助HTTP请求，处理登录及验证码流程。             |
| `opencv-python` | 图像处理，用于获取图片尺寸等信息。               |
| `numpy`        | 数值计算，辅助图片数据处理。                     |
| `Pillow`       | 图像处理库，支持图片读取与格式转换。             |

## 常见功能与指令

### 一、登录与验证

1. **发起登录**

   - 指令：`yw 登录`
   - 说明：启动登录流程，按提示输入11位手机号码获取验证码。

2. **发送手机号码**

   - 指令：`yw 11位手机号码`
   - 说明：在登录提示后输入有效手机号（如：`yw 13812345678`），获取4位验证码。

3. **验证登录**

   - 指令：`yw 4位验证码`
   - 说明：输入收到的验证码完成登录（如：`yw 1234`），自动创建新会话。

### 二、模型管理

1. **显示可用模型**

   - 指令：`yw 打印模型`
   - 说明：列出所有模型及状态（支持联网标识、当前使用模型标记）。
   - 示例输出：
     ```
     可用模型：
     1. deepseek r1（支持联网） ← 当前使用
     2. Step2（支持联网）
     3. Step-R mini（不支持联网）
     4. Step 2-文学大师版（不支持联网）
     ```

2. **切换模型**

   - 指令：`yw 切换模型 [序号]`
   - 说明：切换至指定模型（序号对应 `打印模型` 列表）。
   - 示例：`yw 切换模型 2` 切换至Step2模型。

### 三、基础对话

1. **直接对话**

   - 指令：`yw [问题]`
   - 说明：向跃问AI发送文本提问，支持多轮对话（如：`yw 如何训练AI模型？`）。

2. **新建会话**

   - 指令：`yw 新建会话`
   - 说明：清除历史对话上下文，创建全新会话。

### 四、图片功能

1. **单图识别**

   - 指令：`yw 识图 [提示词]`
   - 说明：结合提示词识别单张图片内容（如：`yw 识图 分析这张猫的图片`）。

2. **多图识别**

   - 指令：`yw 识图 [数量] [提示词]`
   - 说明：指定图片数量（1-3张），按提示依次上传图片（如：`yw 识图 2 对比两张风景图`）。

### 五、视频功能

1. **文生视频**

   - 指令：`yw 视频 [提示词]`
   - 说明：根据文本生成视频，支持：
     - 润色提示词：`yw 视频 星空夜景-润色`
     - 指定镜头语言：`yw 视频 城市全景-拉远`（镜头语言支持：拉近/拉远/左/右/上/下/禁止）。

2. **参考图生成视频**

   - 指令：`yw 参考图 [提示词]`
   - 说明：上传参考图片后，结合提示词生成视频（如：`yw 参考图 海边日落-向上`）。

### 六、联网模式控制

1. **开启联网**

   - 指令：`yw 开启联网`
   - 说明：启用联网模式（仅支持 `deepseek r1` / `Step2` 模型），获取实时信息。

2. **关闭联网**

   - 指令：`yw 关闭联网`
   - 说明：关闭联网模式，模型基于内置知识库回答。

### 七、对话分享

1. **生成分享图片**

   - 指令：`yw 分享`
   - 说明：将最近3分钟内的对话生成图片，包含回答内容、模型信息、耗时等。
   - 限制：需在对话结束后3分钟内使用，支持返回图片，可扫码继续对话。
   - 示例：
     ```
     用户：yw 分享
     插件：[图片]
     ```

### 八、帮助信息

- 指令：`yw` 或 `yw 帮助`
- 说明：显示所有指令列表及格式，支持详细模式查看进阶功能（如图片/视频参数）。

## 登录流程

1. 用户发送 `yw 登录`，按提示输入手机号。
2. 插件发送验证码，用户回复 `yw [验证码]` 完成验证。
3. 登录成功后自动同步服务器状态，创建新会话。

## 注意事项

1. 模型限制：`Step-R mini` / `Step 2-文学大师版` 不支持联网模式，切换时需注意。
2. 图片上传：支持本地路径、URL或直接发送图片文件，需等待文件准备完成。
3. 视频生成：耗时3-5分钟，失败时可重试；镜头语言需按指定关键词输入（如“-拉远”）。
4. 分享时效：`yw 分享` 仅支持最近3分钟内的对话，超时需重新对话后使用。

## 配置说明

- 触发前缀：默认 `yw`，可在配置文件中修改 `trigger_prefix`。
- 模型列表：当前支持4种模型，通过 `yw 打印模型` 查看详情。
- 网络模式：联网状态与模型强相关（如 `deepseek r1` 默认开启联网）。

## 错误处理

- 登录失败：检查手机号/验证码是否正确，重新执行 `yw 登录` 流程。
- 模型切换失败：确认序号有效，或通过 `yw 打印模型` 检查模型状态。
- 图片/视频失败：确保文件格式正确（图片支持JPEG/PNG），或降低生成参数（如减少图片数量）。

通过以上功能，用户可通过简洁指令实现多模态AI交互，高效完成信息查询、内容生成及成果分享。

希望这份文档对你有所帮助！
# yuewen
