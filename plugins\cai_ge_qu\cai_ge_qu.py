# encoding:utf-8
import json
import time
import requests
import plugins
import os
import threading
import random
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from common.log import logger
from plugins import *
import re
from config import conf
from common.tmp_dir import TmpDir
import urllib.parse
import subprocess
import logging

# 定义配置文件路径
CONFIG_PATH = os.path.join(os.path.dirname(__file__), "config.json")

@plugins.register(
    name="CaiGeQu",
    desire_priority=20,
    hidden=False,
    desc="猜歌曲游戏插件",
    version="1.0",
    author="Garson",
)
class CaiGeQu(Plugin):
    """猜歌曲游戏插件
    
    功能：
    1. 支持通过歌手名搜索歌曲
    2. 随机播放歌曲片段，让用户猜测歌曲名称
    3. 支持提示系统
    4. 支持群聊和私聊
    5. 多轮游戏机制
    6. 历史排行榜
    """
    # 默认配置
    DEFAULT_CONFIG = {
        "api_url": "https://api.dragonlongzhu.cn/api/joox/juhe_music.php",
        "cache_timeout": 300,  # 缓存超时时间（5分钟）
        "questions_per_round": 5,  # 每轮题目数量
        "leaderboard_size": 10,  # 排行榜显示人数
        "auth_password": "1122",  # 管理员认证密码
        "enable_group_mode": True,  # 启用群组模式，允许多人在同一个群里共享游戏状态
        "openai": {
            "enabled": False,  # 是否启用OpenAI
            "api_key": "",  # OpenAI API密钥
            "model": "gpt-3.5-turbo",  # 模型名称
            "api_base": "https://api.openai.com/v1",  # API基础URL
            "timeout": 10  # 超时时间（秒）
        },
        "game_settings": {
            "time_limit": 20,  # 每题限时（秒）
            "auto_next_delay": 1,  # 自动进入下一题的延迟（秒）
            "correct_answer_delay": 3,  # 答对后进入下一题的延迟（秒）
            "clip_duration": 10,  # 歌曲片段时长（秒）
            "use_ai_hints": True  # 是否使用AI提示
        },
        "commands": {
            "start_game": ["猜歌曲", "猜歌曲"],
            "help": ["猜歌曲帮助", "猜歌曲 帮助", "猜歌曲帮助", "猜歌曲 帮助"],
            "hint": ["提示"],
            "next_question": ["下一题"],
            "end_game": ["结束游戏"],
            "leaderboard": ["排行榜"],
            "auth": ["auth"],
            "reset_leaderboard": ["重置排行榜"],
            "clean_leaderboard": ["清理排行榜"],
            "settings": ["猜歌曲设置"],
            "guess_prefix": ["我猜", "猜"]
        }
    }

    def __init__(self):
        """初始化插件配置"""
        try:
            super().__init__()
            
            # 确保使用默认配置初始化
            self.config = super().load_config()
            if not self.config:
                self.config = self._load_config_template()
            
            # 使用默认配置初始化
            for key, default_value in self.DEFAULT_CONFIG.items():
                if key not in self.config:
                    self.config[key] = default_value
            
            # 设置配置参数
            self.api_url = self.config.get("api_url")
            self.cache_timeout = self.config.get("cache_timeout", 300)
            self.questions_per_round = self.config.get("questions_per_round", 5)
            self.leaderboard_size = self.config.get("leaderboard_size", 10)
            self.auth_password = self.config.get("auth_password", "1122")
            self.enable_group_mode = self.config.get("enable_group_mode", True)  # 启用群组模式
            
            # 游戏设置
            game_settings = self.config.get("game_settings", {})
            self.time_limit = game_settings.get("time_limit", 20)  # 每题限时（秒）
            self.auto_next_delay = game_settings.get("auto_next_delay", 1)  # 自动进入下一题的延迟（秒）
            self.correct_answer_delay = game_settings.get("correct_answer_delay", 3)  # 答对后进入下一题的延迟（秒）
            self.clip_duration = game_settings.get("clip_duration", 10)  # 歌曲片段时长（秒）
            self.use_ai_hints = game_settings.get("use_ai_hints", True)  # 是否使用AI提示
            
            # 初始化OpenAI助手
            openai_config = self.config.get("openai", {})
            self.openai_enabled = openai_config.get("enabled", False)
            
            api_key = openai_config.get("api_key", "")
            model = openai_config.get("model", "gpt-3.5-turbo")
            api_base = openai_config.get("api_base", "https://api.openai.com/v1")
            timeout = openai_config.get("timeout", 10)
            
            self.openai_helper = OpenAIHelper(
                api_key=api_key,
                model=model,
                api_base=api_base,
                timeout=timeout
            )
            
            # 立即重新加载配置，确保使用最新配置
            self._reload_config()
            
            # 输出关键配置，便于调试
            logger.info(f"[CaiGeQu] 初始化配置: time_limit={self.time_limit}, clip_duration={self.clip_duration}")
            logger.info(f"[CaiGeQu] OpenAI配置: enabled={self.openai_enabled}")
            
            # 管理员列表
            self.admin_users = set()
            
            # 游戏状态缓存
            self.game_states = {}  # 格式: {game_key: {"start_time": timestamp, "song_list": [], "current_song": {...}, "current_round": 1, "score": 0, "question_start_time": timestamp, "current_timer_id": "unique_id"}}
            
            # 歌曲缓存
            self.song_cache = {}  # 格式: {singer: {"update_time": timestamp, "songs": []}}
            
            # 群成员信息缓存
            self.group_members_cache = {}  # 格式: {group_id: {"update_time": timestamp, "members": {wxid: nickname}}}
            self.group_members_lock = threading.Lock()
            
            # 排行榜数据
            self.leaderboard_file = os.path.join(os.path.dirname(__file__), "leaderboard.json")
            self.leaderboard = self._load_leaderboard()  # 加载现有排行榜数据
            
            # 检查排行榜文件是否存在，如果不存在则创建
            if not os.path.exists(self.leaderboard_file):
                self._save_leaderboard()  # 保存空排行榜以创建文件
                logger.info("[CaiGeQu] 排行榜文件不存在，已创建新文件")
                
            # 当前轮次排行榜（只记录本轮游戏）
            self.current_round_scores = {}  # 格式: {user_id: {"name": nickname, "score": score}}
            
            # 初始化定时器线程
            self.timer_thread = None
            self.timer_running = False
            self.timer_lock = threading.Lock()
            
            logger.info(f"[CaiGeQu] 初始化完成, config={self.config}")
            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
        except Exception as e:
            logger.error(f"[CaiGeQu] 初始化异常：{str(e)}", exc_info=True)
            raise Exception("[CaiGeQu] 初始化失败")

    def on_handle_context(self, e_context: EventContext):
        """处理消息上下文"""
        if e_context['context'].type != ContextType.TEXT:
            return

        content = e_context['context'].content.strip()
        context_kwargs = e_context['context'].kwargs
        
        # 重新加载配置，确保使用最新的命令设置
        self._reload_config()
        
        # 获取命令配置
        cmd_config = self.config.get("commands", self.DEFAULT_CONFIG["commands"])
        
        # 获取用户ID
        user_id = None
        msg = context_kwargs.get('msg')
        if msg and hasattr(msg, 'Data') and isinstance(msg.Data, dict):
            from_user = msg.Data.get('FromUserName', {})
            if isinstance(from_user, dict) and 'string' in from_user:
                user_id = from_user['string']
        
        if not user_id and msg:
            if hasattr(msg, 'fromUser'):
                user_id = msg.fromUser
            elif hasattr(msg, 'FromUserName'):
                if isinstance(msg.FromUserName, dict) and 'string' in msg.FromUserName:
                    user_id = msg.FromUserName['string']
                else:
                    user_id = msg.FromUserName
        
        if not user_id:
            session_id = context_kwargs.get('session_id', '')
            if session_id:
                if '@@' in session_id:
                    user_id = session_id.split('@@')[0]
                elif '@' in session_id:
                    user_id = session_id.split('@')[0]
        
        # 获取群组ID（如果有）
        group_id = None
        is_group = context_kwargs.get('isgroup', False)
        if is_group:
            receiver = context_kwargs.get('receiver', '')
            if receiver and '@chatroom' in receiver:
                group_id = receiver
            elif user_id and '@chatroom' in user_id:
                group_id = user_id
                # 如果user_id是群ID，则需要从session_id中提取真正的用户ID
                session_id = context_kwargs.get('session_id', '')
                if '@@' in session_id:
                    user_id = session_id.split('@@')[1]
        
        # 获取游戏键值
        game_key = group_id if (group_id and self.enable_group_mode) else user_id
        
        # 用户名获取
        user_name = self._get_user_name(e_context)
        
        # 根据不同的命令进行处理
        
        # 管理员验证
        auth_cmds = cmd_config.get("auth", ["auth"])
        for cmd in auth_cmds:
            if content.startswith(f"{cmd} "):
                password = content[len(cmd)+1:].strip()
                self._handle_auth(user_id, password, e_context)
                e_context.action = EventAction.BREAK_PASS
                return
        
        # 游戏帮助命令
        help_cmds = cmd_config.get("help", ["猜歌曲帮助", "猜歌曲 帮助"])
        if any(content == cmd for cmd in help_cmds):
            self._show_help(e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 排行榜命令
        leaderboard_cmds = cmd_config.get("leaderboard", ["排行榜"])
        if any(content == cmd for cmd in leaderboard_cmds):
            self._show_leaderboard(e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 设置命令
        settings_cmds = cmd_config.get("settings", ["猜歌曲设置"])
        for cmd in settings_cmds:
            if content.startswith(f"{cmd} "):
                setting_cmd = content[len(cmd)+1:].strip()
                self._update_settings(setting_cmd, user_id, e_context)
                e_context.action = EventAction.BREAK_PASS
                return
        
        # 管理员命令：重置排行榜
        reset_leaderboard_cmds = cmd_config.get("reset_leaderboard", ["重置排行榜"])
        if any(content == cmd for cmd in reset_leaderboard_cmds):
            if self._is_admin(user_id):
                self._reset_leaderboard(e_context)
            else:
                self._send_text_reply("⚠️ 只有管理员才能执行此操作", e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 管理员命令：清理排行榜
        clean_leaderboard_cmds = cmd_config.get("clean_leaderboard", ["清理排行榜"])
        if any(content == cmd for cmd in clean_leaderboard_cmds):
            if self._is_admin(user_id):
                self._clean_leaderboard(e_context)
            else:
                self._send_text_reply("⚠️ 只有管理员才能执行此操作", e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 开始游戏命令
        start_game_cmds = cmd_config.get("start_game", ["猜歌曲"])
        for cmd in start_game_cmds:
            # 精确匹配命令
            if content == cmd:
                self._show_help(e_context)
                e_context.action = EventAction.BREAK_PASS
                return
            # 带参数的命令（歌手名）
            elif content.startswith(f"{cmd} "):
                singer = content[len(cmd)+1:].strip()
                if singer:  # 确保有歌手名
                    self._start_game(game_key, singer, e_context)
                    e_context.action = EventAction.BREAK_PASS
                    return
                else:  # 无歌手名，显示帮助
                    self._show_help(e_context)
                    e_context.action = EventAction.BREAK_PASS
                    return
        
        # 提示命令
        hint_cmds = cmd_config.get("hint", ["提示"])
        if any(content == cmd for cmd in hint_cmds):
            # 检查游戏是否在进行中
            if game_key in self.game_states:
                self._get_hint(game_key, e_context)
            else:
                start_cmd = start_game_cmds[0] if start_game_cmds else "猜歌曲"
                self._send_text_reply(f"⚠️ 没有正在进行的游戏，请先发送\"{start_cmd} 歌手名\"开始游戏", e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 下一题命令
        next_question_cmds = cmd_config.get("next_question", ["下一题"])
        if any(content == cmd for cmd in next_question_cmds):
            # 检查游戏是否在进行中
            if game_key in self.game_states:
                self._next_question(game_key, e_context)
            else:
                start_cmd = start_game_cmds[0] if start_game_cmds else "猜歌曲"
                self._send_text_reply(f"⚠️ 没有正在进行的游戏，请先发送\"{start_cmd} 歌手名\"开始游戏", e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 结束游戏命令
        end_game_cmds = cmd_config.get("end_game", ["结束游戏"])
        if any(content == cmd for cmd in end_game_cmds):
            # 检查游戏是否在进行中
            if game_key in self.game_states:
                self._end_game(game_key, e_context)
            else:
                self._send_text_reply("⚠️ 没有正在进行的游戏", e_context)
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 处理猜测答案 - 支持前缀
        guess_prefixes = cmd_config.get("guess_prefix", ["我猜", "猜"])
        for prefix in guess_prefixes:
            if content.startswith(f"{prefix} "):
                # 提取答案部分
                answer = content[len(prefix)+1:].strip()
                
                # 检查游戏是否在进行中
                if game_key in self.game_states and self.game_states[game_key].get("current_song"):
                    # 检查用户是否在答题
                    if not self.game_states[game_key].get("answered", False):
                        # 尝试匹配答案
                        self._submit_answer(game_key, user_id, user_name, answer, e_context)
                        e_context.action = EventAction.BREAK_PASS
                        return
                else:
                    start_cmd = start_game_cmds[0] if start_game_cmds else "猜歌曲"
                    self._send_text_reply(f"⚠️ 没有正在进行的游戏，请先发送\"{start_cmd} 歌手名\"开始游戏", e_context)
                    e_context.action = EventAction.BREAK_PASS
                    return
        
        # 处理一般答案 - 仅在游戏进行中处理
        if game_key in self.game_states and self.game_states[game_key].get("current_song"):
            # 检查用户是否在答题
            if not self.game_states[game_key].get("answered", False):
                # 尝试匹配答案
                self._submit_answer(game_key, user_id, user_name, content, e_context)
                e_context.action = EventAction.BREAK_PASS
                return
    
    def _start_timer(self, game_key: str, e_context: EventContext):
        """启动计时器，在时间到后自动跳到下一题"""
        # 先停止任何正在运行的定时器
        with self.timer_lock:
            if hasattr(self, 'timer_thread') and self.timer_thread and self.timer_thread.is_alive():
                self.timer_running = False
                logger.debug(f"[CaiGeQu] 停止现有定时器线程")
            
            # 设置新的定时器状态
            self.timer_running = True
            timer_id = f"{time.time()}-{random.randint(1000, 9999)}"
            logger.debug(f"[CaiGeQu] 创建新定时器 ID: {timer_id}, 时限: {self.time_limit}秒")
        
        def timer_callback():
            timer_start_time = time.time()
            logger.debug(f"[CaiGeQu] 定时器 {timer_id} 开始执行, 预计 {self.time_limit}秒后结束")
            try:
                # 检查定时器是否被取消
                if not self.timer_running:
                    logger.debug(f"[CaiGeQu] 定时器 {timer_id} 在等待前已被取消")
                    return
                
                # 等待指定时间
                for _ in range(self.time_limit):
                    time.sleep(1)
                    # 每秒检查一次定时器状态，确保能及时响应取消
                    if not self.timer_running:
                        logger.debug(f"[CaiGeQu] 定时器 {timer_id} 在等待过程中被取消")
                        return
                
                timer_elapsed = time.time() - timer_start_time
                logger.debug(f"[CaiGeQu] 定时器 {timer_id} 等待完成，实际等待 {timer_elapsed:.2f}秒")
                
                # 再次检查定时器状态，避免在等待过程中被取消
                if not self.timer_running:
                    logger.debug(f"[CaiGeQu] 定时器 {timer_id} 在等待后被取消")
                    return
                
                # 检查游戏状态
                if game_key in self.game_states:
                    # 如果题目已经被回答，不执行操作
                    if self.game_states[game_key].get("answered", False):
                        logger.debug(f"[CaiGeQu] 定时器 {timer_id} 终止: 题目已经被回答")
                        return
                    
                    logger.info(f"[CaiGeQu] 定时器 {timer_id} 时间到，处理超时操作")
                    
                    # 使用channel直接发送消息
                    channel = e_context["channel"]
                    current_song = self.game_states[game_key].get("current_song", {})
                    
                    # 发送超时消息
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = (
                        f"⏰ 时间到！\n"
                        f"这首歌是：{current_song.get('title', '未知')} - {current_song.get('singer', '未知')}\n"
                        f"\n发送\"下一题\"继续游戏"
                    )
                    channel.send(reply, e_context["context"])
                    
                    # 发送完整歌曲卡片
                    time.sleep(1)
                    full_song_url = current_song.get("url", "")
                    if full_song_url:
                        # 获取歌曲信息
                        title = current_song.get("title", "未知歌曲")
                        singer = current_song.get("singer", "未知歌手")
                        cover_url = current_song.get("cover", "")
                            
                        # 构造音乐分享卡片XML
                        appmsg = self._construct_music_appmsg(title, singer, full_song_url, cover_url)
                            
                        # 返回APP消息类型
                        reply = Reply()
                        reply.type = ReplyType.APPMSG
                        reply.content = appmsg
                        channel.send(reply, e_context["context"])
                    
                    # 标记为已回答
                    self.game_states[game_key]["answered"] = True
                    self.game_states[game_key]["sent_song_card"] = True  # 标记已发送卡片
                    logger.debug(f"[CaiGeQu] 定时器 {timer_id} 已标记题目为已回答")
                    
                    # 等待指定时间后自动进入下一题
                    logger.debug(f"[CaiGeQu] 定时器 {timer_id} 等待 {self.auto_next_delay}秒后进入下一题")
                    time.sleep(self.auto_next_delay)
                    
                    # 再次检查定时器状态和游戏状态
                    with self.timer_lock:
                        if not self.timer_running:
                            logger.debug(f"[CaiGeQu] 定时器 {timer_id} 在自动下一题前被取消")
                            return
                    
                    if game_key not in self.game_states:
                        logger.debug(f"[CaiGeQu] 定时器 {timer_id} 在自动下一题前游戏已结束")
                        return
                    
                    # 自动进入下一题
                    logger.info(f"[CaiGeQu] 定时器 {timer_id} 自动触发下一题")
                    self._next_question(game_key, e_context)
                else:
                    logger.debug(f"[CaiGeQu] 定时器 {timer_id} 终止: 游戏已不存在")
            except Exception as e:
                logger.error(f"[CaiGeQu] 定时器 {timer_id} 异常：{str(e)}", exc_info=True)
        
        # 标记为未回答状态
        if game_key in self.game_states:
            self.game_states[game_key]["answered"] = False
            logger.debug(f"[CaiGeQu] 游戏 {game_key} 标记为未回答状态")
        
        # 启动定时器线程
        self.timer_thread = threading.Thread(target=timer_callback)
        self.timer_thread.daemon = True
        self.timer_thread.start()
        logger.info(f"[CaiGeQu] 启动定时器 {timer_id}，时限{self.time_limit}秒")
        
        # 保存当前活动的定时器ID到游戏状态
        if game_key in self.game_states:
            self.game_states[game_key]["current_timer_id"] = timer_id
        
        return timer_id
    
    def _get_songs_for_singer(self, singer: str) -> list:
        """获取指定歌手的歌曲列表"""
        try:
            # 检查缓存是否存在且未过期
            if singer in self.song_cache:
                cache_data = self.song_cache[singer]
                if time.time() - cache_data["update_time"] < self.cache_timeout:
                    logger.info(f"[CaiGeQu] 使用缓存的歌手歌曲列表: {singer}")
                    return cache_data["songs"]
            
            # 构建URL - 使用JSON格式返回，更易于解析
            url = f"{self.api_url}?msg={urllib.parse.quote(singer)}&type=json&n="
            
            # 发送请求
            response = requests.get(url, timeout=10)
            
            # 检查响应
            if response.status_code == 200:
                songs = []
                
                try:
                    # 尝试解析为JSON
                    json_data = response.json()
                    
                    # 检查是否是列表格式
                    if isinstance(json_data, list):
                        for i, song in enumerate(json_data):
                            try:
                                # 提取并清理标题，去除平台信息
                                title = song.get("title", "未知歌曲")
                                # 去除[平台]标记
                                if "[" in title and "]" in title:
                                    title = title.split("[")[0].strip()
                                # 去除-后面的信息（如电影主题曲等描述）
                                if "-" in title and not title.startswith("-"):
                                    title = title.split("-")[0].strip()
                                
                                # 只保留纯歌名和歌手
                                songs.append({
                                    "index": i + 1,
                                    "title": title,
                                    "singer": song.get("singer", singer),
                                    "platform": "",
                                    "full_info": f"{title} - {song.get('singer', singer)}"
                                })
                            except Exception as e:
                                logger.error(f"[CaiGeQu] 解析歌曲JSON数据异常: {str(e)}")
                                continue
                    # 检查是否有data字段且为列表
                    elif isinstance(json_data, dict) and "data" in json_data and isinstance(json_data["data"], list):
                        for i, song in enumerate(json_data["data"]):
                            try:
                                # 提取并清理标题，去除平台信息
                                title = song.get("title", "未知歌曲")
                                # 去除[平台]标记
                                if "[" in title and "]" in title:
                                    title = title.split("[")[0].strip()
                                # 去除-后面的信息（如电影主题曲等描述）
                                if "-" in title and not title.startswith("-"):
                                    title = title.split("-")[0].strip()
                                
                                # 只保留纯歌名和歌手
                                songs.append({
                                    "index": i + 1,
                                    "title": title,
                                    "singer": song.get("singer", singer),
                                    "platform": "",
                                    "full_info": f"{title} - {song.get('singer', singer)}"
                                })
                            except Exception as e:
                                logger.error(f"[CaiGeQu] 解析歌曲JSON数据异常: {str(e)}")
                                continue
                    else:
                        # 单首歌曲的JSON格式
                        try:
                            title = json_data.get("title", "") or json_data.get("data", {}).get("title", "未知歌曲")
                            # 去除[平台]标记
                            if "[" in title and "]" in title:
                                title = title.split("[")[0].strip()
                            # 去除-后面的信息（如电影主题曲等描述）
                            if "-" in title and not title.startswith("-"):
                                title = title.split("-")[0].strip()
                                
                            singer_name = json_data.get("singer", "") or json_data.get("data", {}).get("singer", singer)
                            songs.append({
                                "index": 1,
                                "title": title,
                                "singer": singer_name,
                                "platform": "",
                                "full_info": f"{title} - {singer_name}"
                            })
                        except Exception as e:
                            logger.error(f"[CaiGeQu] 解析单首歌曲JSON数据异常: {str(e)}")
                    
                except ValueError:
                    # 如果不是JSON格式，尝试解析文本格式
                    text = response.text.strip()
                    
                    # 按行解析歌曲信息
                    lines = text.split('\n')
                    for line in lines:
                        if ":" in line and "--" in line:
                            parts = line.split(":", 1)
                            if len(parts) != 2:
                                continue
                                
                            try:
                                index = int(parts[0])
                                info_part = parts[1].strip()
                                
                                # 处理 "歌名[平台] -- 歌手" 格式
                                name_platform, singer_name = info_part.split("--", 1)
                                
                                # 处理歌名中的平台信息 "歌名[平台]"
                                title = name_platform.strip()
                                if "[" in title and "]" in title:
                                    title = title.split("[")[0].strip()
                                
                                # 去除-后面的信息（如电影主题曲等描述）
                                if "-" in title and not title.startswith("-"):
                                    title = title.split("-")[0].strip()
                                
                                # 清理歌手名
                                singer_name = singer_name.strip()
                                
                                # 只保留纯歌名和歌手
                                songs.append({
                                    "index": index,
                                    "title": title,
                                    "singer": singer_name,
                                    "platform": "",
                                    "full_info": f"{title} - {singer_name}"
                                })
                            except Exception as e:
                                logger.error(f"[CaiGeQu] 解析歌曲信息异常: {line}, 错误: {str(e)}")
                                continue
                
                # 更新缓存
                self.song_cache[singer] = {
                    "update_time": time.time(),
                    "songs": songs
                }
                
                logger.info(f"[CaiGeQu] 成功获取歌手歌曲列表: {singer}, 共{len(songs)}首歌")
                return songs
            else:
                logger.error(f"[CaiGeQu] 获取歌曲列表失败: HTTP {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"[CaiGeQu] 获取歌手歌曲列表异常: {str(e)}", exc_info=True)
            return []
    
    def _get_song_details(self, singer: str, song_index: int) -> dict:
        """获取指定歌曲的详细信息"""
        try:
            # 构建URL - 使用JSON格式返回
            url = f"{self.api_url}?msg={urllib.parse.quote(singer)}&type=json&n={song_index}"
            
            # 发送请求
            response = requests.get(url, timeout=10)
            
            # 检查响应
            if response.status_code == 200:
                song_info = {}
                
                try:
                    # 尝试解析为JSON
                    json_data = response.json()
                    
                    # 判断返回数据结构
                    if isinstance(json_data, dict):
                        # 直接从JSON中提取数据
                        data = json_data.get("data", json_data)  # 有些API返回包含data字段，有些直接返回数据
                        
                        # 提取歌曲标题并清理
                        title = data.get("title", f"{singer}的歌曲{song_index}")
                        # 去除[平台]标记
                        if "[" in title and "]" in title:
                            title = title.split("[")[0].strip()
                        # 去除-后面的信息（如电影主题曲等描述）
                        if "-" in title and not title.startswith("-"):
                            title = title.split("-")[0].strip()
                        
                        # 提取歌曲详情
                        song_info = {
                            "title": title,
                            "singer": data.get("singer", singer),
                            "cover": data.get("cover", ""),
                            "link": data.get("link", ""),
                            "url": data.get("url", ""),
                            "lyric": data.get("lyric", "")
                        }
                    elif isinstance(json_data, list) and len(json_data) > 0:
                        # 如果返回的是列表，取第一个元素
                        data = json_data[0]
                        
                        # 提取歌曲标题并清理
                        title = data.get("title", f"{singer}的歌曲{song_index}")
                        # 去除[平台]标记
                        if "[" in title and "]" in title:
                            title = title.split("[")[0].strip()
                        # 去除-后面的信息（如电影主题曲等描述）
                        if "-" in title and not title.startswith("-"):
                            title = title.split("-")[0].strip()
                        
                        song_info = {
                            "title": title,
                            "singer": data.get("singer", singer),
                            "cover": data.get("cover", ""),
                            "link": data.get("link", ""),
                            "url": data.get("url", ""),
                            "lyric": data.get("lyric", "")
                        }
                    
                except ValueError:
                    # 如果不是JSON格式，尝试解析文本格式
                    text = response.text.strip()
                    
                    # 检查是否是标准详情格式（包含±img=...±标记）
                    if "±img=" in text:
                        # 处理包含±img=...±标记的格式
                        lines = text.split('\n')
                        
                        # 处理封面图片URL
                        img_line = lines[0] if lines else ""
                        if "±img=" in img_line and "±" in img_line.split("±img=")[1]:
                            img_url = img_line.split("±img=")[1].split("±")[0]
                            song_info["cover"] = img_url
                        
                        # 处理其他信息行
                        for line in lines[1:] if len(lines) > 1 else []:
                            try:
                                if "歌名:" in line or "歌名：" in line:
                                    parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                    title = parts[1].strip() if len(parts) > 1 else ""
                                    # 处理歌名中的平台信息 "歌名[平台]"
                                    if "[" in title and "]" in title:
                                        title = title.split("[")[0].strip()
                                    # 去除-后面的信息（如电影主题曲等描述）
                                    if "-" in title and not title.startswith("-"):
                                        title = title.split("-")[0].strip()
                                    song_info["title"] = title
                                elif "歌手:" in line or "歌手：" in line:
                                    parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                    song_info["singer"] = parts[1].strip() if len(parts) > 1 else ""
                                elif "封面地址:" in line or "封面地址：" in line:
                                    parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                    song_info["cover"] = parts[1].strip() if len(parts) > 1 else ""
                                elif "歌曲详情页:" in line or "歌曲详情页：" in line:
                                    parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                    song_info["link"] = parts[1].strip() if len(parts) > 1 else ""
                                elif "播放链接:" in line or "播放链接：" in line or "歌曲播放链接:" in line or "歌曲播放链接：" in line:
                                    parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                    # 确保获取完整的URL（即使URL中含有冒号）
                                    if len(parts) > 1 and ("http://" in parts[1] or "https://" in parts[1]):
                                        url_parts = parts[1].strip().split("http")
                                        song_info["url"] = "http" + url_parts[-1].strip()
                                    else:
                                        song_info["url"] = parts[1].strip() if len(parts) > 1 else ""
                                elif "歌词:" in line or "歌词：" in line:
                                    parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                    song_info["lyric"] = parts[1].strip() if len(parts) > 1 else ""
                            except Exception as e:
                                logger.error(f"[CaiGeQu] 解析歌曲信息行异常: {line}, 错误: {str(e)}")
                                continue
                    else:
                        # 按行解析歌曲信息
                        lines = text.split('\n')
                        
                        # 检查API响应格式
                        if len(lines) > 0 and ":" in lines[0] and "--" in lines[0]:
                            # 可能是歌曲列表返回格式，从中提取第一首歌
                            try:
                                parts = lines[0].split(":", 1)
                                if len(parts) == 2:
                                    info_part = parts[1].strip()
                                    name_platform, singer_name = info_part.split("--", 1)
                                    
                                    # 处理歌名中的平台信息 "歌名[平台]"
                                    title = name_platform.strip()
                                    if "[" in title and "]" in title:
                                        title = title.split("[")[0].strip()
                                    # 去除-后面的信息（如电影主题曲等描述）
                                    if "-" in title and not title.startswith("-"):
                                        title = title.split("-")[0].strip()
                                        
                                    song_info = {
                                        "title": title,
                                        "singer": singer_name.strip(),
                                        # 使用原始URL作为播放链接，因为详情获取失败
                                        "url": url,
                                        "cover": ""
                                    }
                                    logger.info(f"[CaiGeQu] 使用列表格式解析歌曲: {title}")
                            except Exception as e:
                                logger.error(f"[CaiGeQu] 解析列表格式歌曲信息异常: {str(e)}")
                        else:
                            # 尝试标准详情格式解析
                            for line in lines:
                                try:
                                    if "歌名:" in line or "歌名：" in line:
                                        parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                        title = parts[1].strip() if len(parts) > 1 else ""
                                        # 处理歌名中的平台信息 "歌名[平台]"
                                        if "[" in title and "]" in title:
                                            title = title.split("[")[0].strip()
                                        # 去除-后面的信息（如电影主题曲等描述）
                                        if "-" in title and not title.startswith("-"):
                                            title = title.split("-")[0].strip()
                                        song_info["title"] = title
                                    elif "歌手:" in line or "歌手：" in line:
                                        parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                        song_info["singer"] = parts[1].strip() if len(parts) > 1 else ""
                                    elif "封面地址:" in line or "封面地址：" in line:
                                        parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                        song_info["cover"] = parts[1].strip() if len(parts) > 1 else ""
                                    elif "歌曲详情页:" in line or "歌曲详情页：" in line:
                                        parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                        song_info["link"] = parts[1].strip() if len(parts) > 1 else ""
                                    elif "播放链接:" in line or "播放链接：" in line or "歌曲播放链接:" in line or "歌曲播放链接：" in line:
                                        parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                        # 确保获取完整的URL（即使URL中含有冒号）
                                        if len(parts) > 1 and ("http://" in parts[1] or "https://" in parts[1]):
                                            url_parts = parts[1].strip().split("http")
                                            song_info["url"] = "http" + url_parts[-1].strip()
                                        else:
                                            song_info["url"] = parts[1].strip() if len(parts) > 1 else ""
                                    elif "歌词:" in line or "歌词：" in line:
                                        parts = line.split(":", 1) if ":" in line else line.split("：", 1)
                                        song_info["lyric"] = parts[1].strip() if len(parts) > 1 else ""
                                except Exception as e:
                                    logger.error(f"[CaiGeQu] 解析歌曲信息行异常: {line}, 错误: {str(e)}")
                                    continue
                
                # 如果没有找到歌曲标题和URL，尝试直接从song_index中提取
                if "title" not in song_info or "url" not in song_info:
                    # 使用singer和song_index构建最小信息
                    song_info["title"] = f"{singer}的歌曲{song_index}"
                    song_info["singer"] = singer
                    song_info["url"] = url  # 使用请求URL作为播放链接
                
                logger.info(f"[CaiGeQu] 成功获取歌曲详情: {song_info.get('title', 'unknown')}")
                return song_info
            else:
                logger.error(f"[CaiGeQu] 获取歌曲详情失败: HTTP {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"[CaiGeQu] 获取歌曲详情异常: {str(e)}", exc_info=True)
            return {}
    
    def _verify_url(self, url: str) -> bool:
        """验证URL是否可访问"""
        try:
            if not url or not (url.startswith("http://") or url.startswith("https://")):
                return False
                
            # 发送HEAD请求检查URL是否可访问
            response = requests.head(url, timeout=5, allow_redirects=True)
            
            # 检查状态码
            return response.status_code < 400
        except Exception as e:
            logger.error(f"[CaiGeQu] 验证URL异常: {str(e)}")
            return False
            
    def _download_song(self, song_url: str) -> str:
        """下载歌曲并返回本地文件路径"""
        try:
            # 检查URL是否有效
            if not song_url or not (song_url.startswith("http://") or song_url.startswith("https://")):
                logger.error(f"[CaiGeQu] 无效的歌曲URL: {song_url}")
                return ""
            
            # 先验证URL是否可访问
            if not self._verify_url(song_url):
                logger.error(f"[CaiGeQu] 歌曲URL无法访问: {song_url}")
                return ""
                
            # 获取临时目录
            tmp_dir = TmpDir().path()
            
            # 生成唯一的文件名
            timestamp = int(time.time())
            random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=6))
            song_name = f"song_{timestamp}_{random_str}.mp3"
            song_path = os.path.join(tmp_dir, song_name)
            
            # 发送请求下载歌曲
            response = requests.get(song_url, stream=True, timeout=30)
            
            # 检查响应
            if response.status_code == 200:
                # 检查响应大小，避免下载过大的文件
                content_length = int(response.headers.get('content-length', 0))
                if content_length > 30 * 1024 * 1024:  # 大于30MB
                    logger.error(f"[CaiGeQu] 歌曲文件过大: {content_length/1024/1024:.2f}MB")
                    return ""
                
                # 检查内容类型
                content_type = response.headers.get('content-type', '')
                if content_type and 'audio' not in content_type and 'octet-stream' not in content_type:
                    logger.warning(f"[CaiGeQu] 歌曲URL内容类型可能不是音频: {content_type}")
                    
                # 保存文件
                try:
                    with open(song_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    # 验证文件是否成功保存
                    if os.path.exists(song_path) and os.path.getsize(song_path) > 0:
                        # 验证文件格式是否有效
                        try:
                            # 使用ffmpeg尝试读取文件头信息，判断文件是否可以解码
                            import subprocess
                            result = subprocess.run(
                                ["ffmpeg", "-i", song_path, "-f", "null", "-"], 
                                stderr=subprocess.PIPE,
                                stdout=subprocess.PIPE,
                                timeout=5
                            )
                            # 如果返回码为0，说明文件可以被ffmpeg正常读取
                            if result.returncode == 0:
                                # 获取音频文件时长
                                duration_cmd = subprocess.run(
                                    ["ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", song_path],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    timeout=5
                                )
                                
                                try:
                                    duration = float(duration_cmd.stdout.decode('utf-8').strip())
                                    logger.info(f"[CaiGeQu] 音频文件总时长: {duration:.2f}秒")
                                    
                                    # 使用配置中设置的片段时长
                                    clip_duration = self.clip_duration
                                    logger.info(f"[CaiGeQu] 计划裁剪片段长度: {clip_duration}秒")
                                    
                                    # 如果时长超过clip_duration + 5秒，截取随机片段
                                    if duration > clip_duration + 5:
                                        # 确定裁剪的起始时间 (在前70%的范围内随机选择)
                                        max_start = max(0, duration * 0.7 - clip_duration)
                                        start_time = random.uniform(0, max_start)
                                        
                                        # 确保不会超出总时长
                                        if start_time + clip_duration > duration:
                                            start_time = max(0, duration - clip_duration)
                                        
                                        # 生成裁剪后的文件名
                                        clip_name = f"clip_{timestamp}_{random_str}.mp3"
                                        clip_path = os.path.join(tmp_dir, clip_name)
                                        
                                        # 裁剪音频
                                        logger.info(f"[CaiGeQu] 开始裁剪音频: 从{start_time:.2f}秒开始，长度{clip_duration}秒")
                                        clip_cmd = subprocess.run(
                                            ["ffmpeg", "-i", song_path, "-ss", str(start_time), "-t", str(clip_duration), 
                                             "-c:a", "libmp3lame", "-q:a", "4", clip_path],
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.PIPE,
                                            timeout=10
                                        )
                                        
                                        # 检查裁剪是否成功
                                        if clip_cmd.returncode == 0 and os.path.exists(clip_path):
                                            # 验证裁剪后的文件长度
                                            check_duration_cmd = subprocess.run(
                                                ["ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", clip_path],
                                                stdout=subprocess.PIPE,
                                                stderr=subprocess.PIPE,
                                                timeout=5
                                            )
                                            actual_duration = 0
                                            try:
                                                actual_duration = float(check_duration_cmd.stdout.decode('utf-8').strip())
                                                logger.info(f"[CaiGeQu] 裁剪后实际片段长度: {actual_duration:.2f}秒")
                                            except:
                                                pass
                                                
                                            # 删除原始文件
                                            if os.path.exists(song_path):
                                                os.remove(song_path)
                                            
                                            logger.info(f"[CaiGeQu] 成功截取音频片段: 从{start_time:.2f}秒开始，长度{clip_duration}秒")
                                            return clip_path
                                        else:
                                            # 裁剪失败，返回原始文件
                                            logger.warning(f"[CaiGeQu] 音频裁剪失败，使用原始文件: {clip_cmd.stderr.decode('utf-8', errors='ignore')}")
                                            return song_path
                                    else:
                                        # 时长较短，直接使用原文件
                                        logger.info(f"[CaiGeQu] 音频时长较短 ({duration:.2f}秒)，不进行裁剪")
                                        return song_path
                                    
                                except (ValueError, AttributeError) as e:
                                    logger.error(f"[CaiGeQu] 解析音频时长失败: {str(e)}")
                                    # 解析失败，返回原始文件
                                    return song_path
                                
                                logger.info(f"[CaiGeQu] 成功下载并验证歌曲: {song_path}")
                                return song_path
                            else:
                                error_output = result.stderr.decode('utf-8', errors='ignore')
                                logger.error(f"[CaiGeQu] 下载的音频文件无法被解码: {error_output}")
                                # 尝试删除损坏的文件
                                if os.path.exists(song_path):
                                    os.remove(song_path)
                                return ""
                        except Exception as e:
                            logger.error(f"[CaiGeQu] 验证音频文件异常: {str(e)}", exc_info=True)
                            # 如果验证过程出错，仍然返回文件路径，让系统尝试处理
                            return song_path
                    else:
                        logger.error(f"[CaiGeQu] 歌曲文件保存失败或大小为0")
                        return ""
                except Exception as e:
                    logger.error(f"[CaiGeQu] 保存歌曲文件异常: {str(e)}", exc_info=True)
                    return ""
            else:
                logger.error(f"[CaiGeQu] 下载歌曲失败: HTTP {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"[CaiGeQu] 下载歌曲异常: {str(e)}", exc_info=True)
            return ""
    
    def _reload_config(self):
        """重新加载配置"""
        try:
            if os.path.exists(CONFIG_PATH):
                with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.config = config
                    
                    # 确保所有配置项存在
                    for key, default_value in self.DEFAULT_CONFIG.items():
                        if key not in config:
                            config[key] = default_value
                    
                    # 游戏设置
                    if "game_settings" in config:
                        settings = config["game_settings"]
                        if "time_limit" in settings:
                            self.time_limit = settings["time_limit"]
                        if "auto_next_delay" in settings:
                            self.auto_next_delay = settings["auto_next_delay"]
                        if "correct_answer_delay" in settings:
                            self.correct_answer_delay = settings["correct_answer_delay"]
                        if "clip_duration" in settings:
                            self.clip_duration = settings["clip_duration"]
                        if "use_ai_hints" in settings:
                            self.use_ai_hints = settings["use_ai_hints"]
                    
                    # 命令配置
                    if "commands" in config:
                        self.commands = config["commands"]
                    
                    # 其它配置
                    if "api_url" in config:
                        self.api_url = config["api_url"]
                    if "cache_timeout" in config:
                        self.cache_timeout = config["cache_timeout"]
                    if "questions_per_round" in config:
                        self.questions_per_round = config["questions_per_round"]
                    if "leaderboard_size" in config:
                        self.leaderboard_size = config["leaderboard_size"]
                    if "enable_group_mode" in config:
                        self.enable_group_mode = config["enable_group_mode"]
                    
                    # OpenAI 配置
                    if "openai" in config:
                        openai_config = config["openai"]
                        self.openai_enabled = openai_config.get("enabled", False)
                        self.openai_helper.update_config(
                            api_key=openai_config.get("api_key", ""),
                            model=openai_config.get("model", "gpt-3.5-turbo"),
                            api_base=openai_config.get("api_base", "https://api.openai.com/v1"),
                            timeout=openai_config.get("timeout", 10)
                        )
                    
                    logger.info("[猜歌曲] 配置已加载")
            else:
                self._save_config()
        except Exception as e:
            logger.error(f"[猜歌曲] 加载配置失败: {e}")
            self._save_config()

    def _save_config(self):
        """保存配置"""
        config = {
            "api_url": self.api_url,
            "cache_timeout": self.cache_timeout,
            "questions_per_round": self.questions_per_round,
            "leaderboard_size": self.leaderboard_size,
            "auth_password": self.auth_password,
            "enable_group_mode": self.enable_group_mode,
            "openai": {
                "enabled": self.openai_enabled,
                "api_key": self.openai_helper.api_key,
                "model": self.openai_helper.model,
                "api_base": self.openai_helper.api_base,
                "timeout": self.openai_helper.timeout
            },
            "game_settings": {
                "time_limit": self.time_limit,
                "auto_next_delay": self.auto_next_delay,
                "correct_answer_delay": self.correct_answer_delay,
                "clip_duration": self.clip_duration,
                "use_ai_hints": self.use_ai_hints
            },
            "commands": self.config.get("commands", self.DEFAULT_CONFIG["commands"])
        }
        
        try:
            os.makedirs(os.path.dirname(CONFIG_PATH), exist_ok=True)
            with open(CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            logger.info(f"[CaiGeQu] 配置已保存至 {CONFIG_PATH}")
            return True
        except Exception as e:
            logger.error(f"[CaiGeQu] 保存配置失败: {e}", exc_info=True)
            return False

    def _start_game(self, game_key: str, singer: str, e_context: EventContext):
        """开始猜歌曲游戏"""
        try:
            # 重新加载配置
            self._reload_config()
            
            # 先停止任何正在运行的定时器和清除当前游戏状态
            with self.timer_lock:
                previous_timer_state = self.timer_running
                self.timer_running = False
                
                # 记录并清除旧的定时器ID
                if game_key in self.game_states and "current_timer_id" in self.game_states[game_key]:
                    old_timer_id = self.game_states[game_key].get("current_timer_id", "unknown")
                    logger.debug(f"[CaiGeQu] 开始新游戏: 停止旧定时器 (之前状态: {previous_timer_state}, 定时器ID: {old_timer_id})")
                else:
                    logger.debug(f"[CaiGeQu] 开始新游戏: 停止定时器 (之前状态: {previous_timer_state})")
                
                if game_key in self.game_states:
                    del self.game_states[game_key]
            
            # 加载现有排行榜数据
            self.leaderboard = self._load_leaderboard()
            logger.info(f"[CaiGeQu] 新游戏开始，加载现有排行榜数据")
            
            # 清空当前轮次排行榜
            self.current_round_scores = {}
            logger.info("[CaiGeQu] 已清空当前轮次排行榜")
            
            # 获取歌手的歌曲列表
            songs = self._get_songs_for_singer(singer)
            
            if not songs:
                self._send_text_reply(f"❌ 未找到歌手 {singer} 的歌曲，请更换歌手名称", e_context)
                return
            
            # 随机选择歌曲
            if len(songs) > self.questions_per_round:
                selected_songs = random.sample(songs, self.questions_per_round)
            else:
                selected_songs = songs
                
            # 保存游戏状态
            self.game_states[game_key] = {
                "start_time": time.time(),
                "singer": singer,
                "song_list": selected_songs,
                "current_index": 0,
                "current_song": None,
                "current_round": 1,
                "score": 0,
                "question_start_time": 0,
                "answered": False,
                "hints_used": 0,
                "current_timer_id": None
            }
            
            # 发送游戏开始提示
            channel = e_context["channel"]
            
            start_message = (
                f"欢迎来到猜歌曲游戏！\n"
                f"歌手：{singer}\n"
                f"本轮共{len(selected_songs)}题\n"
                f"⏰ 每题限时{self.time_limit}秒\n"
                f"发送\"提示\"获取提示，发送\"下一题\"跳过当前题目"
            )
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = start_message
            channel.send(reply, e_context["context"])
            
            # 延迟1秒进入第一题
            time.sleep(1)
            
            # 开始第一题
            self._next_question(game_key, e_context)
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 开始游戏异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 游戏启动失败，请稍后重试", e_context)
    
    def _next_question(self, game_key: str, e_context: EventContext):
        """进入下一题"""
        try:
            # 检查游戏状态
            if game_key not in self.game_states:
                logger.debug(f"[CaiGeQu] 无法进入下一题: 游戏 {game_key} 不存在")
                self._send_text_reply("⚠️ 没有正在进行的游戏", e_context)
                return
            
            # 强制停止任何正在运行的定时器
            with self.timer_lock:
                previous_timer_state = self.timer_running
                self.timer_running = False
                logger.debug(f"[CaiGeQu] 进入下一题: 停止定时器 (之前状态: {previous_timer_state}, 定时器ID: {self.game_states[game_key].get('current_timer_id', 'unknown')})")
            
            # 获取游戏状态
            game_state = self.game_states[game_key]
            current_index = game_state["current_index"]
            song_list = game_state["song_list"]
            logger.info(f"[CaiGeQu] 进入下一题: 当前索引 {current_index}, 总题数 {len(song_list)}")
            
            # 清除旧的定时器ID
            if "current_timer_id" in game_state:
                logger.debug(f"[CaiGeQu] 清除旧定时器ID: {game_state['current_timer_id']}")
                game_state.pop("current_timer_id", None)
            
            # 获取上一题的信息(如果有)
            current_song = game_state.get("current_song", {})
            if current_song and not game_state.get("sent_song_card", False) and current_index > 0:
                logger.debug(f"[CaiGeQu] 进入下一题: 发送上一题的正确答案和卡片")
                # 如果有上一题且未发送卡片，则发送正确答案和卡片
                channel = e_context["channel"]
                
                # 发送正确答案
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = (
                    f"🎵 正确答案是：{current_song.get('title', '未知')} - {current_song.get('singer', '未知')}"
                )
                channel.send(reply, e_context["context"])
                
                # 发送完整歌曲卡片
                time.sleep(1)
                full_song_url = current_song.get("url", "")
                if full_song_url:
                    # 获取歌曲信息
                    title = current_song.get("title", "未知歌曲")
                    singer = current_song.get("singer", "未知歌手")
                    cover_url = current_song.get("cover", "")
                        
                    # 构造音乐分享卡片XML
                    appmsg = self._construct_music_appmsg(title, singer, full_song_url, cover_url)
                        
                    # 返回APP消息类型
                    reply = Reply()
                    reply.type = ReplyType.APPMSG
                    reply.content = appmsg
                    channel.send(reply, e_context["context"])
                
                # 标记为已发送卡片，避免重复发送
                self.game_states[game_key]["sent_song_card"] = True
                
                # 延迟1秒再继续
                time.sleep(1)
            
            # 检查是否已经完成所有题目
            if current_index >= len(song_list):
                # 游戏结束
                self._end_game(game_key, e_context)
                return
            
            # 获取当前歌曲信息
            song_info = song_list[current_index]
            singer = game_state["singer"]
            
            # 获取歌曲详情
            song_details = self._get_song_details(singer, song_info["index"])
            
            if not song_details or "url" not in song_details:
                # 跳过这首歌，移动到下一首
                logger.error(f"[CaiGeQu] 获取歌曲详情失败，跳至下一题")
                self.game_states[game_key]["current_index"] += 1
                self._next_question(game_key, e_context)
                return
            
            # 尝试下载歌曲
            song_path = self._download_song(song_details["url"])
            
            # 更新游戏状态
            self.game_states[game_key].update({
                "current_song": song_details,
                "current_song_path": song_path if song_path else "",
                "question_start_time": time.time(),
                "answered": False,
                "hints_used": 0,
                "sent_song_card": False  # 重置卡片发送状态
            })
            
            # 使用channel直接发送消息
            channel = e_context["channel"]
            
            # 发送题目提示
            question_message = (
                f"第{current_index + 1}题（共{len(song_list)}题）\n"
                f"⏰ 倒计时{self.time_limit}秒\n"
                f"请猜出这首歌的名称："
            )
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = question_message
            channel.send(reply, e_context["context"])
            
            # 延迟1秒后发送音频或替代内容
            time.sleep(1)
            
            # 判断是否有可用的音频文件
            if song_path and os.path.exists(song_path):
                # 发送歌曲片段
                try:
                    reply = Reply()
                    reply.type = ReplyType.VOICE
                    reply.content = song_path
                    channel.send(reply, e_context["context"])
                except Exception as e:
                    # 如果音频发送失败，发送替代信息
                    logger.error(f"[CaiGeQu] 发送语音失败: {str(e)}", exc_info=True)
                    self._send_alternative_hint(song_details, channel, e_context)
            else:
                # 没有可用的音频文件，发送替代信息
                logger.warning(f"[CaiGeQu] 没有可用的音频文件，使用替代提示")
                self._send_alternative_hint(song_details, channel, e_context)
            
            # 递增题目索引
            self.game_states[game_key]["current_index"] += 1
            
            # 启动定时器
            self._start_timer(game_key, e_context)
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 进入下一题异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 获取题目失败，请发送\"下一题\"继续或\"结束游戏\"退出", e_context)
            
    def _send_alternative_hint(self, song_details: dict, channel, e_context: EventContext):
        """当音频无法播放时发送替代提示"""
        try:
            title = song_details.get("title", "未知")
            singer = song_details.get("singer", "未知")
            platform = song_details.get("platform", "")
            url = song_details.get("url", "")
            link = song_details.get("link", "")
            
            # 构建替代提示消息
            hint_message = (
                f"🎵 提示：这是{singer}的一首歌\n"
                f"平台：{platform}\n"
            )
            
            # 添加歌曲链接（如果有）
            if link:
                hint_message += f"歌曲链接：{link}\n"
            elif url:
                hint_message += f"歌曲链接：{url}\n"
            
            hint_message += "❗音频播放失败，请根据提示猜测歌曲"
            
            # 发送替代提示消息
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = hint_message
            channel.send(reply, e_context["context"])
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 发送替代提示异常: {str(e)}", exc_info=True)
    
    def _get_hint(self, game_key: str, e_context: EventContext):
        """获取当前歌曲的提示"""
        try:
            # 检查游戏状态
            if game_key not in self.game_states:
                self._send_text_reply("⚠️ 没有正在进行的游戏", e_context)
                return
            
            # 获取游戏状态
            game_state = self.game_states[game_key]
            
            # 检查是否有当前歌曲
            if not game_state.get("current_song"):
                self._send_text_reply("⚠️ 当前没有正在进行的题目", e_context)
                return
            
            # 检查题目是否已经被回答
            if game_state.get("answered", False):
                self._send_text_reply("⚠️ 当前题目已结束，请发送\"下一题\"继续", e_context)
                return
            
            # 获取歌曲信息
            current_song = game_state["current_song"]
            title = current_song.get("title", "")
            singer = current_song.get("singer", "")
            
            # 增加已使用提示计数
            hints_used = game_state.get("hints_used", 0) + 1
            self.game_states[game_key]["hints_used"] = hints_used
            
            # 尝试使用OpenAI生成智能提示
            if self.openai_enabled and self.use_ai_hints and self.openai_helper.is_available():
                logger.info(f"[CaiGeQu] 使用OpenAI生成提示，级别: {hints_used}")
                
                # 获取清理后的歌名（移除平台标记和其他信息）
                clean_title = title
                if "[" in clean_title and "]" in clean_title:
                    clean_title = clean_title.split("[")[0].strip()
                if "-" in clean_title and not clean_title.startswith("-"):
                    clean_title = clean_title.split("-")[0].strip()
                    
                ai_hint = self.openai_helper.get_song_hint(clean_title, singer, hints_used)
                if ai_hint:
                    # 确保提示中不包含完整歌名
                    if clean_title.lower() in ai_hint.lower():
                        # 如果包含歌名，则使用传统提示
                        logger.warning(f"[CaiGeQu] AI提示中包含歌名，切换到传统提示")
                        hint = self._generate_traditional_hint(title, hints_used)
                    else:
                        # 使用AI生成的提示
                        hint_prefix = "🤖 AI提示：" if hints_used == 1 else f"🤖 AI提示 {hints_used}："
                        hint = f"{hint_prefix}{ai_hint}"
                else:
                    # 如果AI生成失败，使用传统提示
                    logger.warning(f"[CaiGeQu] AI提示生成失败，使用传统提示")
                    hint = self._generate_traditional_hint(title, hints_used)
            else:
                # 使用传统提示
                hint = self._generate_traditional_hint(title, hints_used)
            
            # 发送提示
            self._send_text_reply(hint, e_context)
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 获取提示异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 获取提示失败", e_context)
    
    def _generate_traditional_hint(self, title, hints_used):
        """生成传统的提示（不使用AI）"""
        # 确保标题已经格式化（代码已经在_get_song_details方法中处理）
        
        if hints_used == 1:
            # 第一级提示：显示歌曲名称的首字母/拼音首字母
            first_chars = []
            
            for char in title:
                if re.match(r'[\u4e00-\u9fff]', char):  # 中文字符
                    first_chars.append(char[0])
                elif re.match(r'[a-zA-Z]', char):  # 英文字母
                    first_chars.append(char[0].upper())
                else:
                    first_chars.append(char)
            
            # 如果是纯英文单词，可以添加长度提示
            if re.match(r'^[a-zA-Z\s]+$', title):
                words = title.split()
                if len(words) > 1:
                    return f"歌名提示：由{len(words)}个单词组成，首字母分别是{''.join([w[0].upper() for w in words if w])}"
                else:
                    return f"歌名提示：一个单词，长度为{len(title)}个字母，首字母是{title[0].upper()}"
            else:
                return f"歌名提示：{''.join(first_chars)}"
            
        elif hints_used == 2:
            # 第二级提示：显示歌曲名称的一部分
            if len(title) <= 2:
                return f"歌名提示：{title[0]}..."
            elif len(title) <= 4:
                return f"歌名提示：{title[:len(title)//2]}..."
            else:
                # 对于较长的标题，显示前面部分
                reveal_length = max(2, len(title) // 3)
                return f"歌名提示：{title[:reveal_length]}..."
            
        else:
            # 第三级提示：直接给出答案的一大部分
            if len(title) <= 3:
                return f"歌名几乎全部提示：{title[:-1]}..."
            else:
                # 只隐藏最后1-2个字符
                hide_count = min(2, max(1, len(title) // 6))
                return f"歌名几乎全部提示：{title[:len(title)-hide_count]}..."
    
    def _submit_answer(self, game_key: str, user_id: str, user_name: str, answer: str, e_context: EventContext):
        """提交答案"""
        try:
            # 检查游戏状态
            if game_key not in self.game_states:
                logger.debug(f"[CaiGeQu] 无法提交答案: 游戏 {game_key} 不存在")
                return
            
            # 获取游戏状态
            game_state = self.game_states[game_key]
            
            # 检查是否有当前歌曲
            if not game_state.get("current_song"):
                logger.debug(f"[CaiGeQu] 无法提交答案: 当前没有进行中的题目")
                return
            
            # 检查题目是否已经被回答
            if game_state.get("answered", False):
                logger.debug(f"[CaiGeQu] 无法提交答案: 题目已经被回答")
                return
            
            # 获取歌曲信息
            current_song = game_state["current_song"]
            correct_answer = current_song.get("title", "").strip()
            logger.debug(f"[CaiGeQu] 提交答案: 用户 {user_name} 提交 '{answer}', 正确答案 '{correct_answer}'")
            
            # 清理答案，移除所有可能的干扰信息
            # 歌曲已经在_get_song_details中被处理，这里不需要额外处理平台标记
            # 但我们可以进一步清理用户提交的答案
            user_answer = answer.strip().lower()
            correct_answer_lower = correct_answer.lower()
            
            # 计算答题时间
            question_start_time = game_state.get("question_start_time", time.time())
            answer_time = time.time() - question_start_time
            logger.debug(f"[CaiGeQu] 答题用时: {answer_time:.2f}秒")
            
            # 验证答案是否正确
            is_correct = False
            
            # 1. 完全匹配正确答案
            if user_answer == correct_answer_lower:
                is_correct = True
                logger.debug("[CaiGeQu] 答案匹配: 完全匹配原始答案")
            # 2. 用户答案包含在正确答案中，且长度超过正确答案长度的60%
            elif user_answer in correct_answer_lower and len(user_answer) > len(correct_answer_lower) * 0.6:
                is_correct = True
                logger.debug("[CaiGeQu] 答案匹配: 用户答案是正确答案的有效子集")
            # 3. 正确答案包含在用户答案中，且正确答案长度超过用户答案长度的60%
            elif correct_answer_lower in user_answer and len(correct_answer_lower) > len(user_answer) * 0.6:
                is_correct = True
                logger.debug("[CaiGeQu] 答案匹配: 正确答案是用户答案的有效子集")
            # 4. 编辑距离检查
            else:
                try:
                    # 简单的编辑距离计算
                    def levenshtein_distance(a, b):
                        if len(a) < len(b):
                            return levenshtein_distance(b, a)
                        if not b:
                            return len(a)
                        previous_row = range(len(b) + 1)
                        for i, c1 in enumerate(a):
                            current_row = [i + 1]
                            for j, c2 in enumerate(b):
                                insertions = previous_row[j + 1] + 1
                                deletions = current_row[j] + 1
                                substitutions = previous_row[j] + (c1 != c2)
                                current_row.append(min(insertions, deletions, substitutions))
                            previous_row = current_row
                        return previous_row[-1]
                    
                    # 计算编辑距离
                    distance = levenshtein_distance(correct_answer_lower, user_answer)
                    max_allowed_distance = max(2, len(correct_answer_lower) // 4)  # 动态计算允许的编辑距离
                    logger.debug(f"[CaiGeQu] 编辑距离: {distance}, 最大允许距离: {max_allowed_distance}")
                    
                    # 根据答案长度决定容忍的编辑距离
                    if distance <= max_allowed_distance:
                        is_correct = True
                        logger.debug("[CaiGeQu] 答案匹配: 编辑距离在允许范围内")
                except Exception as e:
                    logger.error(f"[CaiGeQu] 计算编辑距离异常: {str(e)}")
            
            # 使用channel直接发送消息
            channel = e_context["channel"]
            
            if is_correct:
                # 停止定时器
                with self.timer_lock:
                    previous_timer_state = self.timer_running
                    self.timer_running = False
                    timer_id = self.game_states[game_key].get('current_timer_id', 'unknown')
                    logger.debug(f"[CaiGeQu] 答案正确: 停止定时器 (之前状态: {previous_timer_state}, 定时器ID: {timer_id})")
                    # 清除定时器ID
                    self.game_states[game_key].pop('current_timer_id', None)
                
                # 固定得分为1分，不考虑提示使用次数和答题时间
                hints_used = game_state.get("hints_used", 0)
                score = 1
                
                # 更新游戏状态
                self.game_states[game_key]["score"] += score
                self.game_states[game_key]["answered"] = True
                self.game_states[game_key]["sent_song_card"] = True  # 标记已发送卡片
                logger.info(f"[CaiGeQu] 用户 {user_name} 答题正确, 得分 +{score}, 总分 {self.game_states[game_key]['score']}")
                
                # 更新排行榜
                self._update_leaderboard(self._normalize_user_id(user_id), user_name, score)
                self._update_current_round_score(self._normalize_user_id(user_id), user_name, score)
                
                # 发送正确回答消息
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = (
                    f"🎉 恭喜 {user_name} 回答正确！\n"
                    f"正确答案：{correct_answer}\n"
                    f"用时：{answer_time:.1f}秒\n"
                    f"使用提示：{hints_used}次\n"
                    f"本题得分：{score}分\n"
                    f"当前总分：{self.game_states[game_key]['score']}分"
                )
                channel.send(reply, e_context["context"])
                
                # 等待一会发送完整歌曲链接
                time.sleep(1)
                
                # 发送完整歌曲链接（改为卡片发送方式）
                reply = Reply()
                full_song_url = current_song.get("url", "")
                if full_song_url:
                    # 获取歌曲信息
                    title = current_song.get("title", "未知歌曲")
                    singer = current_song.get("singer", "未知歌手")
                    cover_url = current_song.get("cover", "")
                    
                    # 构造音乐分享卡片XML
                    appmsg = self._construct_music_appmsg(title, singer, full_song_url, cover_url)
                    
                    # 返回APP消息类型
                    reply.type = ReplyType.APPMSG
                    reply.content = appmsg
                    channel.send(reply, e_context["context"])
                
                # 延迟后自动进入下一题
                logger.debug(f"[CaiGeQu] 延迟 {self.correct_answer_delay}秒后自动进入下一题")
                time.sleep(self.correct_answer_delay)
                
                # 检查游戏状态
                if game_key not in self.game_states:
                    logger.debug("[CaiGeQu] 自动进入下一题取消: 游戏已不存在")
                    return
                
                # 自动进入下一题
                logger.info("[CaiGeQu] 自动进入下一题")
                self._next_question(game_key, e_context)
            else:
                # 添加猜错时的反馈提示
                logger.debug(f"[CaiGeQu] 用户 {user_name} 答题错误: '{answer}'")
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = f"❌ {user_name}，你猜的「{answer}」不对，再想想？"
                channel.send(reply, e_context["context"])
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 提交答案异常: {str(e)}", exc_info=True)
    
    def _end_game(self, game_key: str, e_context: EventContext):
        """结束游戏"""
        try:
            # 检查游戏状态
            if game_key not in self.game_states:
                self._send_text_reply("⚠️ 没有正在进行的游戏", e_context)
                return
            
            # 停止定时器
            with self.timer_lock:
                self.timer_running = False
                logger.debug(f"[CaiGeQu] 结束游戏: 停止定时器 (定时器ID: {self.game_states[game_key].get('current_timer_id', 'unknown')})")
            
            # 获取游戏状态
            game_state = self.game_states[game_key]
            
            # 使用channel直接发送消息
            channel = e_context["channel"]
            
            # 生成游戏结束信息
            end_message = (
                f"🏁 游戏结束！\n"
                f"本轮总分：{game_state.get('score', 0)}分\n\n"
            )
            
            # 如果有人参与，显示本轮排名
            if self.current_round_scores:
                end_message += "🏆 本轮排名：\n"
                # 按分数排序
                sorted_scores = sorted(
                    self.current_round_scores.items(),
                    key=lambda x: x[1]["score"],
                    reverse=True
                )
                
                # 显示前三名或全部（如果不足三名）
                for i, (player_id, player_data) in enumerate(sorted_scores[:3]):
                    end_message += f"{i+1}. {player_data['name']}: {player_data['score']}分\n"
            else:
                end_message += "本轮没有玩家获得分数"
            
            # 发送游戏结束消息
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = end_message
            channel.send(reply, e_context["context"])
            
            # 清理游戏状态
            del self.game_states[game_key]
            
            # 清空当前轮次排行榜
            self.current_round_scores = {}
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 结束游戏异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 游戏结束失败", e_context)
    
    def _send_text_reply(self, content: str, e_context: EventContext):
        """发送文本回复"""
        reply = Reply()
        reply.type = ReplyType.TEXT
        reply.content = content
        e_context["reply"] = reply
    
    def _handle_auth(self, user_id: str, password: str, e_context: EventContext):
        """处理管理员认证"""
        if password == self.auth_password:
            self.admin_users.add(user_id)
            self._send_text_reply("✅ 管理员认证成功", e_context)
            logger.info(f"[CaiGeQu] 用户 {user_id} 成功认证为管理员")
        else:
            self._send_text_reply("❌ 密码错误，认证失败", e_context)
            logger.warning(f"[CaiGeQu] 用户 {user_id} 尝试管理员认证失败")
    
    def _is_admin(self, user_id: str) -> bool:
        """检查用户是否为管理员"""
        return user_id in self.admin_users
    
    def _get_user_name(self, e_context: EventContext) -> str:
        """获取用户名称"""
        user_name = "玩家"  # 默认名称
        
        context_kwargs = e_context["context"].kwargs
        
        # 先尝试从消息对象获取用户名
        msg = context_kwargs.get("msg")
        if msg:
            if hasattr(msg, "actual_user_nickname") and msg.actual_user_nickname:
                user_name = msg.actual_user_nickname
            elif hasattr(msg, "sender_nickname") and msg.sender_nickname:
                user_name = msg.sender_nickname
        
        # 如果没有获取到，尝试从上下文获取
        if user_name == "玩家":
            actual_user_nickname = context_kwargs.get("actual_user_nickname", "")
            if actual_user_nickname:
                user_name = actual_user_nickname
        
        return user_name
    
    def _normalize_user_id(self, user_id):
        """标准化用户ID格式"""
        if user_id:
            # 移除可能的wxid_前缀
            if user_id.startswith("wxid_"):
                return user_id
            
            # 移除@chatroom后缀
            if "@chatroom" in user_id:
                return user_id.split("@chatroom")[0]
            
            # 移除其他后缀
            for suffix in ["@openim", "@stranger"]:
                if suffix in user_id:
                    return user_id.split(suffix)[0]
        
        return user_id
    
    def _load_config_template(self):
        """加载配置模板"""
        return self.DEFAULT_CONFIG
    
    def _load_leaderboard(self):
        """加载排行榜数据"""
        try:
            if os.path.exists(self.leaderboard_file):
                with open(self.leaderboard_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            return {"players": {}}
        except Exception as e:
            logger.error(f"[CaiGeQu] 加载排行榜异常: {str(e)}", exc_info=True)
            return {"players": {}}
    
    def _save_leaderboard(self):
        """保存排行榜数据"""
        try:
            with open(self.leaderboard_file, "w", encoding="utf-8") as f:
                json.dump(self.leaderboard, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"[CaiGeQu] 保存排行榜异常: {str(e)}", exc_info=True)
    
    def _update_leaderboard(self, user_id, user_name, score):
        """更新排行榜数据"""
        try:
            # 确保用户ID有效
            if not user_id:
                return
            
            # 标准化用户ID
            user_id = self._normalize_user_id(user_id)
            
            # 确保players字典存在
            if "players" not in self.leaderboard:
                self.leaderboard["players"] = {}
            
            # 如果玩家不在排行榜中，添加新记录
            if user_id not in self.leaderboard["players"]:
                self.leaderboard["players"][user_id] = {
                    "name": user_name,
                    "total_score": score,
                    "games_played": 1,
                    "last_played": time.time()
                }
            else:
                # 更新现有玩家记录
                player = self.leaderboard["players"][user_id]
                player["total_score"] += score
                player["games_played"] += 1
                player["last_played"] = time.time()
                
                # 如果名字为空，更新名字
                if not player.get("name") and user_name:
                    player["name"] = user_name
            
            # 保存排行榜
            self._save_leaderboard()
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 更新排行榜异常: {str(e)}", exc_info=True)
    
    def _update_current_round_score(self, user_id, user_name, score):
        """更新当前轮次的分数"""
        try:
            # 确保用户ID有效
            if not user_id:
                return
            
            # 标准化用户ID
            user_id = self._normalize_user_id(user_id)
            
            # 如果玩家不在本轮排行榜中，添加新记录
            if user_id not in self.current_round_scores:
                self.current_round_scores[user_id] = {
                    "name": user_name,
                    "score": score
                }
            else:
                # 更新现有玩家分数
                self.current_round_scores[user_id]["score"] += score
                
                # 如果名字为空，更新名字
                if not self.current_round_scores[user_id].get("name") and user_name:
                    self.current_round_scores[user_id]["name"] = user_name
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 更新当前轮次分数异常: {str(e)}", exc_info=True)
    
    def _show_leaderboard(self, e_context: EventContext):
        """显示排行榜"""
        try:
            # 加载最新排行榜数据
            self.leaderboard = self._load_leaderboard()
            
            # 按总分排序
            if "players" not in self.leaderboard:
                self.leaderboard["players"] = {}
            
            sorted_players = sorted(
                self.leaderboard["players"].items(),
                key=lambda x: x[1].get("total_score", 0),
                reverse=True
            )
            
            # 限制显示人数
            display_count = min(self.leaderboard_size, len(sorted_players))
            
            # 构建排行榜消息
            message = "🏆 猜歌曲游戏排行榜 🏆\n\n"
            
            if not sorted_players:
                message += "暂无玩家数据\n"
            else:
                for i, (player_id, player_data) in enumerate(sorted_players[:display_count]):
                    name = player_data.get("name", "玩家")
                    score = player_data.get("total_score", 0)
                    games = player_data.get("games_played", 0)
                    
                    # 添加奖杯图标
                    if i == 0:
                        prefix = "🥇 "
                    elif i == 1:
                        prefix = "🥈 "
                    elif i == 2:
                        prefix = "🥉 "
                    else:
                        prefix = f"{i+1}. "
                    
                    message += f"{prefix}{name}: {score}分 (游戏{games}次)\n"
            
            # 发送排行榜消息
            self._send_text_reply(message, e_context)
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 显示排行榜异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 获取排行榜失败", e_context)
    
    def _reset_leaderboard(self, e_context: EventContext):
        """重置排行榜（管理员命令）"""
        try:
            # 清空排行榜数据
            self.leaderboard = {"players": {}}
            
            # 保存空排行榜
            self._save_leaderboard()
            
            # 发送确认消息
            self._send_text_reply("✅ 排行榜已重置", e_context)
            logger.info("[CaiGeQu] 排行榜已被管理员重置")
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 重置排行榜异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 重置排行榜失败", e_context)
    
    def _clean_leaderboard(self, e_context: EventContext):
        """清理排行榜中的低分或久未活跃用户（管理员命令）"""
        try:
            # 加载最新排行榜数据
            self.leaderboard = self._load_leaderboard()
            
            # 确保players字典存在
            if "players" not in self.leaderboard:
                self.leaderboard["players"] = {}
            
            # 当前时间
            current_time = time.time()
            # 30天的秒数
            inactive_threshold = 30 * 24 * 60 * 60
            
            # 记录清理前的玩家数量
            original_count = len(self.leaderboard["players"])
            
            # 筛选活跃玩家和高分玩家
            active_players = {}
            for player_id, player_data in self.leaderboard["players"].items():
                # 判断条件：分数大于10分或30天内活跃过
                if (player_data.get("total_score", 0) > 10 or 
                    current_time - player_data.get("last_played", 0) < inactive_threshold):
                    active_players[player_id] = player_data
            
            # 更新排行榜
            self.leaderboard["players"] = active_players
            
            # 保存排行榜
            self._save_leaderboard()
            
            # 计算清理的玩家数量
            cleaned_count = original_count - len(active_players)
            
            # 发送确认消息
            self._send_text_reply(f"✅ 排行榜已清理，移除了{cleaned_count}名低分或不活跃玩家", e_context)
            logger.info(f"[CaiGeQu] 排行榜已被管理员清理，移除了{cleaned_count}名玩家")
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 清理排行榜异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 清理排行榜失败", e_context)
    
    def _show_help(self, e_context: EventContext):
        """显示帮助信息"""
        cmd_config = self.config.get("commands", self.DEFAULT_CONFIG["commands"])
        
        # 获取配置中的命令，如果没有则使用默认值
        start_cmd = cmd_config.get("start_game", ["猜歌曲"])[0]
        hint_cmd = cmd_config.get("hint", ["提示"])[0]
        next_cmd = cmd_config.get("next_question", ["下一题"])[0]
        end_cmd = cmd_config.get("end_game", ["结束游戏"])[0]
        leaderboard_cmd = cmd_config.get("leaderboard", ["排行榜"])[0]
        guess_prefix = cmd_config.get("guess_prefix", ["我猜", "猜"])[0]
        
        help_text = (
            f"🎵 猜歌曲游戏帮助 🎵\n\n"
            f"基本命令：\n"
            f"1. {start_cmd} 歌手名 - 开始游戏\n"
            f"2. {hint_cmd} - 获取当前歌曲提示\n"
            f"3. {next_cmd} - 跳过当前题目\n"
            f"4. {end_cmd} - 结束当前游戏\n"
            f"5. {leaderboard_cmd} - 查看历史排行榜\n"
            f"6. {guess_prefix} 歌名 - 提交猜测答案\n\n"
            f"管理员命令：\n"
            f"1. auth 密码 - 认证为管理员\n"
            f"2. {cmd_config.get('reset_leaderboard', ['重置排行榜'])[0]} - 清空排行榜数据\n"
            f"3. {cmd_config.get('clean_leaderboard', ['清理排行榜'])[0]} - 清理不活跃用户\n"
            f"4. {cmd_config.get('settings', ['猜歌曲设置'])[0]} 片段时长=10 - 设置音频片段长度\n\n"
            f"游戏规则：\n"
            f"- 每轮{self.questions_per_round}道题\n"
            f"- 答题时限{self.time_limit}秒\n"
            f"- 音频片段长度{self.clip_duration}秒\n"
            f"- 使用提示不会影响得分\n"
            f"- 猜对歌名得1分，答题时间不影响得分\n"
            f"- AI智能提示：{'已启用' if self.openai_enabled and self.use_ai_hints else '未启用'}"
        )
        
        # 发送帮助信息
        self._send_text_reply(help_text, e_context)
    
    def get_help_text(self, **kwargs):
        """获取插件帮助文本（Plugin基类方法）"""
        verbose = kwargs.get("verbose", False)
        
        # 获取配置中的命令，如果没有则使用默认值
        cmd_config = self.config.get("commands", self.DEFAULT_CONFIG["commands"])
        start_cmd = cmd_config.get("start_game", ["猜歌曲"])[0]
        help_cmd = cmd_config.get("help", ["猜歌曲帮助"])[0]
        
        if verbose:
            # 获取更多命令
            hint_cmd = cmd_config.get("hint", ["提示"])[0]
            next_cmd = cmd_config.get("next_question", ["下一题"])[0]
            end_cmd = cmd_config.get("end_game", ["结束游戏"])[0]
            leaderboard_cmd = cmd_config.get("leaderboard", ["排行榜"])[0]
            guess_prefix = cmd_config.get("guess_prefix", ["我猜", "猜"])[0]
            
            return (
                f"🎵 猜歌曲游戏插件 🎵\n\n"
                f"基本命令：\n"
                f"1. {start_cmd} 歌手名 - 开始游戏\n"
                f"2. {hint_cmd} - 获取当前歌曲提示\n"
                f"3. {next_cmd} - 跳过当前题目\n"
                f"4. {end_cmd} - 结束当前游戏\n"
                f"5. {leaderboard_cmd} - 查看历史排行榜\n"
                f"6. {guess_prefix} 歌名 - 提交猜测答案\n\n"
                f"管理员命令：\n"
                f"1. auth 密码 - 认证为管理员\n"
                f"2. {cmd_config.get('reset_leaderboard', ['重置排行榜'])[0]} - 清空排行榜数据\n"
                f"3. {cmd_config.get('clean_leaderboard', ['清理排行榜'])[0]} - 清理不活跃用户\n"
                f"4. {cmd_config.get('settings', ['猜歌曲设置'])[0]} 片段时长=10 - 设置音频片段长度\n\n"
                f"游戏规则：\n"
                f"- 每轮{self.questions_per_round}道题\n"
                f"- 答题时限{self.time_limit}秒\n"
                f"- 音频片段长度{self.clip_duration}秒\n"
                f"- 使用提示不会影响得分\n"
                f"- 猜对歌名得1分，答题时间不影响得分\n"
                f"- AI智能提示：{'已启用' if self.openai_enabled and self.use_ai_hints else '未启用'}"
            )
        else:
            return f"猜歌曲游戏插件，输入 \"{start_cmd} 歌手名\" 开始游戏，输入 \"{help_cmd}\" 获取详细帮助。"

    def _update_settings(self, settings_cmd: str, user_id: str, e_context: EventContext):
        """更新游戏设置"""
        try:
            # 检查是否为管理员
            if not self._is_admin(user_id):
                self._send_text_reply("⚠️ 只有管理员才能修改设置", e_context)
                return
            
            # 解析设置命令
            # 期望格式: 猜歌曲设置 片段时长=10
            parts = settings_cmd.split("=", 1)
            if len(parts) != 2:
                self._send_text_reply("⚠️ 设置格式错误，正确格式为: 猜歌曲设置 片段时长=10", e_context)
                return
                
            setting_name = parts[0].strip()
            setting_value = parts[1].strip()
            
            # 解析设置值
            try:
                # 目前只支持数字类型的设置
                value = int(setting_value)
            except ValueError:
                self._send_text_reply(f"⚠️ 设置值必须为数字: {setting_value}", e_context)
                return
            
            # 更新对应的设置
            if setting_name == "片段时长":
                # 片段时长不能小于3秒或大于60秒
                if value < 3:
                    self._send_text_reply("⚠️ 片段时长不能小于3秒", e_context)
                    return
                if value > 60:
                    self._send_text_reply("⚠️ 片段时长不能大于60秒", e_context)
                    return
                    
                # 更新配置
                self.clip_duration = value
                self.config["game_settings"]["clip_duration"] = value
                
                # 保存配置到文件
                if self._save_config():
                    self._send_text_reply(f"✅ 片段时长已更新为 {value} 秒", e_context)
                else:
                    self._send_text_reply("❌ 保存配置失败", e_context)
            else:
                self._send_text_reply(f"⚠️ 未知的设置项: {setting_name}", e_context)
                return
                
        except Exception as e:
            logger.error(f"[CaiGeQu] 更新设置异常: {str(e)}", exc_info=True)
            self._send_text_reply("❌ 更新设置失败", e_context)

    # 添加构造音乐分享卡片的方法
    def _construct_music_appmsg(self, title, singer, url, thumb_url=""):
        """
        构造音乐分享卡片的appmsg XML
        :param title: 音乐标题
        :param singer: 歌手名
        :param url: 音乐播放链接
        :param thumb_url: 封面图片URL（可选）
        :return: appmsg XML字符串
        """
        # 处理封面URL
        if thumb_url:
            # 确保URL是以http或https开头的
            if not thumb_url.startswith(("http://", "https://")):
                thumb_url = "https://" + thumb_url.lstrip("/")
            
            # 确保URL没有特殊字符
            thumb_url = thumb_url.replace("&", "&amp;")
        
        # 确保URL没有特殊字符
        url = url.replace("&", "&amp;")
        
        # 使用更简化的XML结构，但保留关键标签
        xml = f"""<appmsg appid="" sdkver="0">
<title>🎵 {title}</title>
<des>{singer}</des>
<action>view</action>
<type>3</type>
<showtype>0</showtype>
<soundtype>0</soundtype>
<mediatagname>音乐</mediatagname>
<messageaction></messageaction>
<content></content>
<contentattr>0</contentattr>
<url>{url}</url>
<lowurl>{url}</lowurl>
<dataurl>{url}</dataurl>
<lowdataurl>{url}</lowdataurl>
<appattach>
    <totallen>0</totallen>
    <attachid></attachid>
    <emoticonmd5></emoticonmd5>
    <fileext></fileext>
    <cdnthumburl>{thumb_url}</cdnthumburl>
    <cdnthumbaeskey></cdnthumbaeskey>
    <aeskey></aeskey>
</appattach>
<extinfo></extinfo>
<sourceusername></sourceusername>
<sourcedisplayname>猜歌曲游戏</sourcedisplayname>
<thumburl>{thumb_url}</thumburl>
<songalbumurl>{thumb_url}</songalbumurl>
<songlyric></songlyric>
</appmsg>"""
        
        # 记录生成的XML，便于调试
        logger.debug(f"[CaiGeQu] 生成的音乐卡片XML: {xml}")
        
        return xml

class OpenAIHelper:
    """OpenAI助手，用于生成智能提示"""
    
    def __init__(self, api_key="", model="gpt-3.5-turbo", timeout=10, api_base="https://api.openai.com/v1"):
        self.api_key = api_key
        self.model = model
        self.timeout = timeout
        self.api_base = api_base
        self.hint_cache = {}  # 缓存已生成的提示
        self.lock = threading.Lock()  # 线程锁，保护缓存访问
    
    def update_config(self, api_key=None, model=None, timeout=None, api_base=None):
        """更新配置参数"""
        if api_key is not None:
            self.api_key = api_key
        if model is not None:
            self.model = model
        if timeout is not None:
            self.timeout = timeout
        if api_base is not None:
            self.api_base = api_base
        # 清空缓存，因为配置变化可能影响生成结果
        with self.lock:
            self.hint_cache = {}
    
    def is_available(self):
        """检查是否可用（是否有API密钥）"""
        return bool(self.api_key)
    
    def get_song_hint(self, song_title, singer, hint_level=1):
        """获取歌曲提示
        
        Args:
            song_title: 歌曲名称
            singer: 歌手名称
            hint_level: 提示级别，1-3，级别越高提示越明显
            
        Returns:
            str: 提示内容
        """
        # 尝试从缓存获取
        cache_key = f"{song_title}_{singer}_{hint_level}"
        with self.lock:
            if cache_key in self.hint_cache:
                return self.hint_cache[cache_key]
            
        try:
            # 构建提示词
            system_prompt = "你是一个音乐知识专家，需要为猜歌游戏提供有趣而不过于明显的提示。"
            
            if hint_level == 1:
                user_prompt = f"""请为歌曲《{song_title}》（歌手：{singer}）提供一个隐晦的提示。
1. 解释歌名的大致含义或背后的故事，但不直接说出歌名
2. 如果歌名是多字词语，可以提示其中一个字的意思，但不要直接给出这个字
3. 描述与歌名相关的一个场景或情感
请确保你的提示有助于玩家思考歌名，但不要过于明显。不要在回答中直接出现歌名的任何字。"""
            elif hint_level == 2:
                user_prompt = f"""请为歌曲《{song_title}》（歌手：{singer}）提供一个中等难度的提示。
1. 明确提示歌名中的一个字（如果是中文歌名），或者一个关键词（如果是英文歌名）
2. 解释歌名整体的含义或与之相关的场景
3. 可以引用一些非标题的歌词片段，这些歌词能暗示歌名
注意不要直接给出完整歌名，但可以明确指出歌名中的一个字或一部分。"""
            else:
                user_prompt = f"""请为歌曲《{song_title}》（歌手：{singer}）提供一个非常明显的提示。
1. 直接给出歌名中的多个字或部分（但不是完整歌名）
2. 明确解释歌名的完整含义
3. 可以使用近义词或形近词来描述歌名中没有直接给出的部分
这是玩家的最后提示，应该让玩家能比较容易猜出歌名，但仍然保留一点挑战性。"""
            
            # 准备请求数据
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 300
            }
            
            # 发送请求
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and result["choices"]:
                    hint = result["choices"][0]["message"]["content"].strip()
                    hint = self._clean_response(hint)
                    
                    # 缓存结果
                    with self.lock:
                        self.hint_cache[cache_key] = hint
                    
                    return hint
            
            logger.warning(f"[CaiGeQu] OpenAI请求失败: {response.status_code} {response.text}")
            return ""
            
        except Exception as e:
            logger.error(f"[CaiGeQu] 获取歌曲提示异常: {str(e)}", exc_info=True)
            return ""
    
    def _clean_response(self, text):
        """清理OpenAI响应，移除引导语等"""
        # 移除常见的引导语
        text = re.sub(r'^(好的|这首歌|关于这首歌|这首)[,.，。:：]?\s*', '', text.strip())
        text = re.sub(r'^["「【《]\s*', '', text.strip())
        text = re.sub(r'\s*["」】》]$', '', text.strip())
        
        return text