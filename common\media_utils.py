# common/media_utils.py
# This file will contain common media processing utility functions.

import os
import shutil # For shutil.which, primarily for non-Windows
from common.log import logger # Assuming logger is from common.log

def find_ffmpeg_path(): # Renamed to be more generic, removed leading underscore
    """Finds the ffmpeg executable path."""
    ffmpeg_cmd = "ffmpeg" # Default command
    if os.name == 'nt': # Windows
        possible_paths = [
            r"D:\ffmpeg-master-latest-win64-gpl-shared\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
            # Spread the list comprehension into possible_paths
            *[os.path.join(p, "ffmpeg.exe") for p in os.environ.get("PATH", "").split(os.pathsep) if p]
        ]
        for path in possible_paths:
            if os.path.exists(path):
                ffmpeg_cmd = path
                logger.debug(f"[MediaUtils] Found ffmpeg at: {ffmpeg_cmd}")
                return ffmpeg_cmd
        logger.warning("[MediaUtils] ffmpeg not found in common Windows paths or PATH, will try system PATH with 'ffmpeg'.")
        return "ffmpeg"
    else: # Linux/macOS
        # import shutil # Already imported at the top
        ffmpeg_path = shutil.which("ffmpeg")
        if ffmpeg_path:
            logger.debug(f"[MediaUtils] Found ffmpeg at: {ffmpeg_path}")
            return ffmpeg_path
        else:
            logger.warning("[MediaUtils] ffmpeg not found using shutil.which. Will try system PATH with 'ffmpeg'.")
            return "ffmpeg"