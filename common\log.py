import logging
import sys
import os
import json
import platform
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器，支持Windows终端"""

    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[92m',    # 绿色
        'INFO': '\033[94m',     # 蓝色
        'WARNING': '\033[93m',  # 黄色
        'ERROR': '\033[91m',    # 红色
        'CRITICAL': '\033[95m', # 紫色
    }

    # 日志级别图标
    ICONS = {
        'DEBUG': '🔍',
        'INFO': '📘',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨',
    }

    # 特殊关键词图标
    KEYWORD_ICONS = {
        '收到': '📨',
        '发送': '📤',
        '连接': '🔌',
        '登录': '🔑',
        '启动': '🚀',
        '初始化': '⚙️',
        '配置': '🔧',
        '插件': '🧩',
        'AI': '🤖',
        'reply': '💬',
        'query': '❓',
        '成功': '✅',
        '失败': '❌',
        '错误': '🚫',
        '警告': '⚠️',
    }

    RESET = '\033[0m'
    BOLD = '\033[1m'
    DIM = '\033[2m'

    def __init__(self, use_color=True, use_icons=True):
        super().__init__()
        self.use_color = use_color and self._supports_color()
        self.use_icons = use_icons

    def _supports_color(self):
        """检查终端是否支持颜色"""
        # Windows 10+ 支持 ANSI 颜色
        if platform.system() == 'Windows':
            try:
                import subprocess
                result = subprocess.run(['ver'], capture_output=True, text=True, shell=True)
                if 'Windows' in result.stdout:
                    # 启用 Windows 终端颜色支持
                    os.system('color')
                    return True
            except:
                pass

        # Unix/Linux 系统通常支持颜色
        return hasattr(sys.stderr, 'isatty') and sys.stderr.isatty()

    def _add_keyword_icons(self, message):
        """为消息添加关键词图标"""
        if not self.use_icons:
            return message

        for keyword, icon in self.KEYWORD_ICONS.items():
            if keyword in message:
                message = message.replace(keyword, f"{icon} {keyword}", 1)
                break
        return message

    def format(self, record):
        # 获取基本信息
        level_name = record.levelname
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        filename = record.filename
        lineno = record.lineno
        message = record.getMessage()

        # 添加关键词图标
        message = self._add_keyword_icons(message)

        # 构建格式化字符串
        if self.use_color:
            color = self.COLORS.get(level_name, '')
            icon = self.ICONS.get(level_name, '📋') if self.use_icons else ''

            formatted = (
                f"{color}{self.BOLD}{icon} [{level_name}]{self.RESET} "
                f"{self.DIM}{timestamp}{self.RESET} "
                f"│ {filename}:{lineno} │ "
                f"{message}"
            )
        else:
            icon = self.ICONS.get(level_name, '') if self.use_icons else ''
            formatted = f"{icon} [{level_name}] {timestamp} │ {filename}:{lineno} │ {message}"

        return formatted


def _reset_logger(log):
    for handler in log.handlers:
        handler.close()
        log.removeHandler(handler)
        del handler
    log.handlers.clear()
    log.propagate = False

    # 读取日志格式配置
    use_color = True
    use_icons = True
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                use_color = config.get("log_use_color", True)
                use_icons = config.get("log_use_icons", True)
    except Exception:
        pass  # 使用默认值

    # 控制台处理器 - 使用彩色格式
    console_handle = logging.StreamHandler(sys.stdout)
    console_handle.setFormatter(ColoredFormatter(use_color=use_color, use_icons=use_icons))

    # 文件处理器 - 使用普通格式（不包含颜色代码）
    file_handle = logging.FileHandler("run.log", encoding="utf-8")
    file_handle.setFormatter(
        logging.Formatter(
            "[%(levelname)s][%(asctime)s][%(filename)s:%(lineno)d] - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
    )

    log.addHandler(file_handle)
    log.addHandler(console_handle)


def _get_logger():
    log = logging.getLogger("log")
    _reset_logger(log)

    # 默认日志级别
    log_level = logging.INFO

    # 尝试从配置文件读取日志级别
    try:
        # 读取配置文件
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                level_str = config.get("log_level", "INFO")

                # 将字符串日志级别转换为logging模块常量
                if level_str == "DEBUG":
                    log_level = logging.DEBUG
                elif level_str == "INFO":
                    log_level = logging.INFO
                elif level_str == "WARNING":
                    log_level = logging.WARNING
                elif level_str == "ERROR":
                    log_level = logging.ERROR
                elif level_str == "CRITICAL":
                    log_level = logging.CRITICAL

                print(f"设置日志级别为: {level_str}")
    except Exception as e:
        print(f"读取日志配置错误，使用默认INFO级别: {e}")

    log.setLevel(log_level)
    return log


# 日志句柄
logger = _get_logger()


# 允许动态设置日志级别的函数
def set_logger_level(level_str):
    """
    动态设置日志级别
    :param level_str: 日志级别字符串 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }

    if level_str in level_map:
        logger.setLevel(level_map[level_str])
        logger.info(f"日志级别已设置为: {level_str}")
    else:
        logger.warning(f"无效的日志级别: {level_str}，可用值: {', '.join(level_map.keys())}")
