import numpy as np
import cv2
import json
import os
import re
import time
import uuid
import base64
import requests
import threading
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from plugins import Plugin, Event, EventAction, EventContext, register
from common.log import logger
from .module.token_manager import TokenManager
from .module.api_client import Api<PERSON><PERSON>
from .module.image_storage import ImageStorage
from .module.image_processor import ImageProcessor
from .module.image_uploader import ImageUploader
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

@register(
    name="Doubao",
    desc="豆包AI绘画插件",
    version="1.1",
    author="lanvent",
    desire_priority=0
)
class DoubaoPlugin(Plugin):
    def __init__(self):
        super().__init__()
        try:
            # 加载配置
            self.config = self._load_config()
            if not self.config:
                raise Exception("Failed to load config")

            # 检查新API必要的配置项
            api_config = self.config.get("api", {})
            new_api_config = api_config.get("new_api", {})
            required_keys = ["cookies", "payload_ms_token", "a_bogus", "user_agent"]
            if api_config.get("use_new_api") and not all(key in new_api_config for key in required_keys):
                 missing_keys = [key for key in required_keys if key not in new_api_config]
                 logger.warning(f"[Doubao] New API enabled but missing config keys: {missing_keys}. Falling back to old API.")
                 self.use_new_api = False
            elif api_config.get("use_new_api"):
                 logger.info("[Doubao] New API configuration found and enabled.")
                 self.use_new_api = True
            else:
                 logger.info("[Doubao] Using old API.")
                 self.use_new_api = False


            # 获取数据保留天数配置
            retention_days = self.config.get("storage", {}).get("retention_days", 7)

            # 初始化存储路径
            storage_dir = os.path.join(os.path.dirname(__file__), "storage")
            if not os.path.exists(storage_dir):
                os.makedirs(storage_dir)

            temp_dir = os.path.join(os.path.dirname(__file__), "temp")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # 初始化各个模块
            self.image_storage = ImageStorage(
                os.path.join(storage_dir, "images.db"),
                retention_days=retention_days
            )

            # 初始化token管理器和API客户端
            # 将新API配置传递给 TokenManager
            self.token_manager = TokenManager(self.config, self.use_new_api)
            self.api_client = ApiClient(self.token_manager, self.use_new_api) # 将 use_new_api 传递给 ApiClient

            # 初始化图片处理器和上传器 (这部分可能也需要看新API是否改变上传逻辑)
            self.image_uploader = ImageUploader(self.config, self.api_client) # ImageUploader 可能需要 ApiClient 来获取必要的认证信息
            self.image_processor = ImageProcessor(temp_dir, self.image_uploader)

            # 从配置文件加载支持的风格列表
            self.styles = self.config.get("styles", [])

            # 初始化会话信息 (这部分可能对新API也适用)
            self.conversation_id = None
            self.section_id = None
            self.reply_id = None # 新API也有reply_id

            # 从数据库恢复上次会话信息 (可能需要根据API调整)
            self._init_conversation_from_storage()

            # 初始化参考图功能相关变量
            self.waiting_for_reference = {}
            self.reference_prompts = {}

            # 初始化区域重绘相关变量
            self.waiting_for_inpaint = {}
            self.inpaint_prompts = {}
            self.inpaint_images = {}

            # 注册事件处理器
            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context

            logger.info(f"[Doubao] plugin initialized (New API: {self.use_new_api}) with {retention_days} days data retention")

            # 心跳检查 (新API可能不需要或需要不同的逻辑)
            self._heartbeat_thread = None
            self._stop_heartbeat = False
            if not self.use_new_api: # 仅在旧API时启动心跳
                self._start_heartbeat_thread()

        except Exception as e:
            logger.error(f"[Doubao] Failed to initialize plugin: {e}")
            raise e

    def _load_config(self):
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), "config.json")
            with open(config_path, "r", encoding='utf-8') as f:
                config = json.load(f)
                # 可以在这里添加一些默认值或配置项检查
                config.setdefault("api", {}).setdefault("use_new_api", False)
                config["api"].setdefault("new_api", {})
                config.setdefault("styles", ["动漫", "写实", "插画", "二次元"]) # 示例默认值
                config.setdefault("params", {"default_ratio": "1:1", "ratios": ["1:1", "4:3", "3:4", "16:9", "9:16"]}) # 示例默认值
                config.setdefault("commands", [ # 添加默认命令配置示例
                    {"name": "draw", "aliases": ["豆包", "豆包画"]},
                    {"name": "reference", "aliases": ["参考图", "豆包参考"]},
                    # ... 其他命令默认值
                ])
                return config
        except FileNotFoundError:
             logger.error(f"[Doubao] Config file not found at {config_path}")
             # 创建一个空的默认配置或返回错误
             # return {"api": {"use_new_api": False, "new_api": {}}, ...} # 或者直接返回空字典触发错误
             return {}

        except json.JSONDecodeError as e:
            logger.error(f"[Doubao] Failed to parse config file {config_path}: {e}")
            return {}
        except Exception as e:
            logger.error(f"[Doubao] Failed to load config: {e}")
            return {}

    def get_help_text(self, **kwargs):
        help_text = "豆包AI绘画插件\n"
        help_text += "使用:\n"

        # 获取命令配置
        commands = self.config.get('commands', [])

        # 找到draw命令的别名
        draw_command = "豆包"  # 默认值
        for cmd in commands:
            if cmd.get("name") == "draw" and cmd.get("aliases"):
                draw_command = cmd["aliases"][0]
                break

        help_text += f"{draw_command} : 新建会话\n"
        help_text += f"{draw_command}新建会话 : 强制新建会话\n"
        help_text += f"{draw_command} [提示词] [-风格] [-比例]: 生成图片\n"
        help_text += "支持的风格: " + ", ".join(self.styles) + "\n"
        help_text += "支持的比例: " + ", ".join(self.config.get("params", {}).get("ratios", ["4:3"])) + "\n"
        help_text += "\n图片操作指令:\n"

        # 找到各个命令的别名
        reference_cmd = "参考图"
        koutu_cmd = "抠图"
        inpaint_cmd = "重绘"
        change_bg_cmd = "豆包换背景"
        change_subj_cmd = "豆包换主体"
        enlarge_cmd = "$u"
        edit_cmd = "$v"
        outpaint_cmd = "$k"
        regenerate_cmd = "$r"

        # 从配置中获取命令别名
        for cmd in commands:
            if cmd.get("name") == "reference" and cmd.get("aliases"):
                reference_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "koutu" and cmd.get("aliases"):
                koutu_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "inpaint" and cmd.get("aliases"):
                inpaint_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "change_background" and cmd.get("aliases"):
                change_bg_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "change_subject" and cmd.get("aliases"):
                change_subj_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "enlarge" and cmd.get("aliases"):
                enlarge_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "edit" and cmd.get("aliases"):
                edit_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "outpaint" and cmd.get("aliases"):
                outpaint_cmd = cmd["aliases"][0]
            elif cmd.get("name") == "regenerate" and cmd.get("aliases"):
                regenerate_cmd = cmd["aliases"][0]

        help_text += f"{reference_cmd} [提示词] [-风格] [-比例]: 使用参考图生成图片\n"
        help_text += f"{koutu_cmd}: 抠出图片主体\n"
        help_text += f"{inpaint_cmd} [描述词]: 区域重绘图片\n"
        help_text += f"{change_bg_cmd} [背景描述词]: 自动识别主体并替换背景\n"
        help_text += f"{change_subj_cmd} [主体描述词]: 自动识别主体并替换主体\n"
        help_text += "\n图片编辑指令:\n"
        help_text += f"放大: {enlarge_cmd} 图片ID 序号(1-4)\n"
        help_text += f"编辑: {edit_cmd} 图片ID 序号(1-4) 编辑提示词  # 首次编辑需要序号\n"
        help_text += f"     {edit_cmd} 图片ID 编辑提示词  # 二次编辑直接编辑\n"
        help_text += f"扩图: {outpaint_cmd} 图片ID 序号(1-4) 比例(1:1/4:3/16:9/9:16/max)  # 首次扩图需要序号\n"
        help_text += f"     {outpaint_cmd} 图片ID 比例  # 二次扩图直接扩图\n"
        help_text += f"重新生成: {regenerate_cmd} 图片ID\n"
        return help_text

    def _create_new_conversation(self):
        """创建新的图像生成会话 (旧API方法，新API可能直接在生成时创建)"""
        if self.use_new_api:
            logger.info("[Doubao] New API creates conversation during generation. Skipping explicit creation.")
            # 新API在首次请求时自动创建会话，只需确保请求时 conversation_id="0" 和 need_create_conversation=True
            # 可以考虑在这里重置本地的 conversation_id 和 section_id
            self.conversation_id = "0" # 或者 None，让ApiClient处理
            self.section_id = None
            # 更新可用风格可能需要新的API调用或在首次生成后更新
            # self.styles = self.config.get("styles", []) # 暂时用配置值
            return True
        else:
            # ... (保留旧的API逻辑) ...
            try:
                # 构建图像生成会话请求数据
                data = {
                    "skill_type": 3,
                    "condition": {
                        "image_condition": {
                            "category_id": 0
                        }
                    }
                }

                result = self.api_client.send_request(data, "/samantha/skill/pack")
                if result and "data" in result and "image" in result["data"]:
                    # 获取图像生成相关的会话信息
                    self.styles = []
                    if "meta" in result["data"]["image"]:
                        category_list = result["data"]["image"]["meta"].get("category_list", [])
                        self.styles = [category["category_name"] for category in category_list]
                        logger.info(f"[Doubao] Loaded {len(self.styles)} styles from API")

                    # 获取历史会话列表
                    history_data = {
                        "request_list": [{
                            "conversation_id": "0"
                        }]
                    }
                    history_result = self.api_client.send_request(history_data, "/alice/conversation/latest_messagelist")
                    if history_result and "data" in history_result and "message_map" in history_result["data"]:
                        # 找到最新的图像生成会话
                        for conv_id, messages in history_result["data"]["message_map"].items():
                            if messages and len(messages) > 0:
                                # 检查是否是绘画会话类型 (可能需要看API具体字段)
                                # if messages[0].get("skill_type") == 3: # 假设 skill_type=3 是绘画
                                self.conversation_id = conv_id
                                self.section_id = messages[0].get("section_id")
                                logger.info(f"[Doubao] Found existing image conversation: {self.conversation_id}")
                                break
                            # 如果没找到，可能需要真的创建一个新的
                            if not self.conversation_id:
                                 logger.info("[Doubao] No existing image conversation found, will create one on first generation.")


                        return True
                    return False
            except Exception as e:
                logger.error(f"[Doubao] Error creating/finding image conversation (Old API): {e}")
                return False

    def _parse_style_and_ratio(self, content: str, draw_command: str) -> tuple:
        """解析绘画指令中的风格和比例参数
        Args:
            content: 完整的指令内容
            draw_command: 绘画命令前缀
        Returns:
            tuple: (prompt, style, ratio)
        """
        # 移除命令前缀
        prompt = content[len(draw_command):].strip()
        style = None
        ratio = self.config.get("params", {}).get("default_ratio", "4:3")
        supported_ratios = self.config.get("params", {}).get("ratios", ["1:1", "2:3", "4:3", "9:16", "16:9"])

        # 1. 处理自然语言描述格式
        if "图风格为" in prompt:
            parts = prompt.split("图风格为")
            if len(parts) == 2:
                prompt = parts[0].strip()
                style_part = parts[1].strip()
                # 提取风格
                if "「" in style_part and "」" in style_part:
                    style = style_part[style_part.find("「")+1:style_part.find("」")]
                else:
                    for s in self.styles:
                        if style_part.startswith(s):
                            style = s
                            break

        # 2. 处理比例描述
        if "比例" in prompt:
            parts = prompt.split("比例")
            if len(parts) == 2:
                prompt = parts[0].strip()
                ratio_part = parts[1].strip()
                # 提取比例
                if "「" in ratio_part and "」" in ratio_part:
                    ratio = ratio_part[ratio_part.find("「")+1:ratio_part.find("」")]
                else:
                    for r in supported_ratios:
                        if ratio_part.startswith(r):
                            ratio = r
                            break

        # 3. 处理分隔符格式 (-或空格或逗号)
        if not style or not ratio:
            # 将中文冒号替换为英文冒号
            prompt = prompt.replace("：", ":")

            # 分割所有可能的分隔符
            parts = []
            for sep in ["-", " ", ","]:
                if sep in prompt:
                    parts.extend([p.strip() for p in prompt.split(sep) if p.strip()])

            if parts:
                # 最后两个部分可能是风格和比例
                last_parts = parts[-2:]
                prompt = " ".join(parts[:-2]) if len(parts) > 2 else parts[0]

                for part in last_parts:
                    # 检查是否是比例格式
                    if ":" in part and part in supported_ratios:
                        ratio = part
                    # 检查是否是支持的风格
                    elif part in self.styles:
                        style = part

        return prompt.strip(), style, ratio.replace("：", ":")

    def on_handle_context(self, e_context: EventContext):
        if e_context["context"].type != ContextType.TEXT and e_context["context"].type != ContextType.IMAGE:
            return

        try:
            content = e_context["context"].content
            msg = e_context["context"]["msg"]

            # 获取命令配置
            commands = self.config.get('commands', [])

            # 找到draw命令的别名
            draw_commands = ["豆包"]  # 默认值
            for cmd in commands:
                if cmd.get("name") == "draw" and cmd.get("aliases"):
                    draw_commands = cmd["aliases"]
                    break

            # 注意：我们不再使用draw_command变量，而是直接使用draw_commands列表

            # 找到各个命令的别名
            reference_cmds = ["参考图"]
            koutu_cmds = ["抠图"]
            inpaint_cmds = ["重绘", "圈选", "涂抹"]
            change_bg_cmds = ["豆包换背景"]
            change_subj_cmds = ["豆包换主体"]
            enlarge_cmds = ["$u"]
            edit_cmds = ["$v"]
            outpaint_cmds = ["$k"]
            regenerate_cmds = ["$r"]

            # 为了兼容性，保留单个命令变量
            reference_cmd = reference_cmds[0]
            koutu_cmd = koutu_cmds[0]
            change_bg_cmd = change_bg_cmds[0]
            change_subj_cmd = change_subj_cmds[0]
            enlarge_cmd = enlarge_cmds[0]
            edit_cmd = edit_cmds[0]
            outpaint_cmd = outpaint_cmds[0]
            regenerate_cmd = regenerate_cmds[0]

            # 从配置中获取命令别名
            for cmd in commands:
                if cmd.get("name") == "reference" and cmd.get("aliases"):
                    reference_cmds = cmd["aliases"]
                    reference_cmd = reference_cmds[0]
                elif cmd.get("name") == "koutu" and cmd.get("aliases"):
                    koutu_cmds = cmd["aliases"]
                    koutu_cmd = koutu_cmds[0]
                elif cmd.get("name") == "inpaint" and cmd.get("aliases"):
                    inpaint_cmds = cmd["aliases"]
                elif cmd.get("name") == "change_background" and cmd.get("aliases"):
                    change_bg_cmds = cmd["aliases"]
                    change_bg_cmd = change_bg_cmds[0]
                elif cmd.get("name") == "change_subject" and cmd.get("aliases"):
                    change_subj_cmds = cmd["aliases"]
                    change_subj_cmd = change_subj_cmds[0]
                elif cmd.get("name") == "enlarge" and cmd.get("aliases"):
                    enlarge_cmds = cmd["aliases"]
                    enlarge_cmd = enlarge_cmds[0]
                elif cmd.get("name") == "edit" and cmd.get("aliases"):
                    edit_cmds = cmd["aliases"]
                    edit_cmd = edit_cmds[0]
                elif cmd.get("name") == "outpaint" and cmd.get("aliases"):
                    outpaint_cmds = cmd["aliases"]
                    outpaint_cmd = outpaint_cmds[0]
                elif cmd.get("name") == "regenerate" and cmd.get("aliases"):
                    regenerate_cmds = cmd["aliases"]
                    regenerate_cmd = regenerate_cmds[0]

            # 处理换背景命令
            is_change_bg_command = False
            used_change_bg_command = None
            for cmd in change_bg_cmds:
                if content.startswith(cmd):
                    is_change_bg_command = True
                    used_change_bg_command = cmd
                    break

            if is_change_bg_command:
                # 提取背景描述词
                background_prompt = content[len(used_change_bg_command):].strip()
                if not background_prompt:
                    e_context["reply"] = Reply(ReplyType.ERROR, f"请在命令后添加背景描述词，如：{used_change_bg_command} 蓝天白云")
                    e_context.action = EventAction.BREAK_PASS
                    return

                # 记录用户ID和等待状态
                self.waiting_for_reference[msg.from_user_id] = True
                self.reference_prompts[msg.from_user_id] = {
                    "type": "change_background",
                    "prompt": background_prompt
                }
                e_context["reply"] = Reply(ReplyType.TEXT, "请发送需要更换背景的图片")
                e_context.action = EventAction.BREAK_PASS
                return

            # 处理换主体命令
            is_change_subj_command = False
            used_change_subj_command = None
            for cmd in change_subj_cmds:
                if content.startswith(cmd):
                    is_change_subj_command = True
                    used_change_subj_command = cmd
                    break

            if is_change_subj_command:
                # 提取主体描述词
                subject_prompt = content[len(used_change_subj_command):].strip()
                if not subject_prompt:
                    e_context["reply"] = Reply(ReplyType.ERROR, f"请在命令后添加主体描述词，如：{used_change_subj_command} 一个帅气的男人")
                    e_context.action = EventAction.BREAK_PASS
                    return

                # 记录用户ID和等待状态
                self.waiting_for_reference[msg.from_user_id] = True
                self.reference_prompts[msg.from_user_id] = {
                    "type": "change_subject",
                    "prompt": subject_prompt
                }
                e_context["reply"] = Reply(ReplyType.TEXT, "请发送需要更换主体的图片")
                e_context.action = EventAction.BREAK_PASS
                return

            # 处理抠图命令
            is_koutu_command = False
            for cmd in koutu_cmds:
                if content == cmd:
                    is_koutu_command = True
                    break

            if is_koutu_command:
                # 记录用户ID和等待状态
                self.waiting_for_reference[msg.from_user_id] = True
                self.reference_prompts[msg.from_user_id] = {"type": "koutu"}
                e_context["reply"] = Reply(ReplyType.TEXT, "请发送需要抠图的图片")
                e_context.action = EventAction.BREAK_PASS
                return

            # 处理参考图命令
            is_reference_command = False
            used_reference_command = None
            for cmd in reference_cmds:
                if content.startswith(cmd):
                    is_reference_command = True
                    used_reference_command = cmd
                    break

            if is_reference_command:
                # 解析完整命令
                command_text = content[len(used_reference_command):].strip()  # 移除参考图前缀
                if not command_text:
                    e_context["reply"] = Reply(ReplyType.ERROR, f"请在{used_reference_command}后添加提示词，如：{used_reference_command} 加个墨镜 人像摄影 4:3")
                    e_context.action = EventAction.BREAK_PASS
                    return

                # 使用现有的解析方法解析提示词、风格和比例
                prompt, style, ratio = self._parse_style_and_ratio(command_text, "")

                if not prompt:
                    e_context["reply"] = Reply(ReplyType.ERROR, "请提供有效的编辑提示词")
                    e_context.action = EventAction.BREAK_PASS
                    return

                # 记录用户ID和完整参数
                self.waiting_for_reference[msg.from_user_id] = True
                self.reference_prompts[msg.from_user_id] = {
                    "prompt": prompt,
                    "style": style,
                    "ratio": ratio
                }
                e_context["reply"] = Reply(ReplyType.TEXT, "请发送需要编辑的参考图片")
                e_context.action = EventAction.BREAK_PASS
                return

            # 处理参考图片上传
            if e_context["context"].type == ContextType.IMAGE and msg.from_user_id in self.waiting_for_reference:
                try:
                    # 获取图片数据
                    logger.info("[Doubao] 开始获取图片数据...")
                    image_data = self._get_image_data(msg, content)
                    if not image_data:
                        logger.error("[Doubao] 获取图片数据失败")
                        e_context["reply"] = Reply(ReplyType.ERROR, "获取图片数据失败，请重试")
                        return

                    # 根据类型处理图片
                    params = self.reference_prompts.get(msg.from_user_id, {})
                    if params.get("type") == "koutu":
                        # 处理抠图
                        self._process_koutu(image_data, msg, e_context)
                    elif params.get("type") == "change_background":
                        # 处理换背景
                        self._process_change_background(image_data, msg, e_context)
                    elif params.get("type") == "change_subject":
                        # 处理换主体
                        self._process_change_subject(image_data, msg, e_context)
                    else:
                        # 处理参考图
                        self._process_image(image_data, msg, e_context)

                except Exception as e:
                    logger.error(f"[Doubao] 处理图片时发生错误: {e}")
                    e_context["reply"] = Reply(ReplyType.ERROR, f"处理图片时发生错误，请重试")

                finally:
                    # 清除等待状态
                    if msg.from_user_id in self.waiting_for_reference:
                        del self.waiting_for_reference[msg.from_user_id]
                    if msg.from_user_id in self.reference_prompts:
                        del self.reference_prompts[msg.from_user_id]

                e_context.action = EventAction.BREAK_PASS
                return

            # 处理区域重绘命令
            if e_context["context"].type == ContextType.TEXT and any(content.startswith(cmd) for cmd in inpaint_cmds):
                # 提取命令类型和描述词
                used_cmd = None
                for cmd in inpaint_cmds:
                    if content.startswith(cmd):
                        used_cmd = cmd
                        break

                if used_cmd == "圈选":
                    mode = "circle"
                    prompt = content[len(used_cmd):].strip()
                elif used_cmd == "涂抹":
                    mode = "brush"
                    prompt = content[len(used_cmd):].strip()
                else:  # 默认使用圈选模式
                    mode = "circle"
                    prompt = content[len(used_cmd):].strip()

                # 检查是否是反选命令
                is_invert = False
                if prompt.startswith("反选"):
                    is_invert = True
                    prompt = prompt[2:].strip()  # 移除"反选"前缀
                elif "反选" in prompt:
                    is_invert = True
                    prompt = prompt.replace("反选", "").strip()

                if not prompt:
                    if mode == "circle":
                        circle_cmd = next((cmd for cmd in inpaint_cmds if cmd == "圈选"), "圈选")
                        e_context["reply"] = Reply(ReplyType.ERROR, f"请在{circle_cmd}后添加描述词，如：{circle_cmd} 添加墨镜")
                    elif mode == "brush":
                        brush_cmd = next((cmd for cmd in inpaint_cmds if cmd == "涂抹"), "涂抹")
                        e_context["reply"] = Reply(ReplyType.ERROR, f"请在{brush_cmd}后添加描述词，如：{brush_cmd} 添加墨镜")
                    else:
                        inpaint_cmd = inpaint_cmds[0] if inpaint_cmds else "重绘"
                        e_context["reply"] = Reply(ReplyType.ERROR, f"请在{inpaint_cmd}后添加描述词，如：{inpaint_cmd} 添加墨镜")
                    e_context.action = EventAction.BREAK_PASS
                    return

                # 记录用户ID和等待状态
                self.waiting_for_inpaint[msg.from_user_id] = True
                self.inpaint_prompts[msg.from_user_id] = {"prompt": prompt, "mode": mode, "is_invert": is_invert}
                e_context["reply"] = Reply(ReplyType.TEXT, "请发送需要重绘的原图")
                e_context.action = EventAction.BREAK_PASS
                return

            # 处理区域重绘的图片上传
            if e_context["context"].type == ContextType.IMAGE and msg.from_user_id in self.waiting_for_inpaint:
                try:
                    # 获取图片数据
                    image_data = self._get_image_data(msg, content)
                    if not image_data:
                        e_context["reply"] = Reply(ReplyType.ERROR, "获取图片数据失败，请重试")
                        return

                    if msg.from_user_id not in self.inpaint_images:
                        # 第一次上传，保存原图
                        self.inpaint_images[msg.from_user_id] = {"original": image_data}
                        # 根据模式显示不同的提示信息
                        mode = self.inpaint_prompts[msg.from_user_id].get("mode", "circle")
                        if mode == "circle":
                            e_context["reply"] = Reply(ReplyType.TEXT, "请发送在需要重绘区域画圈的图片")
                        else:  # brush mode
                            e_context["reply"] = Reply(ReplyType.TEXT, "请发送用红色涂抹需要重绘区域的图片")
                    else:
                        try:
                            # 第二次上传，处理重绘
                            original_image = self.inpaint_images[msg.from_user_id]["original"]
                            prompt = self.inpaint_prompts[msg.from_user_id]["prompt"]
                            mode = self.inpaint_prompts[msg.from_user_id]["mode"]
                            is_invert = self.inpaint_prompts[msg.from_user_id]["is_invert"]

                            # 处理区域重绘
                            self._process_inpaint(original_image, image_data, prompt, msg, e_context)
                        except Exception as e:
                            logger.error(f"[Doubao] Error processing inpaint image: {e}")
                            e_context["reply"] = Reply(ReplyType.ERROR, "处理图片失败，请重试")
                        finally:
                            # 清理状态
                            if msg.from_user_id in self.waiting_for_inpaint:
                                del self.waiting_for_inpaint[msg.from_user_id]
                            if msg.from_user_id in self.inpaint_prompts:
                                del self.inpaint_prompts[msg.from_user_id]
                            if msg.from_user_id in self.inpaint_images:
                                del self.inpaint_images[msg.from_user_id]

                    e_context.action = EventAction.BREAK_PASS
                    return

                except Exception as e:
                    logger.error(f"[Doubao] Error processing inpaint image: {e}")
                    e_context["reply"] = Reply(ReplyType.ERROR, "处理图片失败，请重试")

                    # 清理状态
                    if msg.from_user_id in self.waiting_for_inpaint:
                        del self.waiting_for_inpaint[msg.from_user_id]
                    if msg.from_user_id in self.inpaint_prompts:
                        del self.inpaint_prompts[msg.from_user_id]

                    e_context.action = EventAction.BREAK_PASS
                    return

            # 处理其他命令
            if e_context["context"].type == ContextType.TEXT:
                # 处理图片操作命令
                if content.startswith(enlarge_cmd) or content.startswith(edit_cmd) or content.startswith(outpaint_cmd) or content.startswith(regenerate_cmd):
                    cmd_parts = content.split()
                    if len(cmd_parts) < 2:
                        e_context["reply"] = Reply(ReplyType.ERROR, "命令格式错误")
                        e_context.action = EventAction.BREAK_PASS
                        return

                    # 获取命令名称
                    full_cmd = cmd_parts[0]

                    # 判断是哪个命令
                    if full_cmd.startswith(enlarge_cmd):
                        cmd = "u"
                    elif full_cmd.startswith(edit_cmd):
                        cmd = "v"
                    elif full_cmd.startswith(outpaint_cmd):
                        cmd = "k"
                    elif full_cmd.startswith(regenerate_cmd):
                        cmd = "r"
                    else:
                        cmd = ""
                        
                    img_id = cmd_parts[1]

                    try:
                        if cmd == "u" and len(cmd_parts) == 3:  # 放大命令
                            index = int(cmd_parts[2])
                            is_valid, error_msg = self.image_storage.validate_image_index(img_id, index)
                            if not is_valid:
                                e_context["reply"] = Reply(ReplyType.ERROR, error_msg)
                            else:
                                image_data = self.image_storage.get_image(img_id)
                                image_url = image_data["urls"][index - 1]
                                e_context["reply"] = Reply(ReplyType.IMAGE_URL, image_url)

                        elif cmd == "v":  # 编辑命令
                            if len(cmd_parts) < 3:
                                e_context["reply"] = Reply(ReplyType.ERROR, f"编辑命令格式：\n首次编辑：{edit_cmd} 图片ID 序号(1-4) 编辑提示词\n二次编辑：{edit_cmd} 图片ID 编辑提示词")
                                e_context.action = EventAction.BREAK_PASS
                                return

                            image_data = self.image_storage.get_image(img_id)
                            if not image_data:
                                e_context["reply"] = Reply(ReplyType.ERROR, "找不到对应的图片ID")
                                e_context.action = EventAction.BREAK_PASS
                                return

                            # 判断是否是首次编辑（原始图片有多张）
                            is_first_edit = len(image_data["urls"]) > 1

                            # 解析编辑参数
                            try:
                                if is_first_edit:
                                    if len(cmd_parts) < 4:
                                        e_context["reply"] = Reply(ReplyType.ERROR, f"首次编辑需要提供序号：{edit_cmd} 图片ID 序号(1-4) 编辑提示词")
                                        e_context.action = EventAction.BREAK_PASS
                                        return

                                    index = int(cmd_parts[2])
                                    edit_prompt = " ".join(cmd_parts[3:])

                                    # 验证序号
                                    is_valid, error_msg = self.image_storage.validate_image_index(img_id, index)
                                    if not is_valid:
                                        e_context["reply"] = Reply(ReplyType.ERROR, error_msg)
                                        e_context.action = EventAction.BREAK_PASS
                                        return
                                else:
                                    index = 1
                                    edit_prompt = " ".join(cmd_parts[2:])

                                # 获取指定序号的图片URL和生成对应的token
                                image_url = image_data["urls"][index - 1]
                                image_token = image_url.split("/")[-1].split("~")[0]

                                description = image_data.get("operation_params", {}).get("description", "")
                                conversation_id = image_data.get("operation_params", {}).get("conversation_id")
                                section_id = image_data.get("operation_params", {}).get("section_id")
                                reply_id = image_data.get("operation_params", {}).get("reply_id")

                                if not all([image_token, image_url, conversation_id, section_id]):
                                    e_context["reply"] = Reply(ReplyType.ERROR, "缺少必要的图片信息，无法编辑")
                                    e_context.action = EventAction.BREAK_PASS
                                    return

                                # 发送等待消息
                                e_context["channel"].send(Reply(ReplyType.INFO, f"正在编辑第 {index} 张图片..."), e_context["context"])

                                # 构建编辑请求
                                data = {
                                    "messages": [{
                                        "content": {
                                            "text": edit_prompt,
                                            "edit_image": {
                                                "edit_image_url": image_url,
                                                "edit_image_token": image_token,
                                                "description": description,
                                                "outline_id": None
                                            }
                                        },
                                        "content_type": 2009,
                                        "attachments": []
                                    }],
                                    "completion_option": {
                                        "is_regen": False,
                                        "with_suggest": False,
                                        "need_create_conversation": False,
                                        "launch_stage": 1,
                                        "is_replace": False,
                                        "is_delete": False,
                                        "message_from": 0,
                                        "event_id": "0"
                                    },
                                    "section_id": section_id,
                                    "conversation_id": conversation_id,
                                    "local_message_id": str(uuid.uuid1())
                                }

                                # 发送重绘请求前记录完整请求参数
                                logger.info(f"[Doubao] 完整请求参数:\n请求URL: /samantha/chat/completion\n请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

                                # 保存请求参数到temp目录
                                temp_dir = os.path.join(os.path.dirname(__file__), "temp")
                                if not os.path.exists(temp_dir):
                                    os.makedirs(temp_dir)
                                request_log_path = os.path.join(temp_dir, "@request_debug.json")
                                with open(request_log_path, "w", encoding="utf-8") as f:
                                    json.dump({
                                        "url": "/samantha/chat/completion",
                                        "headers": self.api_client.headers,
                                        "params": self.api_client._get_params(),
                                        "data": data
                                    }, f, ensure_ascii=False, indent=2)
                                logger.info(f"[Doubao] 已保存请求参数到: {request_log_path}")

                                # 发送编辑请求
                                result = self.api_client.send_request(data, "/samantha/chat/completion")
                                if result and "urls" in result:
                                    # 存储编辑后的图片
                                    new_img_id = str(int(time.time()))
                                    operation_params = {
                                        "prompt": edit_prompt,
                                        "conversation_id": conversation_id,
                                        "section_id": section_id,
                                        "reply_id": result.get("reply_id"),
                                        "original_img_id": img_id,
                                        "original_index": index,
                                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],  # 保存新图片token
                                        "image_url": result["urls"][0],  # 保存新图片url
                                        "description": description,
                                        "data": result.get("data", [])  # 保存完整的图片数据
                                    }

                                    # 存储图片信息
                                    image_info = {
                                        "urls": result["urls"],
                                        "type": "edit",
                                        "operation_params": operation_params,
                                        "parent_id": img_id,
                                        "create_time": int(time.time())
                                    }

                                    # 保存到数据库
                                    self.image_storage.store_image(new_img_id, image_info)

                                    # 发送编辑后的图片
                                    e_context["reply"] = Reply(ReplyType.IMAGE_URL, result["urls"][0])

                                    # 等待图片发送完成后再发送帮助信息
                                    time.sleep(1)  # 等待图片发送完成
                                    help_text = self._get_help_text(new_img_id, False)
                                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                                else:
                                    e_context["reply"] = Reply(ReplyType.ERROR, "图片编辑失败")

                            except ValueError as ve:
                                e_context["reply"] = Reply(ReplyType.ERROR, "图片序号必须是1-4之间的数字")
                            except Exception as e:
                                logger.error(f"[Doubao] Error in edit_image: {e}")
                                e_context["reply"] = Reply(ReplyType.ERROR, "图片编辑失败")

                        elif cmd == "k":  # 扩图命令
                            if len(cmd_parts) < 3:
                                e_context["reply"] = Reply(ReplyType.ERROR, f"扩图命令格式：\n首次扩图：{outpaint_cmd} 图片ID 序号(1-4) 比例\n二次扩图：{outpaint_cmd} 图片ID 比例")
                                e_context.action = EventAction.BREAK_PASS
                                return

                            image_data = self.image_storage.get_image(img_id)
                            if not image_data:
                                e_context["reply"] = Reply(ReplyType.ERROR, "找不到对应的图片ID")
                                e_context.action = EventAction.BREAK_PASS
                                return

                            is_first_outpaint = len(image_data["urls"]) > 1

                            if is_first_outpaint:
                                if len(cmd_parts) < 4:
                                    e_context["reply"] = Reply(ReplyType.ERROR, f"首次扩图需要提供序号：{outpaint_cmd} 图片ID 序号(1-4) 比例")
                                    e_context.action = EventAction.BREAK_PASS
                                    return

                                index = int(cmd_parts[2])
                                ratio = cmd_parts[3].replace("：", ":")
                            else:
                                index = 1
                                ratio = cmd_parts[2].replace("：", ":")

                            is_valid, error_msg = self.image_storage.validate_image_index(img_id, index)
                            if not is_valid:
                                e_context["reply"] = Reply(ReplyType.ERROR, error_msg)
                                e_context.action = EventAction.BREAK_PASS
                                return

                            # 获取要扩图的图片URL和token
                            image_url = image_data["urls"][index - 1]
                            image_token = image_url.split("/")[-1].split("~")[0]
                            description = image_data.get("operation_params", {}).get("description", "")
                            conversation_id = image_data.get("operation_params", {}).get("conversation_id")
                            section_id = image_data.get("operation_params", {}).get("section_id")
                            reply_id = image_data.get("operation_params", {}).get("reply_id")

                            e_context["channel"].send(Reply(ReplyType.INFO, f"正在将第 {index} 张图片扩展为 {ratio} 比例..."), e_context["context"])

                            try:
                                # 获取原始会话信息和图片参数
                                params = image_data.get("operation_params", {})

                                # 根据序号获取对应的图片URL和token
                                image_url = image_data["urls"][index - 1]
                                image_token = image_url.split("/")[-1].split("~")[0]

                                description = params.get("description", "")
                                conversation_id = params.get("conversation_id")
                                section_id = params.get("section_id")
                                reply_id = params.get("reply_id")

                                if not all([image_token, image_url, conversation_id, section_id]):
                                    e_context["reply"] = Reply(ReplyType.ERROR, "缺少必要的图片信息，无法扩展")
                                    e_context.action = EventAction.BREAK_PASS
                                    return

                                # 获取原始图片尺寸
                                original_width = int(params.get("width", 1024))
                                original_height = int(params.get("height", 1024))

                                # 根据比例和原始尺寸计算扩图参数
                                if ratio == "1:1":
                                    # 扩展到正方形
                                    expand_ratio = 0.16666667
                                    top = bottom = left = right = expand_ratio
                                elif ratio == "2:3":
                                    # 扩展到竖向2:3
                                    height_ratio = (original_width * 1.5 - original_height) / original_height
                                    top = bottom = height_ratio / 2
                                    left = right = 0
                                elif ratio == "4:3":
                                    # 扩展到横向4:3
                                    width_ratio = (original_height * 1.33333 - original_width) / original_width
                                    left = right = width_ratio / 2
                                    top = bottom = 0
                                elif ratio == "16:9":
                                    # 扩展到横向16:9
                                    width_ratio = (original_height * 1.77778 - original_width) / original_width
                                    left = right = width_ratio / 2
                                    top = bottom = 0
                                elif ratio == "9:16":
                                    # 扩展到竖向9:16
                                    height_ratio = (original_width * 1.77778 - original_height) / original_height
                                    top = bottom = height_ratio / 2
                                    left = right = 0
                                elif ratio == "max":
                                    # 扩展到最大尺寸
                                    height_ratio = (4096 - original_height) / original_height
                                    width_ratio = (2048 - original_width) / original_width
                                    top = bottom = height_ratio / 2
                                    left = right = width_ratio / 2
                                else:
                                    # 默认4:3比例
                                    width_ratio = (original_height * 1.33333 - original_width) / original_width
                                    left = right = width_ratio / 2
                                    top = bottom = 0

                                # 构建扩图请求
                                data = {
                                    "messages": [{
                                        "content": {
                                            "text": "按新尺寸生成图片",
                                            "edit_image": {
                                                "edit_image_url": image_url,
                                                "edit_image_token": image_token,
                                                "ability": "outpainting",
                                                "description": description,
                                                "outline_id": None,
                                                "top": float(top),
                                                "bottom": float(bottom),
                                                "left": float(left),
                                                "right": float(right),
                                                "is_edit_local_image": False,
                                                "is_edit_local_image_v2": "false"
                                            }
                                        },
                                        "content_type": 2009,
                                        "attachments": []
                                    }],
                                    "completion_option": {
                                        "is_regen": False,
                                        "with_suggest": False,
                                        "need_create_conversation": False,
                                        "launch_stage": 1,
                                        "is_replace": False,
                                        "is_delete": False,
                                        "message_from": 0,
                                        "event_id": "0"
                                    },
                                    "section_id": section_id,
                                    "conversation_id": conversation_id,
                                    "local_message_id": str(uuid.uuid1())
                                }

                                # 发送扩图请求
                                result = self.api_client.send_request(data, "/samantha/chat/completion")
                                if result and "urls" in result:
                                    # 存储扩图后的图片
                                    new_img_id = str(int(time.time()))
                                    operation_params = {
                                        "ratio": ratio,
                                        "conversation_id": conversation_id,
                                        "section_id": section_id,
                                        "reply_id": result.get("reply_id"),
                                        "original_img_id": img_id,
                                        "original_index": index,
                                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],  # 保存新图片token
                                        "image_url": result["urls"][0],  # 保存新图片url
                                        "description": description,
                                        "data": result.get("data", []),  # 保存完整的图片数据
                                        "is_edit_local_image": False,
                                        "is_edit_local_image_v2": "false",
                                        "outpaint_params": {
                                            "top": float(top),
                                            "bottom": float(bottom),
                                            "left": float(left),
                                            "right": float(right)
                                        }
                                    }

                                    # 存储图片信息
                                    image_info = {
                                        "urls": result["urls"],
                                        "type": "outpaint",
                                        "operation_params": operation_params,
                                        "parent_id": img_id,
                                        "create_time": int(time.time())
                                    }

                                    # 保存到数据库
                                    self.image_storage.store_image(new_img_id, image_info)

                                    # 发送扩图后的图片
                                    e_context["reply"] = Reply(ReplyType.IMAGE_URL, result["urls"][0])

                                    # 等待图片发送完成后再发送帮助信息
                                    time.sleep(1)  # 等待图片发送完成
                                    help_text = self._get_help_text(new_img_id, False)
                                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                                else:
                                    e_context["reply"] = Reply(ReplyType.ERROR, "图片扩展失败")

                            except Exception as e:
                                logger.error(f"[Doubao] Error in outpaint_image: {e}")
                                e_context["reply"] = Reply(ReplyType.ERROR, "图片扩展失败")

                        elif cmd == "r":  # 重新生成命令
                            image_data = self.image_storage.get_image(img_id)
                            if not image_data:
                                e_context["reply"] = Reply(ReplyType.ERROR, "找不到对应的图片ID")
                                e_context.action = EventAction.BREAK_PASS
                                return

                            # 发送等待消息
                            e_context["channel"].send(Reply(ReplyType.INFO, "正在重新生成图片..."), e_context["context"])

                            # 获取最新的操作信息
                            operation_params = image_data.get("operation_params", {})
                            operation_type = image_data.get("type", "generate")

                            # 获取最新的reply_id
                            reply_id = operation_params.get("reply_id")
                            if not reply_id:
                                e_context["reply"] = Reply(ReplyType.ERROR, "缺少必要的会话信息，无法重新生成")
                                e_context.action = EventAction.BREAK_PASS
                                return

                            # 重新生成图片
                            success, new_img_id, urls, is_multi = self.regenerate_image(
                                image_data,
                                operation_params.get("conversation_id"),
                                operation_params.get("section_id")
                            )

                            if success and urls:
                                # 根据图片数量决定是否使用拼接
                                if len(urls) > 1:
                                    # 多图模式，使用拼接
                                    image_file = self.image_processor.combine_images(urls)
                                    if image_file:
                                        try:
                                            image_reply = Reply(ReplyType.IMAGE, image_file)
                                            e_context["channel"].send(image_reply, e_context["context"])

                                            # 等待图片发送完成后再发送帮助信息
                                            time.sleep(1)  # 等待图片发送完成
                                            help_text = self._get_help_text(new_img_id, True)
                                            e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                                        finally:
                                            image_file.close()
                                else:
                                    # 单图模式，直接发送
                                    e_context["reply"] = Reply(ReplyType.IMAGE_URL, urls[0])

                                    # 等待图片发送完成后再发送帮助信息
                                    time.sleep(1)  # 等待图片发送完成
                                    help_text = self._get_help_text(new_img_id, False)
                                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                            else:
                                e_context["reply"] = Reply(ReplyType.ERROR, "图片重新生成失败")
                        else:
                            e_context["reply"] = Reply(ReplyType.ERROR, "未知的命令")

                    except ValueError:
                        e_context["reply"] = Reply(ReplyType.ERROR, "图片序号必须是1-4之间的数字")
                    except Exception as e:
                        logger.error(f"[Doubao] Error processing command: {e}")
                        e_context["reply"] = Reply(ReplyType.ERROR, "处理命令失败")

                    e_context.action = EventAction.BREAK_PASS
                    return

                # 处理豆包绘画命令
                is_draw_command = False
                used_draw_command = None

                # 检查是否是绘画命令
                for cmd in draw_commands:
                    if content.startswith(cmd):
                        is_draw_command = True
                        used_draw_command = cmd
                        break

                if is_draw_command:
                    # 处理新建会话命令
                    new_session_cmd = None
                    for cmd in commands:
                        if cmd.get("name") == "new_session" and cmd.get("aliases"):
                            new_session_cmd = cmd["aliases"][0]
                            break

                    if new_session_cmd and content == new_session_cmd:
                        if self._create_new_conversation():
                            style_text = "、".join(self.styles) if self.styles else "暂无可用风格"
                            e_context["reply"] = Reply(ReplyType.INFO, f"已创建新的绘图会话\n支持的风格：{style_text}")
                        else:
                            e_context["reply"] = Reply(ReplyType.ERROR, "创建绘图会话失败")
                        e_context.action = EventAction.BREAK_PASS
                        return

                    # 处理其他豆包命令
                    if content.strip() == used_draw_command and not new_session_cmd:  # 只有当没有单独的新建会话命令时，才将纯豆包命令视为新建会话
                        if self._create_new_conversation():
                            style_text = "、".join(self.styles) if self.styles else "暂无可用风格"
                            e_context["reply"] = Reply(ReplyType.INFO, f"已创建新的绘图会话\n支持的风格：{style_text}")
                        else:
                            e_context["reply"] = Reply(ReplyType.ERROR, "创建绘图会话失败")
                        e_context.action = EventAction.BREAK_PASS
                        return

                    # 解析绘图命令参数
                    prompt, style, ratio = self._parse_style_and_ratio(content, used_draw_command)

                    try:
                        if not prompt:
                            e_context["reply"] = Reply(ReplyType.ERROR, "请在命令后输入绘画提示词")
                            e_context.action = EventAction.BREAK_PASS
                            return

                        if self.use_new_api:
                             # 新API可能不支持风格参数，或者需要不同的方式传递
                             if style:
                                 logger.warning(f"[Doubao] Style parameter '{style}' might be ignored by the new API.")
                                 # 可以选择将风格融入prompt
                                 # prompt = f"{prompt}, style: {style}"
                             # 新API可能不支持比例参数，或者需要不同的方式传递
                             if ratio != self.config.get("params", {}).get("default_ratio", "1:1"): # 假设新API默认1:1
                                 logger.warning(f"[Doubao] Ratio parameter '{ratio}' might be ignored by the new API.")
                             full_prompt = prompt # 新API直接使用prompt? 或者需要特定格式？
                             content_payload = {"text": full_prompt} # 新API body里的content部分

                        else: # 旧API逻辑
                            if style and style not in self.styles:
                                e_context["reply"] = Reply(ReplyType.ERROR, f"不支持的风格: {style}")
                                e_context.action = EventAction.BREAK_PASS
                                return
                            full_prompt = prompt
                            if style and style.strip():
                                full_prompt += f"，图风格为「{style}」"
                            if ratio and ratio.strip():
                                full_prompt += f"，比例「{ratio}」"
                            content_payload = {"text": full_prompt} # 旧API body里的content部分


                        # 发送等待消息
                        e_context["channel"].send(Reply(ReplyType.INFO, "正在生成图片,请稍候..."), e_context["context"])

                        # 构建请求数据
                        local_message_id = str(uuid.uuid4()) # 使用uuid4更标准
                        # local_conversation_id 可能只在新API需要，或者两者都需要唯一的
                        local_conversation_id = f"local_{int(time.time()*1000)}_{uuid.uuid4().hex[:6]}"

                        # 根据是否使用新API，确定conversation_id和need_create_conversation
                        current_conversation_id = self.conversation_id
                        need_create = False
                        if self.use_new_api:
                            if not current_conversation_id or current_conversation_id == "0":
                                current_conversation_id = "0"
                                need_create = True
                        else: # 旧API
                             need_create = not bool(current_conversation_id)
                             if need_create:
                                  current_conversation_id = "0" # 旧API创建时也是传 "0"

                        data = {
                            "messages": [{
                                "content": json.dumps(content_payload, ensure_ascii=False) if self.use_new_api else content_payload, # 新API content需要是JSON字符串
                                "content_type": 2009, # 这个类型新旧API是否一致？
                                "attachments": []
                            }],
                            "completion_option": {
                                "is_regen": False,
                                "with_suggest": False, # 新API这个字段可能不同
                                "need_create_conversation": need_create,
                                "launch_stage": 1,
                                "is_replace": False,
                                "is_delete": False,
                                "message_from": 0,
                                "event_id": "0",
                                # 新API 特有字段? "use_auto_cot": False
                            },
                            "conversation_id": current_conversation_id,
                            "section_id": self.section_id if self.section_id else None, # section_id可能在首次生成时为空
                            "local_message_id": local_message_id,
                        }
                        # 新API需要 local_conversation_id
                        if self.use_new_api:
                            data["local_conversation_id"] = local_conversation_id

                        # 生成图片 (调用ApiClient，它会处理新旧API切换)
                        result = self.api_client.send_request(data) # 不需要传endpoint了，ApiClient内部处理

                        # --- 处理返回结果 (需要重写) ---
                        if result and result.get("success"): # 假设ApiClient成功时返回 {"success": True, ...}
                            # 更新会话ID等信息 (从result中获取)
                            if result.get("conversation_id"):
                                self.conversation_id = result["conversation_id"]
                            if result.get("section_id"):
                                self.section_id = result["section_id"]
                            if result.get("reply_id"): # 新旧API都有reply_id?
                                self.reply_id = result["reply_id"]

                            urls = result.get("urls", [])
                            if not urls:
                                 e_context["reply"] = Reply(ReplyType.ERROR, "图片生成成功但未获取到图片URL")
                                 e_context.action = EventAction.BREAK_PASS
                                 return

                            # 存储图片信息 (需要修改 _store_image_info 以适应新结构)
                            img_id = str(int(time.time()))
                            # 提取新API可能返回的详细信息
                            operation_params = {
                                "prompt": prompt,
                                "style": style if not self.use_new_api else None, # 记录原始请求参数
                                "ratio": ratio if not self.use_new_api else None,
                                "full_prompt": full_prompt, # 记录实际发送的prompt
                                "conversation_id": self.conversation_id,
                                "section_id": self.section_id,
                                "reply_id": self.reply_id,
                                "api_version": "new" if self.use_new_api else "old",
                                # 其他需要保存的信息，例如新API返回的原始creations数据
                                "raw_response_data": result.get("raw_data", []) # 让ApiClient返回原始数据
                            }

                            # 调用更新后的存储函数
                            self._store_image_info(img_id, urls, "generate", operation_params, parent_id=None, raw_api_data=result.get("raw_data"))


                            # 发送图片
                            if len(urls) > 0:
                                # 新API返回的图片数量可能不再是固定的4张
                                is_multi = len(urls) > 1 # 简单判断

                                # 如果是多图，尝试拼接；如果是单图，直接发送URL
                                if is_multi:
                                    image_file = self.image_processor.combine_images(urls)
                                    if image_file:
                                        try:
                                            image_reply = Reply(ReplyType.IMAGE, image_file)
                                            e_context["channel"].send(image_reply, e_context["context"])
                                            time.sleep(1)
                                            help_text = self._get_help_text(img_id, is_multi)
                                            e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                                        finally:
                                            image_file.close()
                                    else:
                                         # 拼接失败，尝试发送第一张图
                                         e_context["channel"].send(Reply(ReplyType.IMAGE_URL, urls[0]), e_context["context"])
                                         time.sleep(1)
                                         help_text = self._get_help_text(img_id, is_multi)
                                         e_context["channel"].send(Reply(ReplyType.INFO, f"图片拼接失败，仅显示第一张。\n{help_text}"), e_context["context"])

                                else: # 单图
                                    e_context["channel"].send(Reply(ReplyType.IMAGE_URL, urls[0]), e_context["context"])
                                    time.sleep(1)
                                    help_text = self._get_help_text(img_id, is_multi)
                                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])

                            else: # 没有URL返回
                                e_context["reply"] = Reply(ReplyType.ERROR, "图片生成成功但未返回图片URL")

                        else: # 请求失败或API返回错误
                             error_msg = result.get("error", "图片生成失败") if result else "图片生成失败"
                             e_context["reply"] = Reply(ReplyType.ERROR, error_msg)

                    except Exception as e:
                        logger.error(f"[Doubao] Error generating image: {e}", exc_info=True) # 添加堆栈信息
                        e_context["reply"] = Reply(ReplyType.ERROR, "图片生成时发生内部错误")

                    finally:
                        self.image_processor.cleanup_temp_files() # 这个清理逻辑保持不变

                    e_context.action = EventAction.BREAK_PASS

            except Exception as e:
                logger.error(f"[Doubao] Error processing command: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "处理命令失败")

            finally:
                self.image_processor.cleanup_temp_files()

            e_context.action = EventAction.BREAK_PASS

        except Exception as e:
            logger.error(f"[Doubao] Error processing command: {e}")
            e_context["reply"] = Reply(ReplyType.ERROR, "处理命令失败")

        finally:
            self.image_processor.cleanup_temp_files()

        e_context.action = EventAction.BREAK_PASS

    def _get_image_data(self, msg, content):
        """获取图片数据"""
        try:
            logger.info(f"[Doubao] 开始处理图片，content类型: {type(content)}")

            # 优先检查msg.content
            if hasattr(msg, 'content'):
                logger.info(f"[Doubao] 检查msg.content: {msg.content}")
                if os.path.isfile(msg.content):
                    logger.info(f"[Doubao] 找到图片文件: {msg.content}")
                    try:
                        with open(msg.content, 'rb') as f:
                            return base64.b64encode(f.read()).decode('utf-8')
                    except Exception as e:
                        logger.error(f"[Doubao] 读取msg.content文件失败: {e}")

            # 如果文件还未下载,先尝试下载
            if hasattr(msg, '_prepare_fn') and not msg._prepared:
                logger.info(f"[Doubao] 开始准备图片文件...")
                msg._prepare_fn()
                msg._prepared = True
                time.sleep(1)  # 等待文件准备完成

                # 下载完成后再次检查msg.content
                if hasattr(msg, 'content') and os.path.isfile(msg.content):
                    logger.info(f"[Doubao] 下载后找到图片文件: {msg.content}")
                    try:
                        with open(msg.content, 'rb') as f:
                            return base64.b64encode(f.read()).decode('utf-8')
                    except Exception as e:
                        logger.error(f"[Doubao] 读取下载的文件失败: {e}")

            # 获取当前工作目录
            cwd = os.getcwd()

            # 尝试的路径列表
            file_paths = [
                content,  # 原始路径
                os.path.abspath(content),  # 绝对路径
                os.path.join(cwd, content),  # 相对于当前目录的路径
                os.path.join(cwd, 'tmp', os.path.basename(content)),  # tmp目录
                os.path.join(cwd, 'plugins', 'doubao', 'tmp', os.path.basename(content)),  # 插件tmp目录
                os.path.join(cwd, 'plugins', 'doubao', 'storage', 'temp', os.path.basename(content)),  # 插件临时目录
            ]

            # 检查每个可能的路径
            for path in file_paths:
                if os.path.isfile(path):
                    logger.info(f"[Doubao] 在路径列表中找到图片文件: {path}")
                    try:
                        with open(path, 'rb') as f:
                            return base64.b64encode(f.read()).decode('utf-8')
                    except Exception as e:
                        logger.error(f"[Doubao] 读取文件失败 {path}: {e}")
                        continue

            # 如果是URL,尝试下载
            if isinstance(content, str) and (content.startswith('http://') or content.startswith('https://')):
                response = requests.get(content, timeout=30)
                if response.status_code == 200:
                    return base64.b64encode(response.content).decode('utf-8')

            # 如果是XML内容，尝试解析
            if isinstance(content, str) and '<?xml' in content:
                try:
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(content)
                    img_element = root.find('.//img')
                    if img_element is not None:
                        logger.info("[Doubao] 成功解析XML，查找图片文件...")
                        # 再次检查msg.content
                        if hasattr(msg, 'content') and os.path.isfile(msg.content):
                            logger.info(f"[Doubao] XML解析后找到图片文件: {msg.content}")
                            try:
                                with open(msg.content, 'rb') as f:
                                    return base64.b64encode(f.read()).decode('utf-8')
                            except Exception as e:
                                logger.error(f"[Doubao] 读取XML相关文件失败: {e}")
                except Exception as e:
                    logger.error(f"[Doubao] 解析XML失败: {e}")

            logger.error(f"[Doubao] 未找到图片文件: {content}")
            return None

        except Exception as e:
            logger.error(f"[Doubao] 获取图片数据失败: {e}")
            return None

    def _process_image(self, image_data, msg, e_context):
        """处理参考图片的上传和编辑"""
        try:
            # 获取完整参数
            params = self.reference_prompts.get(msg.from_user_id)
            if not params:
                logger.error("[Doubao] 未找到编辑参数")
                e_context["reply"] = Reply(ReplyType.ERROR, "未找到编辑参数，请重新发送命令")
                return

            # 发送等待消息
            e_context["channel"].send(Reply(ReplyType.INFO, "正在处理图片..."), e_context["context"])

            # 将 base64 字符串转换为字节
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                logger.error(f"[Doubao] Base64解码失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片数据处理失败，请重试")
                return

            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[Doubao] 图片上传失败: {error_msg}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片上传失败，请重试")
                return

            # 获取图片key和URL
            image_key = result.get('image_key')
            image_url = result.get('file_info', {}).get('main_url')
            if not image_key or not image_url:
                logger.error("[Doubao] 未获取到图片信息")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片处理失败，请重试")
                return

            # 获取提示词、风格和比例
            prompt = params.get("prompt", "")
            style = params.get("style")
            ratio = params.get("ratio")

            # 构建完整的提示词
            full_prompt = prompt
            if style and style.strip():
                full_prompt += f"，图风格为「{style}」"
            if ratio and ratio.strip():
                full_prompt += f"，比例「{ratio}」"

            # 构建参考图请求数据
            content = {
                "text": full_prompt,
                "edit_image": {
                    "edit_image_token": image_key,
                    "edit_image_url": image_url,
                    "description": "",
                    "is_edit_local_image": True,
                    "is_edit_local_image_v2": "true"
                }
            }

            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": not bool(self.conversation_id),
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "conversation_id": self.conversation_id if self.conversation_id else "0",
                "section_id": self.section_id,
                "local_message_id": str(uuid.uuid4()),
                "local_conversation_id": f"local_{int(time.time()*1000)}"
            }

            # 发送请求前记录完整请求参数
            logger.info(f"[Doubao] 完整请求参数:\n请求URL: /samantha/chat/completion\n请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

            # 保存请求参数到temp目录
            temp_dir = os.path.join(os.path.dirname(__file__), "temp")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            request_log_path = os.path.join(temp_dir, "@request_debug.json")
            with open(request_log_path, "w", encoding="utf-8") as f:
                json.dump({
                    "url": "/samantha/chat/completion",
                    "headers": self.api_client.headers,
                    "params": self.api_client._get_params(),
                    "data": data
                }, f, ensure_ascii=False, indent=2)
            logger.info(f"[Doubao] 已保存请求参数到: {request_log_path}")

            # 发送参考图请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            if result and "urls" in result:
                # 更新会话信息
                if result.get("conversation_id"):
                    self.conversation_id = result["conversation_id"]
                if result.get("section_id"):
                    self.section_id = result["section_id"]

                # 存储生成的图片
                img_id = str(int(time.time()))
                first_url = result["urls"][0]
                image_token = first_url.split("/")[-1].split("~")[0]

                operation_params = {
                    "prompt": full_prompt,
                    "style": style,
                    "ratio": ratio,
                    "conversation_id": self.conversation_id,
                    "section_id": self.section_id,
                    "reply_id": result.get("reply_id"),
                    "image_token": image_token,
                    "image_url": first_url,
                    "original_key": image_key,
                    "original_url": image_url,
                    "data": result.get("data", [])
                }

                # 存储图片信息
                image_info = {
                    "urls": result["urls"],
                    "type": "reference",
                    "operation_params": operation_params,
                    "parent_id": None,
                    "create_time": int(time.time())
                }

                # 保存到数据库
                self.image_storage.store_image(img_id, image_info)

                # 发送生成的图片
                if len(result["urls"]) > 1:
                    # 多图模式，使用拼接
                    image_file = self.image_processor.combine_images(result["urls"])
                    if image_file:
                        try:
                            image_reply = Reply(ReplyType.IMAGE, image_file)
                            e_context["channel"].send(image_reply, e_context["context"])

                            # 等待图片发送完成后再发送帮助信息
                            time.sleep(1)  # 等待图片发送完成
                            help_text = self._get_help_text(img_id, True)
                            e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                        finally:
                            image_file.close()
                else:
                    # 单图模式，直接发送
                    e_context["channel"].send(Reply(ReplyType.IMAGE_URL, first_url), e_context["context"])
                    time.sleep(1)  # 等待图片发送完成
                    help_text = self._get_help_text(img_id, False)
                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
            else:
                logger.error(f"[Doubao] 参考图生成失败，API响应: {result}")
                e_context["reply"] = Reply(ReplyType.ERROR, "参考图生成失败，请重试")

        except Exception as e:
            logger.error(f"[Doubao] 处理参考图失败: {e}")
            e_context["reply"] = Reply(ReplyType.ERROR, "处理参考图失败，请重试")
            return

    def _process_koutu(self, image_data, msg, e_context):
        """处理抠图功能"""
        try:
            # 发送等待消息
            e_context["channel"].send(Reply(ReplyType.INFO, "正在处理图片..."), e_context["context"])

            # 将 base64 字符串转换为字节
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                logger.error(f"[Doubao] Base64解码失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片数据处理失败，请重试")
                return

            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)

            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[Doubao] 图片上传失败: {error_msg}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片上传失败，请重试")
                return

            # 获取图片key
            image_key = result.get('image_key')
            if not image_key:
                logger.error("[Doubao] 未获取到图片key")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片处理失败，请重试")
                return

            # 获取图片URL和背景蒙版
            file_info = result.get('file_info', {})
            main_url = file_info.get('main_url')
            mask_url = file_info.get('mask_url')

            if not main_url or not mask_url:
                logger.error("[Doubao] 获取图片URL失败")
                e_context["reply"] = Reply(ReplyType.ERROR, "获取图片URL失败，请重试")
                return

            # 存储抠图结果
            img_id = str(int(time.time()))
            operation_params = {
                "image_key": image_key,
                "conversation_id": self.conversation_id,
                "section_id": self.section_id,
                "image_token": image_key.split("/")[-1].split(".")[0],
                "image_url": main_url,
                "original_url": main_url,
                "mask": result.get('mask', ''),
                "without_background": result.get('without_background', False),
                "mask_url": mask_url
            }

            # 存储图片信息
            image_info = {
                "urls": [main_url, mask_url],
                "type": "koutu",
                "operation_params": operation_params,
                "parent_id": None,
                "create_time": int(time.time())
            }

            # 保存到数据库
            try:
                self.image_storage.store_image(img_id, image_info)
            except Exception as e:
                logger.error(f"[Doubao] 保存图片信息失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "保存图片信息失败，请重试")
                return

            # 发送抠图结果
            e_context["reply"] = Reply(ReplyType.IMAGE_URL, mask_url)

            # 发送原图链接信息
            e_context["channel"].send(Reply(ReplyType.TEXT, f"原图链接：{main_url}"), e_context["context"])

            # 等待图片发送完成后再发送帮助信息
            time.sleep(1)  # 等待图片发送完成
            help_text = self._get_help_text(img_id, False)
            e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])

        except Exception as e:
            logger.error(f"[Doubao] 处理抠图失败: {e}")
            e_context["reply"] = Reply(ReplyType.ERROR, "处理抠图失败，请重试")
            return

    def regenerate_image(self, image_data: dict, conversation_id: str, section_id: str):
        """重新生成图片
        Args:
            image_data: 图片数据
            conversation_id: 会话ID
            section_id: 会话分段ID
        Returns:
            tuple: (success, new_img_id, urls, is_multi_images)
        """
        try:
            # 获取最新的操作信息
            operation_params = image_data.get("operation_params", {})
            operation_type = image_data.get("type", "generate")

            # 优先从数据库中获取图片信息
            reply_id = operation_params.get("reply_id")
            prompt = operation_params.get("prompt", "")

            if not reply_id:
                logger.error("[Doubao] Missing reply_id for regeneration")
                return False, None, None, False

            # 构建重新生成请求的基础数据
            content = {
                "text": prompt or "按新尺寸生成图片"
            }

            # 根据不同的操作类型构建不同的请求内容
            if operation_type == "outpaint":
                # 扩图操作的重新生成
                content["edit_image"] = {
                    "edit_image_token": operation_params.get("image_token"),
                    "edit_image_url": operation_params.get("image_url"),
                    "outline_id": None,
                    "description": operation_params.get("description", ""),
                    "is_edit_local_image": False,
                    "is_edit_local_image_v2": "false"
                }
            elif operation_type == "edit":
                # 编辑操作的重新生成
                content["edit_image"] = {
                    "edit_image_token": operation_params.get("image_token"),
                    "edit_image_url": operation_params.get("image_url"),
                    "outline_id": None,
                    "description": operation_params.get("description", "")
                }

            # 构建请求数据
            data = {
                "messages": [{
                    "content": content,
                    "content_type": 2009,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": True,
                    "with_suggest": True,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": str(uuid.uuid1()),
                "reply_id": reply_id
            }

            # 发送重新生成请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            if result and "urls" in result:
                # 存储图片信息
                new_img_id = str(int(time.time()))
                new_operation_params = operation_params.copy()
                new_operation_params.update({
                    "conversation_id": conversation_id,
                    "section_id": section_id,
                    "reply_id": result.get("reply_id"),
                    "prompt": prompt
                })

                # 获取新生成图片的token和url
                if result.get("urls") and len(result["urls"]) > 0:
                    first_url = result["urls"][0]
                    new_operation_params["image_token"] = first_url.split("/")[-1].split("~")[0]
                    new_operation_params["image_url"] = first_url

                # 构建图片信息
                image_info = {
                    "urls": result["urls"][:1] if operation_type in ["outpaint", "edit"] else result["urls"],  # 对于扩图和编辑操作只保留第一张图片
                    "type": operation_type,
                    "operation_params": new_operation_params,
                    "parent_id": image_data.get("id"),
                    "create_time": int(time.time())
                }

                # 保存到数据库
                self.image_storage.store_image(new_img_id, image_info)

                # 根据操作类型和返回的图片数量判断是否是多图模式
                # 只有初始生成和普通重新生成时才可能是多图模式
                is_multi_images = operation_type == "generate" and len(result["urls"]) > 1

                # 对于扩图和编辑操作，只返回第一张图片
                urls_to_return = result["urls"][:1] if operation_type in ["outpaint", "edit"] else result["urls"]

                return True, new_img_id, urls_to_return, is_multi_images
            return False, None, None, False

        except Exception as e:
            logger.error(f"[Doubao] Error in regenerate_image: {e}")
            return False, None, None, False

    def _store_image_info(self, img_id: str, urls: list, operation_type: str, operation_params: dict, parent_id: str = None, raw_api_data: list = None):
        """存储图片信息到数据库 (适配新API)
        Args:
            img_id: 图片ID
            urls: 图片URL列表 (从ApiClient提取好的)
            operation_type: 操作类型(generate/edit/outpaint/inpaint/reference/koutu/change_background/change_subject)
            operation_params: 操作参数 (包含原始请求信息和会话ID等)
            parent_id: 父图片ID
            raw_api_data: API返回的原始图像数据列表 (例如新API的creations列表)
        """
        try:
            # 更新会话信息 (已在调用方更新，这里可以再次确认)
            self.conversation_id = operation_params.get("conversation_id", self.conversation_id)
            self.section_id = operation_params.get("section_id", self.section_id)
            self.reply_id = operation_params.get("reply_id", self.reply_id)

            image_info = {
                "urls": urls,
                "type": operation_type,
                "operation_params": operation_params, # 直接保存传入的参数字典
                "parent_id": parent_id,
                "create_time": int(time.time())
            }

            # 尝试从原始API数据中提取更详细的信息 (特别是新API)
            if self.use_new_api and raw_api_data and isinstance(raw_api_data, list) and len(raw_api_data) > 0:
                 # 假设 raw_api_data 是解析后的 creations 列表
                 # 保存第一张图的 key, description, width, height 等
                 first_image_data = raw_api_data[0].get("image", {})
                 if first_image_data:
                      image_info["operation_params"]["image_key"] = first_image_data.get("key")
                      image_info["operation_params"]["description"] = first_image_data.get("description", "图片") # description可能在image层级
                      # 提取尺寸 (新API有多个尺寸字段)
                      if first_image_data.get("image_raw"):
                           image_info["operation_params"]["width"] = first_image_data["image_raw"].get("width")
                           image_info["operation_params"]["height"] = first_image_data["image_raw"].get("height")
                      elif first_image_data.get("image_ori"):
                           image_info["operation_params"]["width"] = first_image_data["image_ori"].get("width")
                           image_info["operation_params"]["height"] = first_image_data["image_ori"].get("height")
                      # 保存所有原始数据以备将来使用
                      image_info["operation_params"]["raw_api_images_data"] = raw_api_data

            elif not self.use_new_api and operation_params.get("data"): # 旧API数据结构
                 # 从旧 API 的 data 字段提取信息
                 raw_data_list = operation_params.get("data", [])
                 if raw_data_list and isinstance(raw_data_list, list) and len(raw_data_list) > 0:
                      first_img_meta = raw_data_list[0]
                      if isinstance(first_img_meta, dict):
                            # 旧API的尺寸信息 (需要确认旧API返回结构)
                            # image_info["operation_params"]["width"] = first_img_meta.get(...)
                            # image_info["operation_params"]["height"] = first_img_meta.get(...)
                            image_info["operation_params"]["description"] = first_img_meta.get("description")
                            # 旧API token可能在URL里，但也可能在data里
                            image_info["operation_params"]["image_token"] = first_img_meta.get("image_token") # 假设字段名

            # 确保 image_token 和 image_url 存在 (从 URL 提取作为后备)
            if not image_info["operation_params"].get("image_token") and urls:
                 try:
                      image_info["operation_params"]["image_token"] = urls[0].split("/")[-1].split("~")[0].split(".")[0] # 尝试更通用的提取
                 except:
                      pass # 提取失败就算了
            if not image_info["operation_params"].get("image_url") and urls:
                 image_info["operation_params"]["image_url"] = urls[0]


            # 对于编辑/扩图操作, 继承父级信息 (逻辑保持)
            if operation_type in ["edit", "outpaint"] and parent_id:
                parent_image = self.image_storage.get_image(parent_id)
                if parent_image:
                    parent_params = parent_image.get("operation_params", {})
                    prefix = "original_" if operation_type == "edit" else "pre_outpaint_"

                    # 只在子操作参数中不存在时才继承
                    image_info["operation_params"].setdefault(f"{prefix}image_token", parent_params.get("image_token"))
                    image_info["operation_params"].setdefault(f"{prefix}image_url", parent_params.get("image_url"))
                    image_info["operation_params"].setdefault(f"{prefix}reply_id", parent_params.get("reply_id"))
                    image_info["operation_params"].setdefault(f"{prefix}description", parent_params.get("description"))
                    image_info["operation_params"].setdefault(f"{prefix}width", parent_params.get("width"))
                    image_info["operation_params"].setdefault(f"{prefix}height", parent_params.get("height"))

                    # 如果当前操作没有尺寸信息，使用父级尺寸
                    image_info["operation_params"].setdefault("width", parent_params.get("width"))
                    image_info["operation_params"].setdefault("height", parent_params.get("height"))


            # 保存到数据库
            self.image_storage.store_image(img_id, image_info)

            logger.debug(f"[Doubao] Stored image info ({operation_type}): ID={img_id}, Params={image_info['operation_params']}")

        except Exception as e:
            logger.error(f"[Doubao] Error storing image info: {e}", exc_info=True)
            # 不向上抛出异常，避免影响流程

    def _init_conversation_from_storage(self):
        '''从数据库初始化会话信息'''
        try:
            # 从数据库获取最新的一条记录
            latest_image = self.image_storage.get_latest_image()
            if latest_image:
                params = latest_image.get("operation_params", {})
                self.conversation_id = params.get("conversation_id")
                self.section_id = params.get("section_id")
                self.reply_id = params.get("reply_id")
                logger.info(f"[Doubao] 已从历史记录恢复会话信息: conversation_id={self.conversation_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"[Doubao] 从数据库恢复会话信息失败: {e}")
            return False

    def _get_help_text(self, img_id, is_multi=False):
        """获取帮助文本 (保持不变，或根据新API功能调整)"""
        help_text = (
            f"图片ID: {img_id}\n"
            "操作指令:\n"
        )

        # 获取命令别名
        enlarge_cmd = self.config.get("commands", [{}])[5].get("aliases", ["$u"])[0] if len(self.config.get("commands", [])) > 5 else "$u"
        edit_cmd = self.config.get("commands", [{}])[6].get("aliases", ["$v"])[0] if len(self.config.get("commands", [])) > 6 else "$v"
        outpaint_cmd = self.config.get("commands", [{}])[7].get("aliases", ["$k"])[0] if len(self.config.get("commands", [])) > 7 else "$k"
        regenerate_cmd = self.config.get("commands", [{}])[8].get("aliases", ["$r"])[0] if len(self.config.get("commands", [])) > 8 else "$r"


        if is_multi:
            help_text += (
                f"放大: {enlarge_cmd} {img_id} 序号(1-{len(self.image_storage.get_image(img_id)['urls']) if self.image_storage.get_image(img_id) else 'N'})\n" # 动态显示序号范围
                f"编辑: {edit_cmd} {img_id} 序号(1-{len(self.image_storage.get_image(img_id)['urls']) if self.image_storage.get_image(img_id) else 'N'}) 编辑提示词\n"
                f"扩图: {outpaint_cmd} {img_id} 序号(1-{len(self.image_storage.get_image(img_id)['urls']) if self.image_storage.get_image(img_id) else 'N'}) 比例(1:1/4:3/16:9/9:16/max)\n" # TODO: 比例参数是否对新API有效?
            )
        else:
            # 单图模式下，编辑和扩图理论上不需要序号，但为了兼容旧命令或简化逻辑，可以保留 img_id
            help_text += (
                f"编辑: {edit_cmd} {img_id} 编辑提示词\n"
                f"扩图: {outpaint_cmd} {img_id} 比例(1:1/4:3/16:9/9:16/max)\n" # TODO: 比例参数是否对新API有效?
            )

        help_text += f"重新生成: {regenerate_cmd} {img_id}"
        return help_text

    def _process_inpaint(self, original_image_data, mask_image_data, prompt, msg, e_context):
        """处理区域重绘
        Args:
            original_image_data: base64编码的原图数据
            mask_image_data: base64编码的标记图片数据
            prompt: 重绘描述词
            msg: 消息对象
            e_context: 事件上下文
        """
        try:
            # 发送等待消息
            e_context["channel"].send(Reply(ReplyType.INFO, "正在处理图片..."), e_context["context"])

            # 将base64数据转换为字节
            try:
                original_image_bytes = base64.b64decode(original_image_data)
                mask_image_bytes = base64.b64decode(mask_image_data)
            except Exception as e:
                logger.error(f"[Doubao] Base64解码失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片数据处理失败，请重试")
                return

            # 上传原图到服务器
            result = self.image_uploader.upload_and_process_image(original_image_bytes)
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[Doubao] 图片上传失败: {error_msg}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片上传失败，请重试")
                return

            # 获取图片key和URL
            image_key = result.get('image_key')
            image_url = result.get('file_info', {}).get('main_url')
            if not image_key or not image_url:
                logger.error("[Doubao] 未获取到图片信息")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片处理失败，请重试")
                return

            # 获取用户的重绘参数
            inpaint_params = self.inpaint_prompts.get(msg.from_user_id, {})
            mode = inpaint_params.get("mode", "circle")  # 默认使用圈选模式
            is_invert = inpaint_params.get("is_invert", False)  # 获取是否反选

            try:
                # 根据模式选择蒙版生成方法
                if mode == "circle":
                    mask_base64 = self.image_processor.create_mask_from_circle_selection(
                        original_image_bytes,
                        mask_image_bytes,
                        invert=is_invert
                    )
                else:  # brush mode
                    mask_base64 = self.image_processor.create_mask_from_marked_image(
                        original_image_bytes,
                        mask_image_bytes,
                        invert=is_invert
                    )

                if not mask_base64:
                    logger.error("[Doubao] 创建蒙版失败")
                    e_context["reply"] = Reply(ReplyType.ERROR, "创建蒙版失败，请重试")
                    return

                # 构建重绘请求数据
                content = {
                    "text": prompt,
                    "edit_image": {
                        "edit_image_token": image_key,
                        "edit_image_url": image_url,
                        "description": "",
                        "ability": "inpainting",
                        "mask": mask_base64,
                        "is_edit_local_image": True,
                        "is_edit_local_image_v2": "true"
                    }
                }

                data = {
                    "messages": [{
                        "content": json.dumps(content, ensure_ascii=False),
                        "content_type": 2009,
                        "attachments": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,
                        "need_create_conversation": not bool(self.conversation_id),
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "event_id": "0"
                    },
                    "conversation_id": self.conversation_id if self.conversation_id else "0",
                    "section_id": self.section_id,
                    "local_message_id": str(uuid.uuid4()),
                    "local_conversation_id": f"local_{int(time.time()*1000)}"
                }

                # 发送请求前记录完整请求参数
                logger.info(f"[Doubao] 完整请求参数:\n请求URL: /samantha/chat/completion\n请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

                # 保存请求参数到temp目录
                temp_dir = os.path.join(os.path.dirname(__file__), "temp")
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                request_log_path = os.path.join(temp_dir, "@request_debug.json")
                with open(request_log_path, "w", encoding="utf-8") as f:
                    json.dump({
                        "url": "/samantha/chat/completion",
                        "headers": self.api_client.headers,
                        "params": self.api_client._get_params(),
                        "data": data
                    }, f, ensure_ascii=False, indent=2)
                logger.info(f"[Doubao] 已保存请求参数到: {request_log_path}")

                # 发送重绘请求
                result = self.api_client.send_request(data, "/samantha/chat/completion")
                if result and "urls" in result:
                    # 更新会话信息
                    if result.get("conversation_id"):
                        self.conversation_id = result["conversation_id"]
                    if result.get("section_id"):
                        self.section_id = result["section_id"]

                    # 存储重绘后的图片
                    img_id = str(int(time.time()))
                    first_url = result["urls"][0]
                    image_token = first_url.split("/")[-1].split("~")[0]

                    operation_params = {
                        "prompt": prompt,
                        "conversation_id": self.conversation_id,
                        "section_id": self.section_id,
                        "reply_id": result.get("reply_id"),
                        "image_token": image_token,
                        "image_url": first_url,
                        "original_key": image_key,
                        "original_url": image_url,
                        "mask": mask_base64,
                        "mode": mode,  # 记录使用的模式
                        "is_invert": is_invert,  # 记录是否使用反选
                        "data": result.get("data", [])
                    }

                    # 存储图片信息
                    image_info = {
                        "urls": result["urls"],
                        "type": "inpaint",
                        "operation_params": operation_params,
                        "parent_id": None,
                        "create_time": int(time.time())
                    }

                    # 保存到数据库
                    self.image_storage.store_image(img_id, image_info)

                    # 发送重绘后的图片
                    e_context["channel"].send(Reply(ReplyType.IMAGE_URL, first_url), e_context["context"])
                    time.sleep(1)  # 等待图片发送完成
                    help_text = self._get_help_text(img_id, False)
                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                else:
                    logger.error(f"[Doubao] 区域重绘失败，API响应: {result}")
                    e_context["reply"] = Reply(ReplyType.ERROR, "区域重绘失败，请重试")

            except Exception as e:
                logger.error(f"[Doubao] 处理失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, f"处理失败: {str(e)}")
                return

        except Exception as e:
            logger.error(f"[Doubao] 处理区域重绘失败: {e}")
            e_context["reply"] = Reply(ReplyType.ERROR, "处理区域重绘失败，请重试")
            return

    def _process_change_background(self, image_data, msg, e_context):
        """处理换背景功能"""
        try:
            # 发送等待消息
            e_context["channel"].send(Reply(ReplyType.INFO, "正在处理图片..."), e_context["context"])

            # 将 base64 字符串转换为字节
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                logger.error(f"[Doubao] Base64解码失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片数据处理失败，请重试")
                return

            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[Doubao] 图片上传失败: {error_msg}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片上传失败，请重试")
                return

            # 获取图片key和URL
            image_key = result.get('image_key')
            image_url = result.get('file_info', {}).get('main_url')
            if not image_key or not image_url:
                logger.error("[Doubao] 未获取到图片信息")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片处理失败，请重试")
                return

            # 获取主体蒙版
            mask_base64 = result.get('mask')
            if not mask_base64:
                logger.error("[Doubao] 获取主体蒙版失败")
                e_context["reply"] = Reply(ReplyType.ERROR, "获取主体蒙版失败，请重试")
                return

            # 获取背景描述词
            params = self.reference_prompts.get(msg.from_user_id, {})
            background_prompt = params.get("prompt")
            if not background_prompt:
                logger.error("[Doubao] 未找到背景描述词")
                e_context["reply"] = Reply(ReplyType.ERROR, "未找到背景描述词，请重试")
                return

            try:
                # 处理蒙版数据
                if "base64," in mask_base64:
                    mask_base64 = mask_base64.split("base64,")[-1]
                mask_base64 = ''.join(mask_base64.split())
                mask_base64 = mask_base64.rstrip('\\')
                padding = len(mask_base64) % 4
                if padding:
                    mask_base64 += '=' * (4 - padding)

                # 验证并处理蒙版
                try:
                    mask_bytes = base64.b64decode(mask_base64)
                    mask_array = np.frombuffer(mask_bytes, dtype=np.uint8)
                    mask_image = cv2.imdecode(mask_array, cv2.IMREAD_GRAYSCALE)
                    mask_image = cv2.bitwise_not(mask_image)
                    _, buffer = cv2.imencode('.png', mask_image)
                    mask_base64 = base64.b64encode(buffer).decode('utf-8')
                except Exception as e:
                    logger.error(f"[Doubao] 蒙版处理失败: {e}")
                    raise Exception("蒙版处理失败")

                # 构建换背景请求数据
                content = {
                    "text": background_prompt,
                    "edit_image": {
                        "edit_image_token": image_key,
                        "edit_image_url": image_url,
                        "description": "",
                        "ability": "inpainting",
                        "mask": f"data:image/png;base64,{mask_base64}",
                        "is_edit_local_image": True,
                        "is_edit_local_image_v2": "true"
                    }
                }

                data = {
                    "messages": [{
                        "content": json.dumps(content, ensure_ascii=False),
                        "content_type": 2009,
                        "attachments": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,
                        "need_create_conversation": not bool(self.conversation_id),
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "event_id": "0"
                    },
                    "conversation_id": self.conversation_id if self.conversation_id else "0",
                    "section_id": self.section_id,
                    "local_message_id": str(uuid.uuid4()),
                    "local_conversation_id": f"local_{int(time.time()*1000)}"
                }

                # 发送请求前记录完整请求参数
                logger.info(f"[Doubao] 完整请求参数:\n请求URL: /samantha/chat/completion\n请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

                # 保存请求参数到temp目录
                temp_dir = os.path.join(os.path.dirname(__file__), "temp")
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                request_log_path = os.path.join(temp_dir, "@request_debug.json")
                with open(request_log_path, "w", encoding="utf-8") as f:
                    json.dump({
                        "url": "/samantha/chat/completion",
                        "headers": self.api_client.headers,
                        "params": self.api_client._get_params(),
                        "data": data
                    }, f, ensure_ascii=False, indent=2)
                logger.info(f"[Doubao] 已保存请求参数到: {request_log_path}")

                # 发送换背景请求
                result = self.api_client.send_request(data, "/samantha/chat/completion")
                if result and "urls" in result:
                    # 更新会话信息
                    if result.get("conversation_id"):
                        self.conversation_id = result["conversation_id"]
                    if result.get("section_id"):
                        self.section_id = result["section_id"]

                    # 存储换背景后的图片
                    img_id = str(int(time.time()))
                    first_url = result["urls"][0]
                    image_token = first_url.split("/")[-1].split("~")[0]

                    operation_params = {
                        "prompt": background_prompt,
                        "conversation_id": self.conversation_id,
                        "section_id": self.section_id,
                        "reply_id": result.get("reply_id"),
                        "image_token": image_token,
                        "image_url": first_url,
                        "original_key": image_key,
                        "original_url": image_url,
                        "mask": mask_base64,
                        "data": result.get("data", [])
                    }

                    # 存储图片信息
                    image_info = {
                        "urls": result["urls"],
                        "type": "change_background",
                        "operation_params": operation_params,
                        "parent_id": None,
                        "create_time": int(time.time())
                    }

                    # 保存到数据库
                    self.image_storage.store_image(img_id, image_info)

                    # 发送换背景后的图片
                    e_context["channel"].send(Reply(ReplyType.IMAGE_URL, first_url), e_context["context"])
                    time.sleep(1)  # 等待图片发送完成
                    help_text = self._get_help_text(img_id, False)
                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                else:
                    logger.error(f"[Doubao] 换背景失败，API响应: {result}")
                    e_context["reply"] = Reply(ReplyType.ERROR, "换背景失败，请重试")

            except Exception as e:
                logger.error(f"[Doubao] 处理失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, f"处理失败: {str(e)}")
                return

        except Exception as e:
            logger.error(f"[Doubao] 处理换背景失败: {e}")
            e_context["reply"] = Reply(ReplyType.ERROR, "处理换背景失败，请重试")
            return

    def _process_change_subject(self, image_data, msg, e_context):
        """处理换主体功能"""
        try:
            # 发送等待消息
            e_context["channel"].send(Reply(ReplyType.INFO, "正在处理图片..."), e_context["context"])

            # 将 base64 字符串转换为字节
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                logger.error(f"[Doubao] Base64解码失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片数据处理失败，请重试")
                return

            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[Doubao] 图片上传失败: {error_msg}")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片上传失败，请重试")
                return

            # 获取图片key和URL
            image_key = result.get('image_key')
            image_url = result.get('file_info', {}).get('main_url')
            if not image_key or not image_url:
                logger.error("[Doubao] 未获取到图片信息")
                e_context["reply"] = Reply(ReplyType.ERROR, "图片处理失败，请重试")
                return

            # 获取主体蒙版
            mask_base64 = result.get('mask')
            if not mask_base64:
                logger.error("[Doubao] 获取主体蒙版失败")
                e_context["reply"] = Reply(ReplyType.ERROR, "获取主体蒙版失败，请重试")
                return

            # 获取主体描述词
            params = self.reference_prompts.get(msg.from_user_id, {})
            subject_prompt = params.get("prompt")
            if not subject_prompt:
                logger.error("[Doubao] 未找到主体描述词")
                e_context["reply"] = Reply(ReplyType.ERROR, "未找到主体描述词，请重试")
                return

            try:
                # 处理蒙版数据
                if "base64," in mask_base64:
                    mask_base64 = mask_base64.split("base64,")[-1]
                mask_base64 = ''.join(mask_base64.split())
                mask_base64 = mask_base64.rstrip('\\')
                padding = len(mask_base64) % 4
                if padding:
                    mask_base64 += '=' * (4 - padding)

                # 验证并处理蒙版 - 注意这里不需要反转蒙版，因为我们要替换主体
                try:
                    mask_bytes = base64.b64decode(mask_base64)
                    mask_array = np.frombuffer(mask_bytes, dtype=np.uint8)
                    mask_image = cv2.imdecode(mask_array, cv2.IMREAD_GRAYSCALE)
                    # 不需要反转蒙版，因为我们要替换主体区域
                    _, buffer = cv2.imencode('.png', mask_image)
                    mask_base64 = base64.b64encode(buffer).decode('utf-8')
                except Exception as e:
                    logger.error(f"[Doubao] 蒙版处理失败: {e}")
                    raise Exception("蒙版处理失败")

                # 构建换主体请求数据
                content = {
                    "text": subject_prompt,
                    "edit_image": {
                        "edit_image_token": image_key,
                        "edit_image_url": image_url,
                        "description": "",
                        "ability": "inpainting",
                        "mask": f"data:image/png;base64,{mask_base64}",
                        "is_edit_local_image": True,
                        "is_edit_local_image_v2": "true"
                    }
                }

                data = {
                    "messages": [{
                        "content": json.dumps(content, ensure_ascii=False),
                        "content_type": 2009,
                        "attachments": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,
                        "need_create_conversation": not bool(self.conversation_id),
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "event_id": "0"
                    },
                    "conversation_id": self.conversation_id if self.conversation_id else "0",
                    "section_id": self.section_id,
                    "local_message_id": str(uuid.uuid4()),
                    "local_conversation_id": f"local_{int(time.time()*1000)}"
                }

                # 发送请求前记录完整请求参数
                logger.info(f"[Doubao] 完整请求参数:\n请求URL: /samantha/chat/completion\n请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

                # 保存请求参数到temp目录
                temp_dir = os.path.join(os.path.dirname(__file__), "temp")
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                request_log_path = os.path.join(temp_dir, "@request_debug.json")
                with open(request_log_path, "w", encoding="utf-8") as f:
                    json.dump({
                        "url": "/samantha/chat/completion",
                        "headers": self.api_client.headers,
                        "params": self.api_client._get_params(),
                        "data": data
                    }, f, ensure_ascii=False, indent=2)
                logger.info(f"[Doubao] 已保存请求参数到: {request_log_path}")

                # 发送换主体请求
                result = self.api_client.send_request(data, "/samantha/chat/completion")
                if result and "urls" in result:
                    # 更新会话信息
                    if result.get("conversation_id"):
                        self.conversation_id = result["conversation_id"]
                    if result.get("section_id"):
                        self.section_id = result["section_id"]

                    # 存储换主体后的图片
                    img_id = str(int(time.time()))
                    first_url = result["urls"][0]
                    image_token = first_url.split("/")[-1].split("~")[0]

                    operation_params = {
                        "prompt": subject_prompt,
                        "conversation_id": self.conversation_id,
                        "section_id": self.section_id,
                        "reply_id": result.get("reply_id"),
                        "image_token": image_token,
                        "image_url": first_url,
                        "original_key": image_key,
                        "original_url": image_url,
                        "mask": mask_base64,
                        "data": result.get("data", [])
                    }

                    # 存储图片信息
                    image_info = {
                        "urls": result["urls"],
                        "type": "change_subject",
                        "operation_params": operation_params,
                        "parent_id": None,
                        "create_time": int(time.time())
                    }

                    # 保存到数据库
                    self.image_storage.store_image(img_id, image_info)

                    # 发送换主体后的图片
                    e_context["channel"].send(Reply(ReplyType.IMAGE_URL, first_url), e_context["context"])
                    time.sleep(1)  # 等待图片发送完成
                    help_text = self._get_help_text(img_id, False)
                    e_context["channel"].send(Reply(ReplyType.INFO, help_text), e_context["context"])
                else:
                    logger.error(f"[Doubao] 换主体失败，API响应: {result}")
                    e_context["reply"] = Reply(ReplyType.ERROR, "换主体失败，请重试")

            except Exception as e:
                logger.error(f"[Doubao] 处理失败: {e}")
                e_context["reply"] = Reply(ReplyType.ERROR, f"处理失败: {str(e)}")
                return

        except Exception as e:
            logger.error(f"[Doubao] 处理换主体失败: {e}")
            e_context["reply"] = Reply(ReplyType.ERROR, "处理换主体失败，请重试")
            return

    def _start_heartbeat_thread(self):
        """启动心跳线程"""
        def heartbeat_worker():
            while not self._stop_heartbeat:
                try:
                    # 调用心跳检查
                    if hasattr(self, 'api_client'):
                        self.token_manager.check_heartbeat(self.api_client)
                        # 同时检查是否需要刷新会话
                        self.token_manager.check_session_refresh(self.api_client)
                except Exception as e:
                    logger.error(f"[Doubao] 心跳线程异常: {e}")
                # 每60秒检查一次是否需要发送心跳
                time.sleep(60)

        self._heartbeat_thread = threading.Thread(target=heartbeat_worker, daemon=True)
        self._heartbeat_thread.start()

    def on_disable(self):
        """插件禁用时停止心跳线程"""
        self._stop_heartbeat = True
        if self._heartbeat_thread and self._heartbeat_thread.is_alive():
             try:
                  self._heartbeat_thread.join(timeout=2)
                  logger.info("[Doubao] Heartbeat thread stopped.")
             except Exception as e:
                  logger.warning(f"[Doubao] Error stopping heartbeat thread: {e}")