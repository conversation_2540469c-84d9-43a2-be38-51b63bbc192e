# common/cache_utils.py
# This file will contain common caching utility classes and functions.

import os
import time
import glob
import threading
from typing import List, Optional
from common.log import logger # Assuming logger is from common.log
from common.tmp_dir import TmpDir # For a consistent temporary directory base if needed

class FileSystemCacheManager:
    def __init__(self, cache_name: str, base_cache_dir: Optional[str] = None, 
                 max_age_seconds: int = 7 * 24 * 60 * 60, # 7 days
                 cleanup_interval_hours: int = 24, 
                 file_extensions: Optional[List[str]] = None):
        """
        Manages a file system cache with periodic cleanup.

        :param cache_name: Name for this specific cache instance (e.g., "image_cache", "voice_cache"). Used for subdirectories and logging.
        :param base_cache_dir: The root directory for all caches. Defaults to TmpDir().path() / "caches".
        :param max_age_seconds: Max age of a file in cache before it's cleaned up.
        :param cleanup_interval_hours: How often the cleanup task runs.
        :param file_extensions: List of file extensions to manage (e.g., [".jpg", ".png"]). If None, manages all files.
        """
        if base_cache_dir is None:
            base_cache_dir = os.path.join(TmpDir().path(), "cache_system") # Centralized cache directory
        
        self.cache_dir = os.path.join(base_cache_dir, cache_name)
        self.max_age_seconds = max_age_seconds
        self.cleanup_interval_hours = cleanup_interval_hours
        self.file_extensions = [ext.lower() for ext in file_extensions] if file_extensions else None
        self.cache_name = cache_name

        try:
            if not os.path.exists(self.cache_dir):
                os.makedirs(self.cache_dir, exist_ok=True)
                logger.info(f"[{self.cache_name}] Created cache directory: {self.cache_dir}")
        except Exception as e:
            logger.error(f"[{self.cache_name}] Failed to create cache directory {self.cache_dir}: {e}")
            # Propagate or handle critical init failure? For now, log and continue.

        self._cleanup_thread = None
        self._stop_event = threading.Event()

    def _generate_file_path(self, item_id: str, file_extension: str) -> str:
        """Generates a full path for a cached item."""
        if not file_extension.startswith('.'):
            file_extension = '.' + file_extension
        # Sanitize item_id to be a valid filename component if necessary,
        # or assume item_id is already a safe filename (e.g., a hash).
        # For simplicity, we'll use item_id directly here.
        return os.path.join(self.cache_dir, f"{item_id}{file_extension.lower()}")

    def get_cached_file_path(self, item_id: str, file_extension: str) -> Optional[str]:
        """
        Checks if a non-expired cached file exists for the given item_id and extension.
        Returns the file path if found and valid, otherwise None.
        """
        file_path = self._generate_file_path(item_id, file_extension)
        if os.path.exists(file_path):
            try:
                mtime = os.path.getmtime(file_path)
                if (time.time() - mtime) <= self.max_age_seconds:
                    logger.debug(f"[{self.cache_name}] Cache hit for {item_id}{file_extension} at {file_path}")
                    return file_path
                else:
                    logger.debug(f"[{self.cache_name}] Cache expired for {item_id}{file_extension} at {file_path}")
                    # Optionally, delete the expired file here proactively or let cleanup handle it.
            except Exception as e:
                logger.warning(f"[{self.cache_name}] Error checking cache file {file_path}: {e}")
        return None

    def save_to_cache(self, item_id: str, file_extension: str, data: bytes) -> Optional[str]:
        """
        Saves data to the cache.
        Returns the file path if successful, otherwise None.
        """
        file_path = self._generate_file_path(item_id, file_extension)
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True) # Ensure directory exists
            with open(file_path, 'wb') as f:
                f.write(data)
            logger.debug(f"[{self.cache_name}] Saved {item_id}{file_extension} to cache at {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"[{self.cache_name}] Failed to save {item_id}{file_extension} to cache at {file_path}: {e}")
            return None

    def _cleanup_cached_files(self):
        """Cleans up expired files from the cache directory."""
        logger.info(f"[{self.cache_name}] Starting cache cleanup in {self.cache_dir}...")
        if not os.path.exists(self.cache_dir):
            logger.info(f"[{self.cache_name}] Cache directory {self.cache_dir} does not exist. Skipping cleanup.")
            return

        current_time = time.time()
        total_cleaned_count = 0
        total_size_cleaned = 0

        patterns_to_check = []
        if self.file_extensions:
            for ext in self.file_extensions:
                patterns_to_check.append(f"*{ext}")
        else:
            patterns_to_check.append("*") # All files if no specific extensions

        for ext_pattern in patterns_to_check:
            pattern_path = os.path.join(self.cache_dir, ext_pattern)
            cleaned_count_for_pattern = 0
            size_cleaned_for_pattern = 0
            try:
                for fpath in glob.glob(pattern_path):
                    if self._stop_event.is_set():
                        logger.info(f"[{self.cache_name}] Cleanup task stopping.")
                        return
                    try:
                        if os.path.isfile(fpath):
                            mtime = os.path.getmtime(fpath)
                            if (current_time - mtime) > self.max_age_seconds:
                                file_size = os.path.getsize(fpath)
                                os.remove(fpath)
                                cleaned_count_for_pattern += 1
                                size_cleaned_for_pattern += file_size
                                logger.debug(f"[{self.cache_name}] Cleaned up expired cached file: {fpath} (Age: {(current_time - mtime)/3600/24:.1f} days)")
                    except Exception as e:
                        logger.warning(f"[{self.cache_name}] Failed to process/delete cached file {fpath}: {e}")
                
                if cleaned_count_for_pattern > 0:
                    logger.info(f"[{self.cache_name}] Cleaned up {cleaned_count_for_pattern} '{ext_pattern}' files, freed {size_cleaned_for_pattern/1024/1024:.2f} MB.")
                total_cleaned_count += cleaned_count_for_pattern
                total_size_cleaned += size_cleaned_for_pattern
            except Exception as e:
                logger.warning(f"[{self.cache_name}] Error during glob or iteration for pattern {pattern_path}: {e}")


        if total_cleaned_count > 0:
            logger.info(f"[{self.cache_name}] Cache cleanup finished. Total: {total_cleaned_count} files, {total_size_cleaned/1024/1024:.2f} MB freed.")
        else:
            logger.info(f"[{self.cache_name}] Cache cleanup finished. No expired files found matching criteria.")

    def _cleanup_loop(self):
        logger.info(f"[{self.cache_name}] Cache cleanup thread started. Interval: {self.cleanup_interval_hours} hours.")
        try:
            # Initial cleanup
            if not self._stop_event.is_set():
                logger.info(f"[{self.cache_name}] Performing initial cache cleanup...")
                self._cleanup_cached_files()
                logger.info(f"[{self.cache_name}] Initial cache cleanup complete.")

            while not self._stop_event.is_set():
                try:
                    # Sleep for the interval
                    # self._stop_event.wait(timeout=self.cleanup_interval_hours * 60 * 60)
                    # More robust sleep that can be interrupted by stop_event
                    for _ in range(self.cleanup_interval_hours * 60): # Check every minute
                        if self._stop_event.wait(timeout=60): # Wait for 60 seconds
                            logger.info(f"[{self.cache_name}] Cleanup task stopping during sleep.")
                            return
                    
                    if self._stop_event.is_set():
                        logger.info(f"[{self.cache_name}] Cleanup task stopping before next run.")
                        return

                    logger.debug(f"[{self.cache_name}] Cache cleanup task waking up for scheduled run.")
                    self._cleanup_cached_files()
                except Exception as e:
                    logger.error(f"[{self.cache_name}] Cache cleanup loop error: {e}. Retrying in 1 hour.")
                    if self._stop_event.wait(timeout=3600): # Wait 1 hour before retrying loop on major error
                        return
        except Exception as e:
            logger.error(f"[{self.cache_name}] Cache cleanup thread failed critically: {e}")
        finally:
            logger.info(f"[{self.cache_name}] Cache cleanup thread finished.")


    def start_cleanup_task(self):
        """Starts the periodic cache cleanup task in a background thread."""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            logger.info(f"[{self.cache_name}] Cleanup task already running.")
            return

        self._stop_event.clear()
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.name = f"{self.cache_name}CleanupThread"
        self._cleanup_thread.start()
        logger.info(f"[{self.cache_name}] Cache cleanup task scheduled.")

    def stop_cleanup_task(self, wait=True):
        """Stops the cleanup task."""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            logger.info(f"[{self.cache_name}] Stopping cleanup task...")
            self._stop_event.set()
            if wait:
                self._cleanup_thread.join(timeout=10) # Wait for thread to finish
                if self._cleanup_thread.is_alive():
                    logger.warning(f"[{self.cache_name}] Cleanup thread did not stop in time.")
            logger.info(f"[{self.cache_name}] Cleanup task stop signal sent.")
        else:
            logger.info(f"[{self.cache_name}] Cleanup task not running or already stopped.")