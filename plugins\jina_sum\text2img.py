'''
Author: sin<PERSON><PERSON> <EMAIL>
Date: 2024-11-11 17:42:22
LastEditors: sineom <EMAIL>
LastEditTime: 2024-11-21 18:15:04
FilePath: /plugin_summary/text2img.py
Description:

Copyright (c) 2024 by sineom, All Rights Reserved.
'''
import os
import time
import logging
import threading
import markdown2
from typing import Optional
from playwright.sync_api import sync_playwright

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Text2ImageConverter:
    def __init__(self):
        self.output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images')
        os.makedirs(self.output_dir, exist_ok=True)
        self.playwright = None
        self.browser = None
        self.is_available = False
        self._thread_local = threading.local()
        # 初始化线程本地存储的属性
        self._thread_local.playwright = None
        self._thread_local.browser = None
        self.setup()

    def setup(self):
        """初始化Playwright"""
        try:
            # 在主线程中初始化Playwright
            self._init_playwright()
            self.is_available = True
            logger.info("Playwright initialized successfully with system Chrome")
        except Exception as e:
            logger.error(f"Failed to initialize Playwright: {e}")
            self.is_available = False

    def _init_playwright(self):
        """在当前线程中初始化Playwright"""
        try:
            self._thread_local.playwright = sync_playwright().start()
            try:
                self._thread_local.browser = self._thread_local.playwright.chromium.launch(
                    headless=True,
                    channel='chrome',  # 使用已安装的Chrome
                    args=[
                        '--disable-gpu',
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-setuid-sandbox',
                    ]
                )
                logger.info("Successfully launched Chrome browser")
            except Exception as e:
                logger.warning(f"Failed to use system Chrome, falling back to built-in browser: {e}")
                # 如果失败，回退到内置浏览器
                if hasattr(self._thread_local, 'playwright') and self._thread_local.playwright:
                    try:
                        self._thread_local.browser = self._thread_local.playwright.chromium.launch(
                            headless=True,
                            args=[
                                '--disable-gpu',
                                '--no-sandbox',
                                '--disable-dev-shm-usage',
                                '--disable-setuid-sandbox',
                            ]
                        )
                        logger.info("Successfully launched built-in browser")
                    except Exception as browser_e:
                        logger.error(f"Failed to launch built-in browser: {browser_e}")
                        # 如果无法启动浏览器，清理playwright并重新抛出异常
                        if hasattr(self._thread_local, 'playwright') and self._thread_local.playwright:
                            self._thread_local.playwright.stop()
                            self._thread_local.playwright = None
                        raise
        except Exception as init_e:
            logger.error(f"Failed to initialize Playwright: {init_e}")
            # 重置属性
            self._thread_local.playwright = None
            self._thread_local.browser = None
            raise

    def _ensure_playwright_initialized(self):
        """确保当前线程中已初始化Playwright"""
        current_thread_id = threading.get_ident()
        logger.debug(f"Ensuring Playwright initialized in thread {current_thread_id}")

        if (not hasattr(self._thread_local, 'playwright') or
            self._thread_local.playwright is None or
            not hasattr(self._thread_local, 'browser') or
            self._thread_local.browser is None):
            logger.info(f"Initializing Playwright in thread {current_thread_id}")
            self._init_playwright()

        # 验证初始化是否成功
        if (not hasattr(self._thread_local, 'browser') or
            self._thread_local.browser is None):
            logger.error(f"Browser initialization failed in thread {current_thread_id}")
            raise RuntimeError("Browser initialization failed")

        logger.debug(f"Playwright initialized successfully in thread {current_thread_id}")

    def convert_markdown_to_html(self, text: str) -> str:
        """将Markdown格式转换为HTML"""
        # 转换Markdown为HTML，保留换行符
        html = markdown2.markdown(text, extras=['break-on-newline'])
        return html

    def convert_text_to_image(self, text: str) -> Optional[str]:
        """将文本转换为图片"""
        if not self.is_available:
            logger.warning("Text to image conversion not available")
            return None

        try:
            # 确保当前线程中已初始化Playwright
            self._ensure_playwright_initialized()

            # 将Markdown转换为HTML
            content_html = self.convert_markdown_to_html(text)

            # 构建完整的 HTML
            # pyright: ignore[reportUndefinedVariable]
            html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        body {{
            font-family: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 15px;
            min-height: 100vh;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #ffffff;
            line-height: 1.6;
        }}

        .page-title {{
            text-align: center;
            font-size: 26px;
            font-weight: 700;
            color: transparent;
            background: linear-gradient(45deg, #ff3e82, #f97316);
            -webkit-background-clip: text;
            background-clip: text;
            margin-bottom: 22px;
            text-shadow: 0 2px 10px rgba(255, 62, 130, 0.2);
            letter-spacing: 2px;
            position: relative;
            padding-bottom: 15px;
        }}

        .page-title::after {{
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, #ff3e82, #f97316);
            border-radius: 3px;
        }}

        .container {{
            background: rgba(15, 23, 42, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 28px 22px;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.25),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            max-width: 500px;
            margin: 0 auto;
            border: 1px solid rgba(255, 255, 255, 0.08);
            position: relative;
            overflow: hidden;
        }}

        .container::before {{
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0),
                rgba(255, 255, 255, 0.1),
                rgba(255, 255, 255, 0));
        }}

        h1 {{
            font-size: 24px;
            color: #ffffff;
            margin-bottom: 24px;
            text-align: center;
            font-weight: 700;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }}

        h2 {{
            font-size: 19px;
            color: #ffffff;
            margin: 28px 0 18px 0;
            padding: 12px 18px;
            background: linear-gradient(90deg, rgba(79, 107, 255, 0.2), rgba(79, 107, 255, 0.05));
            border-radius: 10px;
            border-left: 4px solid #4f6bff;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }}

        h3 {{
            font-size: 17px;
            color: #ffffff;
            margin: 22px 0 14px 0;
            padding: 10px 16px;
            background: linear-gradient(90deg, rgba(79, 107, 255, 0.15), rgba(79, 107, 255, 0.01));
            border-radius: 10px;
            border-left: 3px solid #4f6bff;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }}

        ul {{
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
        }}

        li {{
            position: relative;
            padding: 12px 18px 12px 18px;
            color: #e2e8f0;
            line-height: 1.5;
            background: rgba(30, 41, 59, 0.4);
            margin: 10px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }}

        li:hover {{
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }}

        ol {{
            list-style-position: inside;
            padding: 0;
            margin: 0 0 20px 0;
            counter-reset: item;
        }}

        ol li {{
            counter-increment: item;
            padding: 12px 18px 12px 18px;
            color: #e2e8f0;
            line-height: 1.5;
            background: rgba(30, 41, 59, 0.4);
            margin: 10px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }}

        ol li::before {{
            content: counter(item) ".";
            display: inline-block;
            font-weight: bold;
            margin-right: 8px;
            color: #4f6bff;
        }}

        strong {{
            color: #ffffff;
            font-weight: 600;
            margin-right: 4px;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
        }}

        hr {{
            border: none;
            height: 1px;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0),
                rgba(255, 255, 255, 0.2),
                rgba(255, 255, 255, 0));
            margin: 25px 0;
            position: relative;
        }}

        hr::before {{
            content: "✧";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #0f172a;
            padding: 0 10px;
            color: rgba(255, 255, 255, 0.4);
            font-size: 12px;
        }}

        p {{
            margin: 14px 5px;
            color: #e2e8f0;
            line-height: 1.6;
            padding: 0 10px;
            text-align: justify;
        }}

        /* Tags styles */
        .tags-paragraph {{
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
            padding: 5px 10px;
            margin-bottom: 15px;
            justify-content: center;
        }}

        .tag-item {{
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
            color: #93c5fd;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid rgba(59, 130, 246, 0.3);
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
            transition: all 0.2s ease;
        }}

        .tag-item:hover {{
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            border-color: rgba(59, 130, 246, 0.5);
        }}

        .footer {{
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            color: #93c5fd;
            font-size: 14px;
            position: relative;
            font-weight: 500;
        }}

        .footer::before {{
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 1px;
            background: linear-gradient(90deg,
                rgba(147, 197, 253, 0),
                rgba(147, 197, 253, 0.5),
                rgba(147, 197, 253, 0));
        }}

    </style>
</head>
<body>
    <div class="page-title">Markdown Poster</div>
    <div class="container">
        {content_html}
    </div>
    <div class="footer">
        Powered by 🤖 小艾
    </div>
</body>
</html>
"""

            # 创建新的页面
            if not hasattr(self._thread_local, 'browser') or self._thread_local.browser is None:
                raise RuntimeError("Browser is not initialized")

            page = self._thread_local.browser.new_page()
            try:
                # 设置视口大小 - 使用更窄的宽度
                page.set_viewport_size({"width": 550, "height": 600})

                # 设置HTML内容
                page.set_content(html)

                # 等待内容加载完成
                page.wait_for_load_state('networkidle')

                # Added JavaScript execution to wrap tags
                page.evaluate("""() => {
                    const paragraphs = document.querySelectorAll('.container p'); // Target paragraphs within the container
                    paragraphs.forEach(p => {
                        // More specific check: Look for paragraphs starting roughly with '🏷️ 智能标签' or similar
                        // and containing '#' characters. Adjust the condition based on actual LLM output.
                        const textContent = p.textContent.trim();
                        if ((textContent.startsWith('🏷️') || textContent.startsWith('智能标签') || textContent.startsWith('#')) && textContent.includes('#')) {
                            // Check if most words are tags
                            const words = textContent.split(/\\s+/); // Escaped backslash for regex
                            const tagWords = words.filter(word => word.startsWith('#'));
                            // Consider it a tags paragraph if a significant portion are tags
                            if (tagWords.length > 0 && tagWords.length >= (words.length / 2 - 1) ) { // Heuristic: at least half are tags (adjust as needed)
                                p.classList.add('tags-paragraph'); // Add class for styling
                                p.innerHTML = tagWords.map(tag =>
                                    // Escape backticks for Python triple-quoted string
                                    `<span class='tag-item'>${tag.replace(/[.,;:!?]$/, '')}</span>` // Remove trailing punctuation from tag
                                ).join(' ');
                            }
                        }
                    });
                }""")

                # 获取内容高度
                body_height = page.evaluate('document.body.scrollHeight')

                # 调整视口高度以适应内容，保持窄的宽度
                page.set_viewport_size({"width": 550, "height": body_height})

                # 生成唯一的文件名 - 改为JPG格式
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                image_path = os.path.join(self.output_dir, f'summary_{timestamp}.jpg')

                # 截图 - 使用JPG格式以减小文件大小
                page.screenshot(
                    path=image_path,
                    full_page=True,
                    type='jpeg',
                    quality=85  # JPG质量设置为85，平衡文件大小和图片质量
                )

                logger.info(f"Image saved to {image_path}")
                return image_path
            finally:
                page.close()
        except Exception as e:
            logger.error(f"Error converting text to image: {e}", exc_info=True)
            return None

    def close(self):
        """关闭浏览器和Playwright"""
        try:
            if hasattr(self._thread_local, 'browser') and self._thread_local.browser is not None:
                try:
                    self._thread_local.browser.close()
                except Exception as browser_e:
                    logger.error(f"Error closing browser: {browser_e}")
                finally:
                    self._thread_local.browser = None

            if hasattr(self._thread_local, 'playwright') and self._thread_local.playwright is not None:
                try:
                    self._thread_local.playwright.stop()
                except Exception as playwright_e:
                    logger.error(f"Error stopping playwright: {playwright_e}")
                finally:
                    self._thread_local.playwright = None

            logger.info("Browser and Playwright closed successfully")
        except Exception as e:
            logger.error(f"Error closing browser and Playwright: {e}", exc_info=True)

def main():
    converter = Text2ImageConverter()
    try:
        # 示例文本
        text = """
# 【每日总结】

## 整体评价
群聊氛围活跃，话题丰富多样。

### 1️⃣ 鲍鱼大餐 🔥🔥🔥
- **参与者**：用户A、用户B
- **时间段**：12:01-12:11
- **过程**：用户讨论了北京有鲍，鲍很大，一锅炖下下，化为脓，脓很大，需要两个烧烤架，随后用户回复鲍之大，好吃，吃不完。
- **评价**：简短的玩梗，内容轻松。

------------

### 2️⃣ 即学即分 🔥
- **参与者**：
- **时间段**：
- **过程**：
- **评价**：

------------

## 今日最活跃的前五个发言者
用户A、用户B
"""

        image_path = converter.convert_text_to_image(text)
        if image_path:
            print(f"Image generated successfully at: {image_path}")
        else:
            print("Failed to generate image")

    except Exception as e:
        logger.error(f"Process failed: {e}", exc_info=True)
    finally:
        converter.close()

if __name__ == "__main__":
    main()