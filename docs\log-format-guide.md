# 📋 日志格式指南

> **Dify-on-WeChat-PadPro** 美观日志系统使用指南

---

## 🎨 新日志格式特性

### ✨ 主要特性
- **🌈 彩色显示**: 不同日志级别使用不同颜色
- **📱 图标标识**: 直观的emoji图标增强可读性
- **🔍 关键词高亮**: 自动识别并标记重要操作
- **🖥️ Windows优化**: 专为Windows终端环境优化
- **⚙️ 可配置**: 支持自定义开启/关闭各项功能

---

## 🎯 日志级别与颜色

| 级别 | 图标 | 颜色 | 用途 |
|------|------|------|------|
| 🔍 DEBUG | 🔍 | 绿色 | 调试信息 |
| 📘 INFO | 📘 | 蓝色 | 一般信息 |
| ⚠️ WARNING | ⚠️ | 黄色 | 警告信息 |
| ❌ ERROR | ❌ | 红色 | 错误信息 |
| 🚨 CRITICAL | 🚨 | 紫色 | 严重错误 |

---

## 🏷️ 关键词图标

系统会自动为包含特定关键词的日志添加相应图标：

| 关键词 | 图标 | 示例 |
|--------|------|------|
| 收到 | 📨 | 📨 收到文本消息 |
| 发送 | 📤 | 📤 发送回复成功 |
| 连接 | 🔌 | 🔌 连接到服务器 |
| 登录 | 🔑 | 🔑 登录成功 |
| 启动 | 🚀 | 🚀 启动应用程序 |
| 初始化 | ⚙️ | ⚙️ 初始化完成 |
| 配置 | 🔧 | 🔧 配置加载成功 |
| 插件 | 🧩 | 🧩 插件注册完成 |
| AI | 🤖 | 🤖 AI 模型响应 |
| reply | 💬 | 💬 reply 生成完成 |
| query | ❓ | ❓ query 处理中 |
| 成功 | ✅ | ✅ 成功完成操作 |
| 失败 | ❌ | ❌ 失败重试中 |

---

## ⚙️ 配置选项

在 `config.json` 中添加以下配置项：

```json
{
  "log_level": "INFO",
  "log_use_color": true,
  "log_use_icons": true,
  "debug": false
}
```

### 配置说明

- **`log_use_color`**: 是否启用彩色显示
  - `true`: 启用彩色（推荐）
  - `false`: 使用普通黑白显示

- **`log_use_icons`**: 是否启用图标显示
  - `true`: 显示emoji图标（推荐）
  - `false`: 不显示图标

---

## 🖥️ 终端兼容性

### ✅ 完全支持
- **Windows Terminal** (推荐)
- **PowerShell 7+**
- **VS Code 集成终端**
- **PyCharm 终端**

### ⚠️ 部分支持
- **Windows CMD** (颜色支持有限)
- **PowerShell 5.1** (可能需要额外配置)

### 🔧 故障排除

如果颜色显示异常，可以：

1. **禁用颜色**：设置 `"log_use_color": false`
2. **仅禁用图标**：设置 `"log_use_icons": false`
3. **更新终端**：使用 Windows Terminal 获得最佳体验

---

## 🧪 测试日志格式

运行测试脚本查看效果：

```bash
python test_log_format.py
```

这将显示各种日志级别和关键词的格式化效果。

---

## 📝 日志格式示例

### 🎨 彩色模式（终端显示）
```
📘 [INFO] 2025-01-27 20:30:15 │ wechatpadpro_channel.py:1810 │ 📨 收到文本消息: ID:123456
🤖 [INFO] 2025-01-27 20:30:16 │ open_ai_bot.py:138 │ 💬 reply=又来烦我了是吧？
✅ [INFO] 2025-01-27 20:30:17 │ wechatpadpro_channel.py:3695 │ 📤 发送文本消息成功
⚠️ [WARNING] 2025-01-27 20:30:18 │ config.py:45 │ 🔧 配置文件缺少可选参数
```

### 📄 普通模式（文件保存）
```
[INFO][2025-01-27 20:30:15][wechatpadpro_channel.py:1810] - 收到文本消息: ID:123456
[INFO][2025-01-27 20:30:16][open_ai_bot.py:138] - reply=又来烦我了是吧？
[INFO][2025-01-27 20:30:17][wechatpadpro_channel.py:3695] - 发送文本消息成功
[WARNING][2025-01-27 20:30:18][config.py:45] - 配置文件缺少可选参数
```

---

## 💡 使用建议

1. **开发调试**: 启用所有功能获得最佳体验
2. **生产环境**: 可考虑禁用图标以提高性能
3. **日志分析**: 文件中的日志保持原格式便于解析
4. **终端选择**: 推荐使用 Windows Terminal 获得最佳显示效果

---

*最后更新: 2025-01-27*  
*维护者: AI Assistant*
