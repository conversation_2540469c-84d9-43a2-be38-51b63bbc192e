import json
import os
import time
import uuid
import base64
import requests
import random
import hashlib
import re
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from plugins import Plugin, Event, EventAction, EventContext, register
from common.log import logger
from .module.token_manager import TokenManager
from .module.api_client import ApiClient
from .module.image_storage import ImageStorage
from .module.video_generator import VideoGenerator

@register(
    name="<PERSON><PERSON>",
    desc="即梦AI绘画和视频生成插件",
    version="1.0",
    author="lanvent",
    desire_priority=92  # 提高优先级，确保最先处理命令
)
class JimengPlugin(Plugin):
    def __init__(self):
        super().__init__()
        self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
        self.config = self._load_config()
        
        # 获取数据保留天数配置
        retention_days = self.config.get("storage", {}).get("retention_days", 7)
        
        # 初始化存储路径
        storage_dir = os.path.join(os.path.dirname(__file__), "storage")
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir)
            
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 初始化各个模块
        self.image_storage = ImageStorage(
            os.path.join(storage_dir, "images.db"),
            retention_days=retention_days
        )
        self.token_manager = TokenManager(self.config)
        self.api_client = ApiClient(self.token_manager, self.config)
        self.video_generator = VideoGenerator(self.config, self.token_manager)
        
        # 初始化图片处理器
        from .module.image_processor import ImageProcessor
        self.image_processor = ImageProcessor(temp_dir)
        
        # 定义用户代理
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
        
        # 等待接收图片的状态
        self.waiting_for_image = {}
        
        logger.info(f"[Jimeng] plugin initialized with {retention_days} days data retention")

    def _save_temp_image(self, image_content, session_id, context=None):
        """保存临时图片"""
        try:
            # 检查图片内容
            if not image_content:
                logger.error("[Jimeng] Empty image content")
                return None
                
            # 确保临时目录存在
            temp_dir = os.path.join(os.path.dirname(__file__), "temp")
            if not os.path.exists(temp_dir):
                try:
                    os.makedirs(temp_dir, exist_ok=True)
                    logger.info(f"[Jimeng] Created temp directory: {temp_dir}")
                except Exception as e:
                    logger.error(f"[Jimeng] Failed to create temp directory: {str(e)}")
                    return None
            
            # 获取当前工作目录
            cwd = os.getcwd()
            
            # 尝试的路径列表
            file_paths = [
                image_content,  # 原始路径
                os.path.abspath(image_content),  # 绝对路径
                os.path.join(cwd, image_content),  # 相对于当前目录的路径
                os.path.join(cwd, 'tmp', os.path.basename(image_content)),  # tmp目录
                os.path.join(cwd, 'plugins', 'jimeng', 'tmp', os.path.basename(image_content)),  # 插件tmp目录
                os.path.join(cwd, 'plugins', 'jimeng', 'storage', 'temp', os.path.basename(image_content)),  # 插件临时目录
            ]
            
            # 检查每个可能的路径
            for path in file_paths:
                if os.path.isfile(path):
                    logger.info(f"[Jimeng] 找到图片文件: {path}")
                    return path
            
            # 如果文件还未下载,尝试下载
            if context and "msg" in context and hasattr(context["msg"], '_prepare_fn') and not context["msg"]._prepared:
                msg = context["msg"]
                msg._prepare_fn()
                msg._prepared = True
                time.sleep(1)  # 等待文件准备完成
                
                # 再次检查所有路径
                for path in file_paths:
                    if os.path.isfile(path):
                        logger.info(f"[Jimeng] 下载后找到图片文件: {path}")
                        return path
            
            # 如果是URL,尝试下载
            if isinstance(image_content, str) and (image_content.startswith('http://') or image_content.startswith('https://')):
                response = requests.get(image_content, timeout=30)
                if response.status_code == 200:
                    temp_path = os.path.join(temp_dir, f"temp_{session_id}.png")
                    with open(temp_path, "wb") as f:
                        f.write(response.content)
                    return temp_path
            
            logger.error(f"[Jimeng] 未找到图片文件: {image_content}")
            return None
            
        except Exception as e:
            logger.error(f"[Jimeng] Error in _save_temp_image: {str(e)}")
            return None

    def on_handle_context(self, e_context: EventContext):
        """处理消息"""
        context = e_context['context']
        content = context.content
        session_id = context['session_id']
        logger.debug(f"[Jimeng] on_handle_context. content: {content}")
        
        if not content:
            return
            
        # 优先处理积分相关命令
        content = content.strip().lower()
        
        # 积分查询命令
        if content in ["j积分", "j积分查询"]:
            logger.info("[Jimeng] 触发积分查询命令")
            try:
                # 保存当前账号索引
                original_index = self.token_manager.current_account_index
                reply_text = "所有账号积分信息:\n"
                
                # 遍历所有账号
                for i in range(self.token_manager.get_account_count()):
                    account = self.token_manager.switch_to_account(i)
                    if account:
                        credit_info = self.token_manager.get_credit()
                        if credit_info:
                            reply_text += f"\n账号{i + 1} ({account.get('description', '未命名')}):\n"
                            if credit_info['gift_credit'] > 0:
                                reply_text += f"赠送积分: {credit_info['gift_credit']}\n"
                            if credit_info['purchase_credit'] > 0:
                                reply_text += f"购买积分: {credit_info['purchase_credit']}\n"
                            if credit_info['vip_credit'] > 0:
                                reply_text += f"VIP积分: {credit_info['vip_credit']}\n"
                            reply_text += f"总积分: {credit_info['total_credit']}"
                        else:
                            reply_text += f"\n账号{i + 1} ({account.get('description', '未命名')}): 获取积分失败"
                
                # 恢复原始账号
                self.token_manager.switch_to_account(original_index)
                e_context['reply'] = Reply(ReplyType.TEXT, reply_text)
            except Exception as e:
                logger.error(f"[Jimeng] Error getting credit info: {str(e)}")
                e_context['reply'] = Reply(ReplyType.TEXT, "查询积分失败，请稍后重试")
            e_context.action = EventAction.BREAK_PASS  # 阻止其他插件处理
            return

        # 积分领取命令
        if content == "j积分领取":
            logger.info("[Jimeng] 触发积分领取命令")
            try:
                # 为所有账号领取每日积分
                reply_text = "领取每日积分结果:\n"
                original_index = self.token_manager.current_account_index
                
                for i in range(self.token_manager.get_account_count()):
                    # 切换到对应账号
                    account = self.token_manager.switch_to_account(i)
                    if account:
                        total_credit = self.token_manager.receive_daily_credit()
                        if total_credit is not None:
                            reply_text += f"\n账号{i + 1} ({account.get('description', '未命名')}): 领取成功，当前总积分: {total_credit}"
                        else:
                            reply_text += f"\n账号{i + 1} ({account.get('description', '未命名')}): 领取失败，可能已经领取过"
                    else:
                        reply_text += f"\n账号{i + 1}: 切换失败"
                
                # 恢复原始账号
                self.token_manager.switch_to_account(original_index)
                e_context['reply'] = Reply(ReplyType.TEXT, reply_text)
            except Exception as e:
                logger.error(f"[Jimeng] Error receiving daily credit: {str(e)}")
                e_context['reply'] = Reply(ReplyType.TEXT, "领取积分失败，请稍后重试")
            e_context.action = EventAction.BREAK_PASS  # 阻止其他插件处理
            return

        # 切换账号命令
        if content.startswith("j切换账号"):
            logger.info("[Jimeng] 触发切换账号命令")
            try:
                account_index = int(content[5:].strip()) - 1  # 将用户输入的账号数字转为索引
                account = self.token_manager.switch_to_account(account_index)
                if account:
                    # 更新当前账号索引
                    self.token_manager.current_account_index = account_index
                    logger.info(f"[Jimeng] 成功切换到账号 {account_index + 1}, 邮箱: {account.get('email', 'unknown')}")
                    
                    credit_info = self.token_manager.get_credit()
                    if credit_info:
                        reply_text = f"已切换到账号 {account_index + 1} ({account.get('description', '未命名')})\n当前账号积分信息:\n"
                        # 只显示非零积分项
                        if credit_info['gift_credit'] > 0:
                            reply_text += f"赠送积分: {credit_info['gift_credit']}\n"
                        if credit_info['purchase_credit'] > 0:
                            reply_text += f"购买积分: {credit_info['purchase_credit']}\n"
                        if credit_info['vip_credit'] > 0:
                            reply_text += f"会员积分: {credit_info['vip_credit']}\n"
                        reply_text += f"总积分: {credit_info['total_credit']}"
                        e_context['reply'] = Reply(ReplyType.TEXT, reply_text)
                    else:
                        e_context['reply'] = Reply(ReplyType.TEXT, f"切换到账号 {account_index + 1} ({account.get('description', '未命名')}) 成功，但获取积分信息失败")
                else:
                    e_context['reply'] = Reply(ReplyType.TEXT, f"切换账号失败，账号索引无效。当前共有 {self.token_manager.get_account_count()} 个账号")
            except ValueError:
                e_context['reply'] = Reply(ReplyType.TEXT, "切换账号命令格式错误，正确格式：j切换账号2")
            except Exception as e:
                logger.error(f"[Jimeng] Error switching account: {str(e)}")
                e_context['reply'] = Reply(ReplyType.TEXT, f"切换账号失败: {str(e)}")
            e_context.action = EventAction.BREAK_PASS  # 阻止其他插件处理
            return
            
        # 检查是否是即梦命令
        if not (content.startswith("即梦") or content.startswith("j放大") or content.startswith("jm")):
            # 检查是否在等待接收图片
            if session_id in self.waiting_for_image and context.type == ContextType.IMAGE:
                prompt_data = self.waiting_for_image[session_id]
                del self.waiting_for_image[session_id]
                
                # 获取参数
                prompt = prompt_data["prompt"]
                model = prompt_data["model"]
                type = prompt_data.get("type", "")
                
                logger.info(f"[Jimeng] 收到图片，开始处理。类型: {type}, 模型: {model}, 提示词: {prompt}")
                
                # 处理参考图生图
                if type == "img2img":
                    # 发送等待提示
                    wait_reply = Reply(ReplyType.TEXT, f"正在使用{model}模型生成图片......")
                    e_context["channel"].send(wait_reply, e_context["context"])
                    
                    # 获取图片内容
                    try:
                        temp_file = self._save_temp_image(content, session_id, context)
                        if not temp_file:
                            e_context['reply'] = Reply(ReplyType.TEXT, "保存图片失败，请重试")
                            return
                        
                        logger.info(f"[Jimeng] 成功保存临时图片: {temp_file}")
                        
                        # 检查图片文件是否存在
                        if not os.path.exists(temp_file):
                            logger.error(f"[Jimeng] 临时文件不存在: {temp_file}")
                            e_context['reply'] = Reply(ReplyType.TEXT, "临时文件不存在，请重试")
                            return
                            
                        # 检查图片文件大小
                        file_size = os.path.getsize(temp_file)
                        if file_size == 0:
                            logger.error(f"[Jimeng] 临时文件大小为0: {temp_file}")
                            e_context['reply'] = Reply(ReplyType.TEXT, "上传的图片无效，请重试")
                            return
                            
                        logger.info(f"[Jimeng] 临时文件大小: {file_size} 字节")
                        
                        # 上传图片获取URI
                        image_uri = None
                        try:
                            # 使用VideoGenerator中的图片上传方法
                            upload_result = self.video_generator.upload_image(temp_file)
                            if upload_result and "image_uri" in upload_result:
                                image_uri = upload_result["image_uri"]
                                # 检查image_uri格式，确保其格式完整
                                if not image_uri.startswith("tos-cn-i-"):
                                    # 如果返回的不是完整路径，则构建完整路径
                                    if "/" not in image_uri:
                                        image_uri = f"tos-cn-i-tb4s082cfz/{image_uri}"
                                
                                logger.info(f"[Jimeng] 成功获取image_uri: {image_uri}")
                            else:
                                logger.error(f"[Jimeng] Upload failed or missing image_uri: {upload_result}")
                                e_context['reply'] = Reply(ReplyType.TEXT, "上传图片失败，请重试")
                                return
                        except Exception as e:
                            logger.error(f"[Jimeng] Error uploading image: {str(e)}")
                            e_context['reply'] = Reply(ReplyType.TEXT, f"上传图片失败: {str(e)}")
                            return
                            
                        # 构造aigc_draft/generate请求
                        try:
                            # 获取控制网络参数
                            control_net = prompt_data.get("control_net", "none")
                            strength = prompt_data.get("strength", 1.0)
                            
                            # 获取token信息
                            token_info = self.token_manager.get_token("/mweb/v1/aigc_draft/generate")
                            if not token_info:
                                logger.error("[Jimeng] 获取token失败")
                                e_context['reply'] = Reply(ReplyType.TEXT, "获取token失败，请重试")
                                return
                            
                            # 获取账号信息
                            account = self.token_manager.get_current_account()
                            if not account:
                                logger.error("[Jimeng] 获取账号信息失败")
                                e_context['reply'] = Reply(ReplyType.TEXT, "获取账号信息失败")
                                return
                            
                            # 构建正确的babi_param
                            babi_param = {
                                "scenario": "image_video_generation",
                                "feature_key": "to_image_referenceimage_generate",
                                "feature_entrance": "to_image",
                                "feature_entrance_detail": "to_image-referenceimage-byte_edit"
                            }
                            
                            url = "https://jimeng.jianying.com/mweb/v1/aigc_draft/generate"
                            
                            # 设置URL参数
                            params = {
                                "babi_param": json.dumps(babi_param),
                                "aid": "513695",
                                "device_platform": "web",
                                "region": "CN", 
                                "web_id": account.get("web_id", "")
                            }

                            # 为提示词添加##前缀
                            formatted_prompt = prompt
                            if not formatted_prompt.startswith("##"):
                                formatted_prompt = f"##{formatted_prompt}"
                            
                            # 生成UUID作为请求ID
                            submit_id = str(uuid.uuid4())
                            draft_id = str(uuid.uuid4())
                            component_id = str(uuid.uuid4())
                            
                            # 根据control_net参数设置ability_name
                            ability_name = "byte_edit"
                            if control_net == "canny":
                                ability_name = "canny"
                            elif control_net == "lineart":
                                ability_name = "lineart"
                            elif control_net == "openpose":
                                ability_name = "openpose"
                            
                            # 构造draft_content
                            draft_content = {
                                "type": "draft",
                                "id": draft_id,
                                "min_version": "3.0.2",
                                "min_features": [],
                                "is_from_tsn": True,
                                "version": "3.1.5",
                                "main_component_id": component_id,
                                "component_list": [
                                    {
                                        "type": "image_base_component",
                                        "id": component_id,
                                        "min_version": "3.0.2",
                                        "generate_type": "blend",
                                        "aigc_mode": "workbench",
                                        "abilities": {
                                            "type": "",
                                            "id": str(uuid.uuid4()),
                                            "blend": {
                                                "type": "",
                                                "id": str(uuid.uuid4()),
                                                "min_features": [],
                                                "core_param": {
                                                    "type": "",
                                                    "id": str(uuid.uuid4()),
                                                    "model": "high_aes_general_v20_L:general_v2.0_L",
                                                    "prompt": formatted_prompt,
                                                    "sample_strength": strength,
                                                    "image_ratio": 1,
                                                    "large_image_info": {
                                                        "type": "",
                                                        "id": str(uuid.uuid4()),
                                                        "height": 1360,
                                                        "width": 1360,
                                                        "resolution_type": "1k"
                                                    }
                                                },
                                                "ability_list": [
                                                    {
                                                        "type": "",
                                                        "id": str(uuid.uuid4()),
                                                        "name": ability_name,
                                                        "image_uri_list": [image_uri],
                                                        "image_list": [
                                                            {
                                                                "type": "image",
                                                                "id": str(uuid.uuid4()),
                                                                "source_from": "upload",
                                                                "platform_type": 1,
                                                                "name": "",
                                                                "image_uri": image_uri,
                                                                "width": 0,
                                                                "height": 0,
                                                                "format": "",
                                                                "uri": image_uri
                                                            }
                                                        ],
                                                        "strength": strength
                                                    }
                                                ],
                                                "history_option": {
                                                    "type": "",
                                                    "id": str(uuid.uuid4())
                                                },
                                                "prompt_placeholder_info_list": [
                                                    {
                                                        "type": "",
                                                        "id": str(uuid.uuid4()),
                                                        "ability_index": 0
                                                    }
                                                ],
                                                "postedit_param": {
                                                    "type": "",
                                                    "id": str(uuid.uuid4()),
                                                    "generate_type": 0
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                            
                            # 构造请求体
                            payload = {
                                "extend": {
                                    "root_model": "high_aes_general_v20_L:general_v2.0_L",
                                    "template_id": ""
                                },
                                "submit_id": submit_id,
                                "draft_content": json.dumps(draft_content)
                            }
                            
                            # 日志输出详细的请求数据，便于调试
                            logger.debug(f"[Jimeng] aigc_draft/generate完整请求数据:")
                            logger.debug(f"[Jimeng] URL: {url}")
                            logger.debug(f"[Jimeng] params: {json.dumps(params, ensure_ascii=False)}")
                            logger.debug(f"[Jimeng] payload: {json.dumps(payload, ensure_ascii=False)}")
                            
                            # 设置请求头
                            headers = {
                                'Content-Type': 'application/json',
                                'User-Agent': self.user_agent,
                                'appid': '513695',
                                'sign': token_info["sign"],
                                'sign-ver': '1',
                                'cookie': token_info["cookie"],
                                'device-time': token_info["device_time"],
                                'msToken': token_info["msToken"],
                                'x-bogus': token_info["a_bogus"]
                            }
                            
                            # 发送请求
                            logger.info(f"[Jimeng] 发送aigc_draft/generate请求: URL={url}, params={params}")
                            logger.debug(f"[Jimeng] 请求体: {json.dumps(payload, ensure_ascii=False)}")
                            
                            response = requests.post(
                                url,
                                params=params,
                                headers=headers,
                                json=payload,
                                timeout=20
                            )
                            
                            if response.status_code != 200:
                                logger.error(f"[Jimeng] aigc_draft/generate失败: 状态码={response.status_code}, 响应={response.text}")
                                e_context['reply'] = Reply(ReplyType.TEXT, f"生成图片失败，请重试: HTTP {response.status_code}")
                                return
                                
                            result = response.json()
                            logger.info(f"[Jimeng] aigc_draft/generate响应: {json.dumps(result, ensure_ascii=False)}")
                            
                            if result.get("ret") != "0":
                                logger.error(f"[Jimeng] aigc_draft/generate API错误: {result}")
                                error_msg = result.get('errmsg', '未知错误')
                                error_code = result.get('ret', '未知错误代码')
                                e_context['reply'] = Reply(ReplyType.TEXT, f"生成图片API错误: {error_msg} (代码: {error_code})")
                                return
                                
                            # 获取图片URL
                            data = result.get("data", {})
                            resources = data.get("aigc_data", {}).get("resources", [])
                            
                            if not resources:
                                logger.error(f"[Jimeng] 响应中未包含resources: {result}")
                                e_context['reply'] = Reply(ReplyType.TEXT, "生成图片失败，未获取到图片资源")
                                return
                            
                            # 添加详细日志，记录完整的响应结构
                            logger.debug(f"[Jimeng] 响应resources结构: {json.dumps(resources, ensure_ascii=False)}")
                            logger.debug(f"[Jimeng] 完整响应数据: {json.dumps(result, ensure_ascii=False)}")
                            
                            # 从resources中获取图片URL
                            image_url = None
                            for resource in resources:
                                if resource.get("type") == "image" and resource.get("image_info"):
                                    image_url = resource.get("image_info", {}).get("image_url")
                                    logger.info(f"[Jimeng] 从resources中获取到图片URL: {image_url}")
                                    if image_url:
                                        break
                            
                            if not image_url:
                                # 获取task_id，查询任务状态
                                task_id = data.get("aigc_data", {}).get("task", {}).get("task_id")
                                status = data.get("aigc_data", {}).get("status")
                                history_id = data.get("aigc_data", {}).get("history_record_id")
                                
                                # 记录获取到的任务信息
                                logger.info(f"[Jimeng] 获取到任务信息: task_id={task_id}, status={status}, history_id={history_id}")
                                
                                # 检查是否返回的是原始图片URL
                                if image_url and image_uri in image_url:
                                    logger.warning(f"[Jimeng] 检测到返回的是原始上传图片URL: {image_url}")
                                    
                                    # 如果有history_id，尝试通过它获取结果
                                    if history_id:
                                        logger.info(f"[Jimeng] 等待10秒后通过history_id={history_id}获取结果")
                                        # 等待几秒让服务器有时间处理
                                        time.sleep(10)
                                        e_context['reply'] = Reply(ReplyType.TEXT, "图片正在生成中，请稍后...")
                                        
                                        # 使用history_id获取结果
                                        success, result_url = self.get_generation_result(history_id)
                                        if success and result_url:
                                            logger.info(f"[Jimeng] 成功获取到生成图片URL: {result_url}")
                                            # 发送生成的图片
                                            image_reply = Reply(ReplyType.IMAGE_URL, result_url)
                                            e_context["channel"].send(image_reply, e_context["context"])
                                            
                                            # 查询并显示剩余积分
                                            credit_info = self.token_manager.get_credit()
                                            if credit_info:
                                                credit_text = f"图片生成成功！当前账号剩余积分: {credit_info['total_credit']}"
                                                e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                                            return
                                        else:
                                            # 如果未能获取结果，返回等待消息
                                            e_context['reply'] = Reply(ReplyType.TEXT, "图片生成需要一些时间，请稍后再次尝试命令查看结果")
                                            return
                                    else:
                                        e_context['reply'] = Reply(ReplyType.TEXT, "图片正在生成中，请稍后再次尝试命令查看结果")
                                        return
                                elif not image_url:
                                    logger.error(f"[Jimeng] 未找到图片URL或task_id: {result}")
                                    e_context['reply'] = Reply(ReplyType.TEXT, "生成图片失败，未获取到图片URL")
                                    return
                                
                            logger.info(f"[Jimeng] 成功获取到图片URL: {image_url}")
                            
                            # 发送生成的图片
                            image_reply = Reply(ReplyType.IMAGE_URL, image_url)
                            e_context["channel"].send(image_reply, e_context["context"])
                            
                            # 查询并显示剩余积分
                            credit_info = self.token_manager.get_credit()
                            if credit_info:
                                credit_text = f"图片生成成功！当前账号剩余积分: {credit_info['total_credit']}"
                                e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                        except Exception as e:
                            logger.error(f"[Jimeng] Error generating reference image: {str(e)}")
                            e_context['reply'] = Reply(ReplyType.TEXT, f"生成图片失败: {str(e)}")
                    except Exception as e:
                        logger.error(f"[Jimeng] 获取图片内容时出错: {str(e)}")
                        e_context['reply'] = Reply(ReplyType.TEXT, f"获取图片内容失败: {str(e)}")
                    finally:
                        # 删除临时文件
                        try:
                            if os.path.exists(temp_file):
                                os.remove(temp_file)
                                logger.debug(f"[Jimeng] Removed temp file: {temp_file}")
                        except Exception as e:
                            logger.warning(f"[Jimeng] Failed to remove temp file: {str(e)}")
                    
                    return
                # 处理参考图生成视频逻辑
                elif type == "video":
                    # 检查当前账号积分
                    required_credit = 5  # 默认S2.0模型需要5积分
                    if model == "p2.0p":
                        required_credit = 20  # P2.0P模型需要20积分
                        
                    # 获取当前账号信息
                    account = self.token_manager.get_current_account()
                    if not account:
                        e_context['reply'] = Reply(ReplyType.TEXT, "获取当前账号信息失败")
                        return
                        
                    # 检查当前账号积分
                    credit_info = self.token_manager.get_credit()
                    if not credit_info or credit_info['total_credit'] < required_credit:
                        e_context['reply'] = Reply(ReplyType.TEXT, f"当前账号积分不足，无法生成视频（需要{required_credit}积分）")
                        return
                        
                    # 获取模型的中文名称
                    model_name = self.config.get("video_models", {}).get(model, {}).get("name", model)
                    
                    # 发送等待提示
                    wait_reply = Reply(ReplyType.TEXT, f"正在使用{model_name}模型生成视频......")
                    e_context["channel"].send(wait_reply, e_context["context"])
                    
                    # 获取图片内容
                    try:
                        # 保存临时图片
                        temp_file = self._save_temp_image(content, session_id, context)
                        if not temp_file:
                            e_context['reply'] = Reply(ReplyType.TEXT, "保存图片失败，请重试")
                            return
                        
                        logger.info(f"[Jimeng] Successfully saved temp image: {temp_file}")
                        
                        # 上传图片并生成视频
                        success, result = self.video_generator.upload_image_and_generate_video(temp_file, prompt, model)
                        
                        # 删除临时文件
                        try:
                            if os.path.exists(temp_file):
                                os.remove(temp_file)
                                logger.debug(f"[Jimeng] Removed temp file: {temp_file}")
                        except Exception as e:
                            logger.warning(f"[Jimeng] Failed to remove temp file: {str(e)}")
                        
                        if success:
                            # 直接发送视频 URL
                            video_reply = Reply(ReplyType.VIDEO_URL, result)
                            e_context["channel"].send(video_reply, e_context["context"])
                            
                            # 查询并显示剩余积分
                            credit_info = self.token_manager.get_credit()
                            if credit_info:
                                credit_text = f"视频生成成功！当前账号剩余积分: {credit_info['total_credit']}"
                                e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                        else:
                            e_context['reply'] = Reply(ReplyType.TEXT, f"生成视频失败: {result}")
                        return
                    except Exception as e:
                        logger.error(f"[Jimeng] Error processing image: {str(e)}")
                        e_context['reply'] = Reply(ReplyType.TEXT, f"处理图片失败: {str(e)}")
                        return
            return
            
        e_context.action = EventAction.BREAK_PASS
        
        # 处理参考图生成视频命令
        if content.startswith("即梦参考图v") or content.startswith("即梦参考图V"):
            # 检查当前账号积分
            required_credit = 5  # 默认S2.0模型需要5积分
            if "p2.0p" in content.lower():
                required_credit = 20  # P2.0P模型需要20积分
                
            # 获取当前账号信息
            account = self.token_manager.get_current_account()
            if not account:
                e_context['reply'] = Reply(ReplyType.TEXT, "获取当前账号信息失败")
                return
                
            # 检查当前账号积分
            credit_info = self.token_manager.get_credit()
            if not credit_info or credit_info['total_credit'] < required_credit:
                e_context['reply'] = Reply(ReplyType.TEXT, f"当前账号积分不足，无法生成视频（需要{required_credit}积分）")
                return
                
            content = content[6:].strip()  # 去除"即梦参考图v"
            if not content:
                e_context['reply'] = Reply(ReplyType.TEXT, "请输入视频描述词")
                return
                
            # 解析命令参数（参考图模式）
            prompt, model = self._parse_video_command(content, True)
            
            # 获取模型的中文名称 - 确保从config中获取正确模型名称
            model_name = self.config.get("video_models", {}).get(model, {}).get("name", model)
            
            # 添加调试日志
            logger.debug(f"[Jimeng] 解析参考图视频参数: prompt='{prompt}', model='{model}', model_name='{model_name}'")
            
            # 记录等待状态
            self.waiting_for_image[session_id] = {
                "prompt": prompt,
                "model": model,
                "type": "video"  # 指定类型为视频
            }
            
            # 发送等待提示
            e_context['reply'] = Reply(ReplyType.TEXT, f"请发送一张图片，将使用{model_name}模型生成视频")
            return

        # 新增：处理参考图生图命令
        elif content.startswith("即梦参考图"):
            # 检查当前账号积分
            required_credit = 1  # 图片生成需要1积分
            
            # 获取当前账号信息
            account = self.token_manager.get_current_account()
            if not account:
                e_context['reply'] = Reply(ReplyType.TEXT, "获取当前账号信息失败")
                return
                
            # 检查当前账号积分
            credit_info = self.token_manager.get_credit()
            if not credit_info or credit_info['total_credit'] < required_credit:
                e_context['reply'] = Reply(ReplyType.TEXT, f"当前账号积分不足，无法生成图片（需要{required_credit}积分）")
                return
                
            content = content[6:].strip()  # 去除"即梦参考图"
            if not content:
                e_context['reply'] = Reply(ReplyType.TEXT, "请输入图片描述词")
                return
                
            # 解析命令参数
            prompt, model, ratio = self._parse_command(content)
            
            # 检查是否指定了控制网络模式
            control_net = "none"  # 默认使用none模式
            strength = 1.0
            
            # 检查是否包含特定的控制网络标记
            if "景深" in content or "canny" in content.lower():
                control_net = "canny"
                strength = 0.6
                logger.info(f"[Jimeng] 检测到使用景深(canny)模式，强度:{strength}")
            elif "线稿" in content or "线条" in content or "黑白" in content or "sketch" in content.lower():
                control_net = "lineart"
                strength = 0.8
                logger.info(f"[Jimeng] 检测到使用线稿(lineart)模式，强度:{strength}")
            elif "姿势" in content or "姿态" in content or "pose" in content.lower():
                control_net = "openpose"
                strength = 0.8
                logger.info(f"[Jimeng] 检测到使用姿势(openpose)模式，强度:{strength}")
            else:
                logger.info(f"[Jimeng] 使用默认(none)模式，强度:{strength}")
            
            # 强制使用 2.0 Pro 模型，因为参考图生图只支持此模型
            model = "2.0pro"
            model_name = "2.0 Pro"
            
            # 记录等待状态
            self.waiting_for_image[session_id] = {
                "prompt": prompt,
                "model": model,
                "ratio": ratio,
                "type": "img2img",  # 指定类型为图生图
                "control_net": control_net,
                "strength": strength
            }
            
            logger.info(f"[Jimeng] 等待用户发送参考图片，参数: prompt='{prompt}', model='{model}', control_net='{control_net}', strength='{strength}'")
            
            # 发送等待提示
            control_net_name = {
                "none": "普通模式",
                "canny": "景深模式",
                "lineart": "线稿模式",
                "openpose": "姿势模式"
            }.get(control_net, "普通模式")
            
            e_context['reply'] = Reply(ReplyType.TEXT, f"请发送一张图片，将使用{model_name}模型和{ratio}比例生成图片（{control_net_name}）")
            return

        # 处理文字生成视频命令
        if content.startswith("即梦v") or content.startswith("即梦V"):
            # 检查当前账号积分
            required_credit = 5  # 默认S2.0模型需要5积分
            if "p2.0p" in content.lower():
                required_credit = 20  # P2.0P模型需要20积分
                
            # 获取当前账号信息
            account = self.token_manager.get_current_account()
            if not account:
                e_context['reply'] = Reply(ReplyType.TEXT, "获取当前账号信息失败")
                return
                
            # 检查当前账号积分
            credit_info = self.token_manager.get_credit()
            if not credit_info or credit_info['total_credit'] < required_credit:
                e_context['reply'] = Reply(ReplyType.TEXT, f"当前账号积分不足，无法生成视频（需要{required_credit}积分）")
                return

            content = content[3:].strip()  # 去除"即梦v"
            if not content:
                e_context['reply'] = Reply(ReplyType.TEXT, "请输入视频描述词")
                return
                
            # 解析命令参数（文字生成视频模式）
            prompt, model, ratio = self._parse_video_command(content, False)
            
            # 获取模型的中文名称
            model_name = self.config.get("video_models", {}).get(model, {}).get("name", model)
            
            # 发送等待提示
            wait_reply = Reply(ReplyType.TEXT, f"即梦正在使用{model_name}模型，{ratio}比例生成视频，请稍候......")
            e_context["channel"].send(wait_reply, e_context["context"])

            # 直接调用generate_video方法生成视频
            success, result = self.generate_video(prompt, None, 5000, model, ratio)
            if success:
                # 发送视频URL
                video_reply = Reply(ReplyType.VIDEO_URL, result)
                e_context["channel"].send(video_reply, e_context["context"])

                # 查询并显示剩余积分
                credit_info = self.token_manager.get_credit()
                if credit_info:
                    credit_text = f"视频生成成功！当前账号剩余积分: {credit_info['total_credit']}"
                    e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
            else:
                e_context['reply'] = Reply(ReplyType.TEXT, f"生成视频失败: {result}")
            return
            
        # 处理放大图片命令
        if content.startswith("j放大"):
            try:
                parts = content.split()
                if len(parts) != 3:  # 确保命令格式正确
                    e_context['reply'] = Reply(ReplyType.TEXT, "命令格式错误，正确格式：j放大 [图片ID] [序号]")
                    return
                    
                img_id = parts[1]
                index = int(parts[2])
                
                # 获取原图
                image_content, error = self.api_client.get_original_image(img_id, index)
                if error:
                    e_context['reply'] = Reply(ReplyType.TEXT, error)
                else:
                    if isinstance(image_content, str) and (image_content.startswith('http://') or image_content.startswith('https://')):
                        e_context['reply'] = Reply(ReplyType.IMAGE_URL, image_content)
                    else:
                        e_context['reply'] = Reply(ReplyType.IMAGE, image_content)
            except ValueError:
                e_context['reply'] = Reply(ReplyType.TEXT, "序号必须是数字")
            except Exception as e:
                logger.error(f"[Jimeng] Error getting original image: {str(e)}")
                e_context['reply'] = Reply(ReplyType.TEXT, f"获取原图失败: {str(e)}")
            return
            
        # 处理图片转视频命令
        if content.startswith("jm"):
            try:
                parts = content.split(" ")
                if len(parts) < 3:
                    e_context['reply'] = Reply(ReplyType.TEXT, "命令格式错误，正确格式：jm [图片ID] [序号] [可选:提示词] [可选:模型] [可选:比例]")
                    return
                    
                # 检查当前账号积分
                required_credit = 5  # 默认S2.0模型需要5积分
                if len(parts) > 3 and ("s2.0p" in parts[3].lower() or "p2.0p" in parts[3].lower()):
                    required_credit = 20  # P2.0P模型需要20积分
                    
                # 获取当前账号信息
                account = self.token_manager.get_current_account()
                if not account:
                    e_context['reply'] = Reply(ReplyType.TEXT, "获取当前账号信息失败")
                    return
                    
                # 检查当前账号积分
                credit_info = self.token_manager.get_credit()
                if not credit_info or credit_info['total_credit'] < required_credit:
                    e_context['reply'] = Reply(ReplyType.TEXT, f"当前账号积分不足，无法生成视频（需要{required_credit}积分）")
                    return
                    
                img_id = parts[1]
                index = int(parts[2])
                remaining_content = " ".join(parts[3:])
                
                # 解析剩余参数
                prompt, model, ratio = self._parse_video_command(remaining_content)
                
                # 获取模型的中文名称
                model_name = self.config.get("video_models", {}).get(model, {}).get("name", model)
                
                # 发送等待提示
                wait_reply = Reply(ReplyType.TEXT, f"即梦正在使用{model_name}模型，{ratio}比例将图片转换为视频，请稍后......")
                e_context["channel"].send(wait_reply, e_context["context"])
                
                # 转换图片为视频
                success, result = self.image_to_video(img_id, index, prompt, model)
                if success:
                    e_context['reply'] = Reply(ReplyType.VIDEO_URL, result)
                    
                    # 查询并显示剩余积分
                    credit_info = self.token_manager.get_credit()
                    if credit_info:
                        credit_text = f"视频生成成功！当前账号剩余积分: {credit_info['total_credit']}"
                        e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                else:
                    e_context['reply'] = Reply(ReplyType.TEXT, result)
            except Exception as e:
                logger.error(f"[Jimeng] Error converting image to video: {str(e)}")
                e_context['reply'] = Reply(ReplyType.TEXT, f"图片转视频失败: {str(e)}")
            return
        
        # 处理图片生成命令
        try:
            # 检查账号积分
            account = self.token_manager.find_account_with_sufficient_credit(1)  # 图片生成需要1积分
            if not account:
                e_context['reply'] = Reply(ReplyType.TEXT, "所有账号积分不足，无法生成图片")
                return
                
            # 移除命令前缀"即梦"
            if content.lower().startswith("即梦"):
                content = content[2:].strip()  # 去除"即梦"前缀
            
            # 解析命令参数
            prompt, model, ratio = self._parse_command(content)
            
            # 发送等待提示
            wait_reply = Reply(ReplyType.TEXT, f"即梦正在使用 {model} 模型以 {ratio} 比例生成图片，请稍候......")
            e_context["channel"].send(wait_reply, e_context["context"])
            
            # 生成图片
            result = self.api_client.generate_image(prompt, model=model, ratio=ratio)
            if not result:
                if model == "3.0":
                    logger.error("[Jimeng] 3.0模型生成失败，可能是由于分辨率或资源限制")
                    return None, "3.0模型生成失败，请检查网络连接或尝试其他模型"
                e_context['reply'] = Reply(ReplyType.TEXT, "图片生成失败，请稍后重试")
                return
            
            # 存储图片信息
            img_id = str(int(time.time()))
            self.image_storage.store_image(
                img_id,
                result["urls"],
                metadata={
                    "prompt": prompt,  # 使用解析后的提示词保存，而不是带有前缀的原始命令
                    "type": "generate"
                }
            )
            
            # 发送图片
            if len(result["urls"]) >= 4:
                image_file = self.image_processor.combine_images(result["urls"][:4])
                if image_file:
                    image_reply = Reply(ReplyType.IMAGE, image_file)
                    e_context["channel"].send(image_reply, e_context["context"])
                    image_file.close()
                    
                    # 删除临时拼接图片
                    temp_dir = os.path.join(os.path.dirname(__file__), "temp")
                    for file in os.listdir(temp_dir):
                        if file.startswith("combined_"):
                            try:
                                os.remove(os.path.join(temp_dir, file))
                                logger.debug(f"[Jimeng] Removed temp file: {file}")
                            except Exception as e:
                                logger.warning(f"[Jimeng] Failed to remove temp file {file}: {e}")
                    
                    # 发送帮助文本
                    help_text = f"图片生成成功！\n图片ID: {img_id}\n使用'j放大 {img_id} 序号'可以查看原图"
                    e_context["channel"].send(Reply(ReplyType.TEXT, help_text), e_context["context"])
                    
                    # 查询并显示剩余积分
                    credit_info = self.token_manager.get_credit()
                    if credit_info:
                        credit_text = f"当前账号剩余积分: {credit_info['total_credit']}"
                        e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                        
                    e_context['reply'] = None  # 已经发送过图片，不需要再设置reply
                else:
                    # 如果合并失败，发送单张图片
                    for url in result["urls"]:
                        image_reply = Reply(ReplyType.IMAGE_URL, url)
                        e_context["channel"].send(image_reply, e_context["context"])
                        
                    # 发送帮助文本
                    help_text = f"图片生成成功！\n图片ID: {img_id}\n使用'j放大 {img_id} 序号'可以查看原图"
                    text_reply = Reply(ReplyType.TEXT, help_text)
                    e_context["channel"].send(text_reply, e_context["context"])
                    
                    # 查询并显示剩余积分
                    credit_info = self.token_manager.get_credit()
                    if credit_info:
                        credit_text = f"当前账号剩余积分: {credit_info['total_credit']}"
                        e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                        
                    e_context['reply'] = None  # 已经发送过图片，不需要再设置reply
            else:
                # 直接发送单张图片的URL
                for url in result["urls"]:
                    image_reply = Reply(ReplyType.IMAGE_URL, url)
                    e_context["channel"].send(image_reply, e_context["context"])
                    
                # 发送帮助文本
                help_text = f"图片生成成功！\n图片ID: {img_id}\n使用'j放大 {img_id} 序号'可以查看原图"
                text_reply = Reply(ReplyType.TEXT, help_text)
                e_context["channel"].send(text_reply, e_context["context"])
                
                # 查询并显示剩余积分
                credit_info = self.token_manager.get_credit()
                if credit_info:
                    credit_text = f"当前账号剩余积分: {credit_info['total_credit']}"
                    e_context["channel"].send(Reply(ReplyType.TEXT, credit_text), e_context["context"])
                    
                e_context['reply'] = None  # 已经发送过图片，不需要再设置reply
            
            # 在生成图片时添加模型信息到日志
            logger.info(f"[Jimeng] Generating image with model {model} ({self.config['params']['models'][model]['name']})")
            
        except Exception as e:
            logger.error(f"[Jimeng] Error generating image with model {model}: {str(e)}")
            e_context['reply'] = Reply(ReplyType.TEXT, f"图片生成失败: {str(e)}")

    def get_help_text(self, **kwargs):
        commands = self.config.get('commands', {})
        draw_command = commands.get('draw', '即梦') if isinstance(commands, dict) else '即梦'
        
        help_text = "即梦AI绘画和视频生成插件使用说明：\n\n"
        
        # 基本命令说明
        help_text += "基本命令：\n"
        help_text += f"1. 生成图片: '{draw_command} [描述] [模型] [比例]'\n"
        help_text += f"2. 查看原图: 'j放大 [图片ID] [序号]'\n"
        help_text += f"3. 生成视频: '{draw_command}v [描述] [模型] [比例]'\n"
        help_text += f"4. 参考图生成视频: '{draw_command}参考图v [描述] [模型]'\n"
        help_text += f"5. 参考图生成图片: '{draw_command}参考图 [描述] [模型] [比例]'\n"
        help_text += f"6. 查询积分: 'j积分'\n"
        help_text += f"7. 领取积分: 'j积分领取'\n\n"
        
        help_text += "示例：\n"
        help_text += f"1. {draw_command} 一只可爱的猫咪 3.0 16:9  # 使用3.0模型和16:9比例\n"
        help_text += f"2. j放大 1704067890 2  # 查看ID为1704067890的第2张原图\n"
        help_text += f"3. {draw_command}v 现代美少女在海边 s2.0 16:9  # 生成视频，使用s2.0模型和16:9比例\n"
        help_text += f"4. {draw_command}参考图v 人物表情慢慢变沮丧 p2.0p  # 使用参考图和p2.0p模型生成视频\n"
        help_text += f"5. {draw_command}参考图 将图片转为卡通风格 2.0pro 1:1  # 基于参考图生成新图像\n"
        help_text += f"6. {draw_command}参考图 景深 将人物突出在模糊的背景中  # 使用景深模式处理图像\n"
        help_text += f"7. {draw_command}参考图 线稿 将照片转为黑白线条风格  # 使用线稿模式处理图像\n"
        help_text += f"8. j积分  # 查看当前账号积分\n"
        help_text += f"9. j积分领取  # 领取每日积分\n\n"
        
        # 支持的视频模型
        help_text += "支持的视频模型：\n"
        video_models = self.config.get("video_models", {})
        for key, model in video_models.items():
            help_text += f"- {model.get('name', key)}\n"
        help_text += "\n"
        
        # 支持的视频比例（仅用于文字生成视频）
        help_text += "支持的视频比例（仅用于文字生成视频）：\n"
        ratios = self.config.get("video_ratios", {})
        for ratio in ratios:
            help_text += f"- {ratio}\n"
        help_text += "\n"
        
        # 使用说明
        help_text += "注意事项：\n"
        help_text += "1. 参考图生成视频时不需要指定比例，会自动使用图片原始比例\n"
        help_text += "2. 文字生成视频时可以指定模型和比例\n"
        help_text += "3. 参考图生成图片功能默认使用控制网络，可实现风格迁移和图像变换\n"
        help_text += "4. 参考图生成图片支持多种模式：普通、景深、线稿、姿势\n"
        help_text += "5. 模型参数直接跟在提示词后面，不需要加-m等标识\n"
        help_text += "6. 支持的模型有：s2.0、p2.0p等(视频)，2.1、3.0等(图片)\n"
        help_text += "7. 每日可领取积分，记得及时领取哦\n"
        
        help_text += "参考图生图模式说明：\n"
        help_text += "- 普通模式：保留原图结构和内容，应用新的风格和细节\n"
        help_text += "- 景深模式：保留主体，但会使背景模糊化处理\n"
        help_text += "- 线稿模式：将图像转为黑白线条风格\n"
        help_text += "- 姿势模式：保留人物姿势，但改变形象和风格\n\n"
        
        help_text += "支持的图片模型：\n"
        help_text += "- 3.0（默认）：影视质感，文字更准，直出2k高清图\n"
        help_text += "- 2.1：稳定的结构和更强的影视质感\n"
        help_text += "- 2.0 Pro：真实的照片质感，也是参考图生图功能唯一支持的模型\n"
        
        return help_text

    def send_reply(self, e_context: EventContext, reply: Reply):
        """发送回复"""
        e_context['reply'] = reply
        e_context.action = EventAction.BREAK_PASS

    def send_image_and_text(self, e_context: EventContext, image_content, text_content):
        """发送图片和文本消息"""
        # 先发送图片
        image_reply = Reply(ReplyType.IMAGE, image_content)
        self.send_reply(e_context, image_reply)
        
        # 再发送文本
        if text_content:
            text_reply = Reply(ReplyType.TEXT, text_content)
            self.send_reply(e_context, text_reply)

    def image_to_video(self, img_id, index, prompt="", model="s2.0"):
        """将图片转换为视频
        Args:
            img_id: 图片ID
            index: 图片序号
            prompt: 自定义提示词，如果为空则使用原始提示词
            model: 视频模型
        Returns:
            tuple: (success, result)
        """
        try:
            # 从数据库获取图片信息
            image_info = self.image_storage.get_image(img_id)
            if not image_info:
                return False, "未找到指定图片"
                
            # 获取图片URL
            urls = image_info.get("urls", [])
            if not urls or len(urls) <= index - 1:
                return False, "图片序号无效"
                
            image_url = urls[index - 1]
            logger.debug(f"[Jimeng] Using image URL: {image_url}")
            
            # 获取提示词，优先使用自定义提示词
            if not prompt:
                prompt = image_info.get("metadata", {}).get("prompt", "")
            if not prompt:
                prompt = "一个美丽的场景"  # 默认提示词
            logger.debug(f"[Jimeng] Using prompt: {prompt}")
            
            # 从URL中提取image_uri
            image_uri = image_url.split("?")[0].split("/")[-1]
            # 正确构建完整的image_uri
            full_image_uri = f"tos-cn-i-tb4s082cfz/{image_uri.split('~')[0]}"
            
            # 构建first_frame_info
            first_frame_info = {
                "width": 1024,  # 默认尺寸
                "height": 1024,  # 默认尺寸
                "image_uri": full_image_uri
            }
            
            # 调用generate_video方法，传递模型参数
            return self.generate_video(prompt, first_frame_info, 5000, model)
            
        except Exception as e:
            logger.error(f"[Jimeng] Error converting image to video: {str(e)}")
            return False, f"图片转视频失败: {str(e)}"

    def generate_video(self, prompt, first_frame_info=None, duration_ms=5000, model="s2.0", ratio="4:3"):
        """生成视频"""
        try:
            # 打印当前账号状态
            current_account_number = self.token_manager.current_account_index + 1
            logger.info(f"[Jimeng] 开始生成视频任务，使用账号{current_account_number}")
            
            # 获取当前账号信息 - 移除force_refresh参数
            account = self.token_manager.get_current_account()
            if not account:
                logger.error("[Jimeng] 获取当前账号信息失败")
                return None, "获取当前账号信息失败"
                
            # 检查当前账号积分
            credit_info = self.token_manager.get_credit()
            if not credit_info:
                logger.error("[Jimeng] 获取积分信息失败")
                return None, "获取积分信息失败"
                
            required_credit = 20 if model == "p2.0p" else 5
            logger.info(f"[Jimeng] 账号{current_account_number}当前积分: {credit_info['total_credit']}, 需要积分: {required_credit}")
            
            if credit_info['total_credit'] < required_credit:
                error_msg = f"账号{current_account_number}积分不足，当前积分: {credit_info['total_credit']}, 需要积分: {required_credit}"
                logger.error(f"[Jimeng] {error_msg}")
                return None, error_msg
                
            logger.info(f"[Jimeng] 使用账号{current_account_number}生成视频，当前积分：{credit_info['total_credit']}")
            
            # 生成唯一的submit_id
            submit_id = str(uuid.uuid4())
            
            # 生成当前时间戳
            current_timestamp = int(time.time())
            
            # 获取当前账号的web_id
            web_id = account.get('web_id', str(random.random() * 999999999999999999 + 7000000000000000000))
            
            # 生成msToken (基于账号信息和时间戳)
            msToken_base = f"{account['sessionid']}_{current_timestamp}"
            msToken = base64.b64encode(msToken_base.encode()).decode()
            
            # 生成sign (基于账号信息和时间戳)
            sign_base = f"{account['sessionid']}_{current_timestamp}_video"
            sign = hashlib.md5(sign_base.encode()).hexdigest()
            
            # 生成a_bogus (基于账号信息和时间戳)
            a_bogus_base = f"{account['sessionid']}_{current_timestamp}_bogus"
            a_bogus = base64.b64encode(a_bogus_base.encode()).decode()
            
            # 根据模型设置不同的参数
            if model == "p2.0p":
                model_params = {
                    "model_req_key": "dreamina_ailab_generate_video_model_v1.4",
                    "video_mode": 2,
                    "fps": 12,
                    "template_id": "",
                    "lens_motion_type": "0",
                    "motion_speed": "0",
                    "ending_control": "0",
                    "benefit_type": "basic_video_operation_lab_14",
                    "feature_entrance": "to_video",
                    "feature_entrance_detail": "to_video-text_to_video"
                }
            else:  # s2.0 或 s2.0p
                # 区分s2.0和s2.0p
                if model == "s2.0p":
                    model_req_key = "dreamina_ic_generate_video_model_vgfm1.0"
                    benefit_type = "basic_video_operation_vgfm"
                else:  # s2.0
                    model_req_key = "dreamina_ic_generate_video_model_vgfm_lite"
                    benefit_type = "basic_video_operation_vgfm_lite"
                    
                model_params = {
                    "model_req_key": model_req_key,
                    "video_mode": 2,  # 文生视频应该用2
                    "fps": 24,
                    "template_id": "",
                    "lens_motion_type": "",
                    "motion_speed": "",
                    "ending_control": "",
                    "benefit_type": benefit_type,
                    "feature_entrance": "to_video",
                    "feature_entrance_detail": "to_video-text_to_video"
                }
            
            # 构建 babi_param - 修改feature_key为text_to_video
            babi_param = {
                "scenario": "image_video_generation",
                "feature_key": "text_to_video",
                "feature_entrance": model_params["feature_entrance"],
                "feature_entrance_detail": model_params["feature_entrance_detail"]
            }
            
            # 构建URL参数
            url_params = {
                "aid": "513695",
                "babi_param": json.dumps(babi_param),
                "device_platform": "web",
                "region": "CN",
                "web_id": web_id,
                "msToken": msToken,
                "a_bogus": a_bogus
            }
            
            # 准备请求数据 - 调整文生视频的请求结构
            generate_video_payload = {
                "submit_id": submit_id,
                "task_extra": json.dumps({
                    "promptSource": "custom",
                    "originSubmitId": str(uuid.uuid4()),
                    "isDefaultSeed": 1,
                    "originTemplateId": "",
                    "imageNameMapping": {},
                    "isUseAiGenPrompt": False,
                    "batchNumber": 1
                }),
                "http_common_info": {
                    "aid": "513695"
                },
                "input": {
                    "video_aspect_ratio": ratio,
                    "seed": random.randint(1000000000, 9999999999),
                    "video_gen_inputs": [
                        {
                            "prompt": prompt,
                            "fps": model_params["fps"],
                            "duration_ms": duration_ms,
                            "video_mode": model_params["video_mode"],
                            "template_id": model_params["template_id"]
                        }
                    ],
                    "priority": 0,
                    "model_req_key": model_params["model_req_key"]
                },
                "mode": "workbench",
                "history_option": {},
                "commerce_info": {
                    "resource_id": "generate_video",
                    "resource_id_type": "str",
                    "resource_sub_type": "aigc",
                    "benefit_type": model_params["benefit_type"]
                },
                "client_trace_data": {}
            }

            # 使用当前账号的sessionid构建cookie
            cookie = f"sessionid={account['sessionid']}; sessionid_ss={account['sessionid']}; _tea_web_id={web_id}; web_id={web_id}; _v2_spipe_web_id={web_id}"
            
            # 使用最新的token发送请求
            headers = {
                "Content-Type": "application/json",
                "User-Agent": self.user_agent,
                "appid": "513695",
                "sign": sign,
                "sign-ver": "1",
                "cookie": cookie,
                "device-time": str(current_timestamp),
                "msToken": msToken,
                "x-bogus": a_bogus
            }
            
            # 打印完整的请求信息
            logger.info("[Jimeng] 生成视频请求信息:")
            logger.info(f"当前使用账号: {current_account_number}")
            logger.info(f"URL: https://jimeng.jianying.com/mweb/v1/generate_video")
            logger.info(f"URL Params: {json.dumps(url_params, ensure_ascii=False, indent=2)}")
            logger.info(f"Query Params: {json.dumps(generate_video_payload, ensure_ascii=False, indent=2)}")
            logger.info(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
            
            response = requests.post(
                "https://jimeng.jianying.com/mweb/v1/generate_video",
                params=url_params,
                headers=headers,
                json=generate_video_payload,
                timeout=10
            )
            
            # 打印响应信息
            logger.info(f"[Jimeng] Response Status: {response.status_code}")
            logger.info(f"[Jimeng] Response Headers: {json.dumps(dict(response.headers), ensure_ascii=False, indent=2)}")
            logger.info(f"[Jimeng] Response Body: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            
            if response.status_code != 200:
                logger.error(f"[Jimeng] Video generation request failed: HTTP {response.status_code} - {response.text}")
                return None, f"视频生成请求失败，状态码：{response.status_code}"
                
            result = response.json()
            if result.get("ret") != "0":
                logger.error(f"[Jimeng] API error: {result}")
                return None, f"API错误：{result.get('msg', '未知错误')}"
                
            task_id = result.get("data", {}).get("aigc_data", {}).get("task", {}).get("task_id")
            if not task_id:
                logger.error(f"[Jimeng] Task ID not found in response: {result}")
                return None, "未获取到任务ID"
                
            # 检查视频生成状态
            success, result = self.check_video_status(task_id)
            if not success:
                return None, result
                
            return True, result
            
        except Exception as e:
            logger.error(f"[Jimeng] Error generating video: {str(e)}")
            return None, f"视频生成失败: {str(e)}"

    def _parse_command(self, content):
        """解析图片生成命令参数（优先匹配末尾有效参数）
        Args:
            content: 命令内容，如 "一只猫 2.1 4:3"
        Returns:
            tuple: (prompt, model, ratio)
        """
        parts = content.strip().split()
        model = self.config.get("params", {}).get("default_model", "3.0")
        ratio = self.config.get("params", {}).get("default_ratio", "1:1")

        # 从后向前匹配有效参数
        valid_models = list(self.config.get("params", {}).get("models", {}).keys()) + ["20", "21", "20p", "30", "xlpro"]
        valid_ratios = list(self.config.get("params", {}).get("ratios", {}).keys())
    
        # 初始化参数匹配状态
        found_ratio = None
        found_model = None

        # 检查最后两个部分是否为有效参数
        for i in range(len(parts)-1, -1, -1):
            part = parts[i].lower().replace("：", ":")
        
            # 如果还未找到比例参数且当前部分是有效比例
            if not found_ratio and part in valid_ratios:
                found_ratio = part
                continue
            
            # 如果还未找到模型参数且当前部分是有效模型
            if not found_model and any(p in part for p in valid_models):
                found_model = part
                continue
            
            # 如果已找到两个参数或不再匹配，则终止
            if found_ratio and found_model:
                break

        # 更新模型和比例
        if found_model:
            model = found_model
            # 处理简写
            model = (model.replace("20", "2.0")
                        .replace("21", "2.1")
                        .replace("30", "3.0")
                        .replace("xlpro", "xl"))
        if found_ratio:
            ratio = found_ratio

        # 合并剩余的parts作为提示词
        if found_model or found_ratio:
            prompt = " ".join(parts[:len(parts) - ((1 if found_model else 0) + (1 if found_ratio else 0))])
        else:
            prompt = content.strip()

        # 记录解析结果
        logger.debug(f"[Jimeng] Parsed command: prompt='{prompt}', model='{model}', ratio='{ratio}'")
        
        return prompt, model, ratio

    def on_stop_plugin(self):
        """清理临时文件"""
        self.api_client.cleanup_temp_files()

    def check_video_status(self, task_id, max_retries=30):
        try:
            # 获取当前账号信息
            current_account_number = self.token_manager.current_account_index + 1
            account = self.token_manager.get_current_account()
            if not account:
                return None, "获取当前账号信息失败"
                
            logger.info(f"[Jimeng] 使用账号{current_account_number}检查视频状态")
            
            # 准备请求参数
            url = "https://jimeng.jianying.com/mweb/v1/mget_generate_task"
            params = {
                "aid": "513695",
                "device_platform": "web",
                "region": "CN",
                "web_id": account.get('web_id', '')
            }
            
            # 准备请求体
            data = {
                "task_id_list": [task_id],
                "http_common_info": {"aid": "513695"}
            }
            
            # 轮询检查视频状态
            for i in range(max_retries):
                try:
                    # 获取token信息
                    token_info = self.token_manager.get_token("/mweb/v1/mget_generate_task")
                    if not token_info:
                        logger.error("[Jimeng] 获取token失败")
                        time.sleep(3)
                        continue
                        
                    # 准备请求头
                    headers = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'app-sdk-version': '48.0.0',
                        'appid': '513695',
                        'appvr': '5.8.0',
                        'content-type': 'application/json',
                        'cookie': token_info["cookie"],
                        'device-time': token_info["device_time"],
                        'sign': token_info["sign"],
                        'sign-ver': '1',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                        'msToken': token_info["msToken"],
                        'x-bogus': token_info["a_bogus"]
                    }
                    
                    response = requests.post(
                        url,
                        params=params,
                        headers=headers,
                        json=data,
                        timeout=10
                    )
                    
                    # 打印响应信息用于调试
                    logger.debug(f"[Jimeng] 状态检查响应: {response.text}")
                    
                    if response.status_code != 200:
                        logger.error(f"[Jimeng] 状态检查失败: HTTP {response.status_code} - {response.text}")
                        time.sleep(3)
                        continue
                        
                    result = response.json()
                    if result.get("ret") != "0":
                        logger.error(f"[Jimeng] API错误: {result}")
                        time.sleep(3)
                        continue
                    
                    # 从task_map获取视频任务信息
                    task_map = result.get("data", {}).get("task_map", {})
                    task_info = task_map.get(task_id)
                    
                    if not task_info:
                        logger.error(f"[Jimeng] 未找到任务信息: {task_id}")
                        time.sleep(3)
                        continue
                    
                    # 获取任务状态
                    status = task_info.get("status")
                    logger.info(f"[Jimeng] 视频状态: {status}")
                    
                    if status == 50:  # 生成成功
                        item_list = task_info.get("item_list", [])
                        if not item_list:
                            logger.error("[Jimeng] 没有找到视频内容")
                            time.sleep(3)
                            continue
                            
                        # 从第一个item中获取视频信息
                        video = item_list[0].get("video", {})
                        if not video:
                            logger.error("[Jimeng] 没有找到视频对象")
                            time.sleep(3)
                            continue
                            
                        # 从transcoded_video中获取origin视频URL
                        transcoded_video = video.get("transcoded_video", {})
                        origin_video = transcoded_video.get("origin", {})
                        
                        video_url = origin_video.get("video_url")
                        if not video_url:
                            logger.error("[Jimeng] 没有找到视频URL")
                            time.sleep(3)
                            continue
                            
                        return True, video_url
                        
                    elif status == 60:  # 生成失败
                        fail_code = task_info.get("task_payload", {}).get("fail_code", "")
                        fail_msg = task_info.get("fail_msg", "未知错误")
                        logger.error(f"[Jimeng] 视频生成失败: {fail_code} - {fail_msg}")
                        return False, f"视频生成失败: {fail_msg}"
                        
                    # 继续等待
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"[Jimeng] 检查状态出错: {str(e)}")
                    time.sleep(3)
                    
            return False, "视频生成超时，请稍后查看结果"
            
        except Exception as e:
            logger.error(f"[Jimeng] 检查视频状态异常: {str(e)}")
            return False, f"检查视频状态失败: {str(e)}"

    def _load_config(self):
        """加载配置文件"""
        config_path = os.path.join(os.path.dirname(__file__), "config.json")
        try:
            if not os.path.exists(config_path):
                # 如果配置文件不存在，复制模板文件
                template_path = os.path.join(os.path.dirname(__file__), "config.json.template")
                if os.path.exists(template_path):
                    import shutil
                    shutil.copy(template_path, config_path)
                    logger.info("[Jimeng] Created config file from template")
                else:
                    raise Exception("Config template file not found")

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info("[Jimeng] Config loaded successfully")
            return config
        except Exception as e:
            logger.error(f"[Jimeng] Failed to load config: {str(e)}")
            # 返回默认配置
            return {
                "video_api": {},
                "storage": {"retention_days": 7},
                "params": {
                    "default_model": "3.0",
                    "default_ratio": "1:1"
                }
            }

    def _parse_video_command(self, content, is_reference_mode=False):
        """解析视频命令参数
        Args:
            content: 命令内容
            is_reference_mode: 是否是参考图模式
        Returns:
            tuple: (prompt, model, ratio) 或 (prompt, model) 取决于模式
        """
        # 首先移除前缀部分
        if content.startswith("即梦参考图v"):
            content = content[6:].strip()
        elif content.startswith("即梦参考图V"):
            content = content[6:].strip()
        elif content.startswith("即梦v"):
            content = content[3:].strip()
        elif content.startswith("即梦V"):
            content = content[3:].strip()
        
        model = self.config.get("default_video_model", "s2.0")
        ratio = self.config.get("default_video_ratio", "4:3")
        
        # 检查是否包含模型标识
        model_found = False
        model_patterns = {
            "s2.0p": ["s2.0p", "-s2.0p", "_s2.0p", "s2.0 pro", "-s2.0 pro"],
            "p2.0p": ["p2.0p", "-p2.0p", "_p2.0p", "p2.0 pro", "-p2.0 pro"],
            "s2.0": ["s2.0", "-s2.0", "_s2.0"]
        }
        
        # 记录原始内容用于调试
        logger.debug(f"[Jimeng] 解析前内容: '{content}'")
        
        # 从长到短排序模型名称，避免部分匹配问题
        for model_key, patterns in sorted(model_patterns.items(), key=lambda x: len(x[0]), reverse=True):
            for pattern in patterns:
                if pattern.lower() in content.lower():
                    model = model_key
                    # 替换模型标识，确保不影响提示词
                    content = re.sub(pattern, "", content, flags=re.IGNORECASE).strip()
                    model_found = True
                    logger.debug(f"[Jimeng] 识别到模型: {model_key}, 匹配模式: {pattern}")
                    break
            if model_found:
                break
        
        # 提取提示词
        prompt = content.strip()
        logger.debug(f"[Jimeng] 解析后: prompt='{prompt}', model='{model}'")
        
        if is_reference_mode:
            return prompt, model
        return prompt, model, ratio

    def check_image_status(self, task_id):
        try:
            # 获取当前账号信息
            current_account_number = self.token_manager.current_account_index + 1
            account = self.token_manager.get_current_account()
            if not account:
                return False, "获取当前账号信息失败"
                
            logger.info(f"[Jimeng] 使用账号{current_account_number}检查图片状态")
            
            # 准备请求参数
            url = "https://jimeng.jianying.com/mweb/v1/mget_generate_task"
            params = {
                "aid": "513695",
                "device_platform": "web",
                "region": "CN",
                "web_id": account.get('web_id', '')
            }
            
            # 准备请求体
            data = {
                "task_id_list": [task_id],
                "http_common_info": {"aid": "513695"}
            }
            
            # 轮询检查图片状态
            for i in range(30):
                try:
                    logger.info(f"[Jimeng] 第{i+1}次尝试检查图片状态 (task_id={task_id})")
                    
                    # 获取token信息
                    token_info = self.token_manager.get_token("/mweb/v1/mget_generate_task")
                    if not token_info:
                        logger.error("[Jimeng] 获取token失败")
                        time.sleep(1)
                        continue
                        
                    # 准备请求头
                    headers = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'app-sdk-version': '48.0.0',
                        'appid': '513695',
                        'appvr': '5.8.0',
                        'content-type': 'application/json',
                        'cookie': token_info["cookie"],
                        'device-time': token_info["device_time"],
                        'sign': token_info["sign"],
                        'sign-ver': '1',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                        'msToken': token_info["msToken"],
                        'x-bogus': token_info["a_bogus"]
                    }
                    
                    response = requests.post(
                        url,
                        params=params,
                        headers=headers,
                        json=data,
                        timeout=10
                    )
                    
                    # 打印响应信息用于调试
                    logger.debug(f"[Jimeng] 状态检查响应: {response.text}")
                    
                    if response.status_code != 200:
                        logger.error(f"[Jimeng] 状态检查失败: HTTP {response.status_code} - {response.text}")
                        time.sleep(1)
                        continue
                        
                    result = response.json()
                    if result.get("ret") != "0":
                        logger.error(f"[Jimeng] API错误: {result}")
                        time.sleep(1)
                        continue
                    
                    # 从task_map获取图片任务信息
                    task_map = result.get("data", {}).get("task_map", {})
                    task_info = task_map.get(task_id)
                    
                    if not task_info:
                        logger.error(f"[Jimeng] 未找到任务信息: {task_id}")
                        time.sleep(1)
                        continue
                    
                    # 获取任务状态
                    status = task_info.get("status")
                    logger.info(f"[Jimeng] 图片状态: {status}")
                    
                    if status == 50:  # 生成成功
                        item_list = task_info.get("item_list", [])
                        if not item_list:
                            logger.error("[Jimeng] 没有找到图片内容")
                            time.sleep(1)
                            continue
                            
                        # 从第一个item中获取图片信息
                        image = item_list[0].get("image", {})
                        if not image:
                            # 尝试获取资源类型
                            resources = task_info.get("resources", [])
                            if resources:
                                for resource in resources:
                                    if resource.get("type") == "image" and resource.get("image_info"):
                                        image_url = resource.get("image_info", {}).get("image_url")
                                        if image_url:
                                            logger.info(f"[Jimeng] 从resources中找到图片URL: {image_url}")
                                            return True, image_url
                            
                            logger.error("[Jimeng] 没有找到图片对象")
                            time.sleep(1)
                            continue
                            
                        # 尝试获取所有可能的图片URL路径
                        # 1. 直接从image对象获取
                        image_url = image.get("image_url")
                        if image_url:
                            logger.info(f"[Jimeng] 从image.image_url中找到图片URL: {image_url}")
                            return True, image_url
                            
                        # 2. 从transcoded_image中获取origin图片URL
                        transcoded_image = image.get("transcoded_image", {})
                        if transcoded_image:
                            origin_image = transcoded_image.get("origin", {})
                            image_url = origin_image.get("image_url")
                            if image_url:
                                logger.info(f"[Jimeng] 从transcoded_image.origin.image_url中找到图片URL: {image_url}")
                                return True, image_url
                        
                        # 3. 尝试从其他可能的字段获取
                        if not image_url:
                            logger.error("[Jimeng] 没有找到图片URL，尝试输出完整的item结构")
                            logger.debug(f"[Jimeng] item结构: {json.dumps(item_list[0], ensure_ascii=False)}")
                            time.sleep(1)
                            continue
                        
                        return True, image_url
                        
                    elif status == 60:  # 生成失败
                        fail_code = task_info.get("task_payload", {}).get("fail_code", "")
                        fail_msg = task_info.get("fail_msg", "未知错误")
                        logger.error(f"[Jimeng] 图片生成失败: {fail_code} - {fail_msg}")
                        return False, f"图片生成失败: {fail_msg}"
                        
                    # 继续等待
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"[Jimeng] 检查状态出错: {str(e)}")
                    time.sleep(1)
                    
            return False, "图片生成超时，请稍后查看结果"
            
        except Exception as e:
            logger.error(f"[Jimeng] 检查图片状态异常: {str(e)}")
            return False, f"检查图片状态失败: {str(e)}"

    def get_generation_result(self, history_id):
        """通过history_id获取生成的图片结果
        Args:
            history_id: 历史记录ID
        Returns:
            tuple: (success, image_url)
        """
        try:
            # 获取当前账号信息
            current_account_number = self.token_manager.current_account_index + 1
            account = self.token_manager.get_current_account()
            if not account:
                return False, "获取当前账号信息失败"
                
            logger.info(f"[Jimeng] 使用账号{current_account_number}通过history_id={history_id}查询生成结果")
            
            # 准备请求参数
            url = "https://jimeng.jianying.com/mweb/v1/get_history_record"
            params = {
                "aid": "513695",
                "device_platform": "web",
                "region": "CN",
                "web_id": account.get('web_id', '')
            }
            
            # 准备请求体
            data = {
                "history_record_id": history_id,
                "http_common_info": {"aid": "513695"}
            }
            
            # 轮询检查生成结果
            for i in range(3):  # 尝试3次
                try:
                    # 获取token信息
                    token_info = self.token_manager.get_token("/mweb/v1/get_history_record")
                    if not token_info:
                        logger.error("[Jimeng] 获取token失败")
                        time.sleep(2)
                        continue
                        
                    # 准备请求头
                    headers = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'app-sdk-version': '48.0.0',
                        'appid': '513695',
                        'appvr': '5.8.0',
                        'content-type': 'application/json',
                        'cookie': token_info["cookie"],
                        'device-time': token_info["device_time"],
                        'sign': token_info["sign"],
                        'sign-ver': '1',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                        'msToken': token_info["msToken"],
                        'x-bogus': token_info["a_bogus"]
                    }
                    
                    response = requests.post(
                        url,
                        params=params,
                        headers=headers,
                        json=data,
                        timeout=10
                    )
                    
                    # 打印响应信息用于调试
                    logger.debug(f"[Jimeng] 历史记录查询响应: {response.text}")
                    
                    if response.status_code != 200:
                        logger.error(f"[Jimeng] 历史记录查询失败: HTTP {response.status_code} - {response.text}")
                        time.sleep(2)
                        continue
                        
                    result = response.json()
                    if result.get("ret") != "0":
                        logger.error(f"[Jimeng] API错误: {result}")
                        time.sleep(2)
                        continue
                    
                    # 提取生成的图片URL
                    history_record = result.get("data", {}).get("history_record", {})
                    item_list = history_record.get("item_list", [])
                    
                    if not item_list:
                        logger.error("[Jimeng] 历史记录中未找到项目列表")
                        time.sleep(2)
                        continue
                    
                    # 获取第一个项目中的资源
                    first_item = item_list[0]
                    resources = first_item.get("resources", [])
                    
                    # 尝试从resources中获取图片URL
                    for resource in resources:
                        if resource.get("type") == "image":
                            image_info = resource.get("image_info", {})
                            image_url = image_info.get("image_url")
                            if image_url:
                                logger.info(f"[Jimeng] 从历史记录中找到图片URL: {image_url}")
                                return True, image_url
                    
                    # 如果没有在resources中找到，尝试其他可能的字段
                    image = first_item.get("image", {})
                    if image:
                        image_url = image.get("image_url")
                        if image_url:
                            logger.info(f"[Jimeng] 从历史记录中找到图片URL: {image_url}")
                            return True, image_url
                    
                    logger.error("[Jimeng] 未在历史记录中找到图片URL")
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"[Jimeng] 查询历史记录出错: {str(e)}")
                    time.sleep(2)
                    
            return False, "未能从历史记录中获取生成结果"
            
        except Exception as e:
            logger.error(f"[Jimeng] 查询生成结果异常: {str(e)}")
            return False, f"查询生成结果失败: {str(e)}"
