# -*- coding: utf-8 -*-
# yuewen.py
import json
import time
import struct
import random
import os
import httpx # type: ignore
import re
import requests
import logging
import base64
import numpy as np
import cv2 # type: ignore
from plugins import *
from bridge.context import ContextType, Context
from bridge.reply import Reply, ReplyType
from channel.chat_message import ChatMessage
from common.log import logger
from plugins import Plugin, Event, EventAction, EventContext, register
from config import conf
from .login import LoginHandler

@register(
    name="<PERSON>ew<PERSON>",
    desc="跃问AI助手插件",
    version="0.1",
    author="zhayujie",
    desire_priority=-1,
    enabled=True
)
class YuewenPlugin(Plugin):
    def get_help_text(self, verbose=False, **kwargs):
        """获取帮助文本"""
        help_text = "跃问AI助手\n"
        if not verbose:
            return help_text
        
        trigger = self.config.get("trigger_prefix", "yw")
        
        help_text += "\n基本指令：\n"
        help_text += f"{trigger} [问题] - 直接对话\n"
        help_text += f"{trigger} 新建会话 - 开启新对话\n\n"
        
        help_text += "模型管理：\n"
        help_text += f"{trigger} 打印模型 - 显示可用模型列表\n"
        help_text += f"{trigger} 更新模型 - 获取最新模型列表\n"
        help_text += f"{trigger} 切换模型 [序号] - 切换到指定模型\n\n"
        
        help_text += "图片功能：\n"
        help_text += f"{trigger} {self.pic_trigger_prefix} - 识别图片内容\n"
        help_text += f"{trigger} {self.pic_trigger_prefix} [提示词] - 根据提示词识别图片\n\n"
        
        help_text += "视频功能：\n"
        help_text += f"{trigger} 视频 [提示词] - 根据提示词生成视频\n"
        help_text += f"{trigger} 视频 [提示词]-润色 - 润色提示词后生成视频\n"
        help_text += f"{trigger} 视频 [提示词]-[镜头语言] - 指定镜头语言生成视频\n"
        help_text += f"{trigger} 参考图 [提示词] - 根据参考图生成视频\n"
        help_text += f"{trigger} 参考图 [提示词]-润色 - 润色提示词后生成视频\n"
        help_text += f"{trigger} 参考图 [提示词]-[镜头语言] - 使用参考图和镜头语言\n\n"
        
        help_text += "其他功能：\n"
        help_text += f"{trigger} 开启联网 - 启用联网模式\n"
        help_text += f"{trigger} 关闭联网 - 关闭联网模式"
        
        return help_text

    def __init__(self):
        super().__init__()
        try:
            # 优先从父类加载配置
            self.config = super().load_config() or {}

            # --- API Versioning Start ---
            # 添加 api_version 配置，默认为 'old'
            if "api_version" not in self.config:
                self.config["api_version"] = "old" # 'old' for yuewen.cn, 'new' for stepfun.com

            self.base_urls = {
                'old': 'https://yuewen.cn',
                'new': 'https://www.stepfun.com' # 新版 API 的基础 URL
            }
            # 读取当前版本并设置基础 URL
            self.current_api_version = self.config.get('api_version', 'old')
            self.current_base_url = self.base_urls.get(self.current_api_version, self.base_urls['old'])
            logger.info(f"[Yuewen] Initializing with API version: {self.current_api_version} ({self.current_base_url})")
            # --- API Versioning End ---

            # 确保必要的配置项存在 (保持不变)
            if "need_login" not in self.config:
                self.config["need_login"] = True
            if "oasis_webid" not in self.config:
                self.config["oasis_webid"] = None
            if "oasis_token" not in self.config:
                self.config["oasis_token"] = None
            if "current_model_id" not in self.config:
                # 注意：新版 API 可能不使用数字 ID，需要调整模型切换逻辑
                # 暂时保留旧版 ID，但在新版逻辑中可能需要映射或忽略
                self.config["current_model_id"] = 6
            if "network_mode" not in self.config:
                self.config["network_mode"] = True # 旧版联网开关
            if "trigger_prefix" not in self.config:
                self.config["trigger_prefix"] = "yw"
            if "image_config" not in self.config:
                self.config["image_config"] = {
                    "imgprompt": "解释下图片内容",
                    "trigger": "识图"
                }

            # 使用父类方法保存配置 (确保 api_version 被保存)
            self.save_config(self.config)

            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
            self.handlers[Event.ON_RECEIVE_MESSAGE] = self.on_receive_message

            # 初始化 LoginHandler
            self.login_handler = LoginHandler(self.config)
            # 重要：将插件实例传递给login_handler以便保存配置和访问属性
            self.login_handler._plugin = self # 允许 LoginHandler 访问 current_base_url 等

            self.client = None
            self.current_chat_id = None # 旧版 API 会话 ID
            self.current_chat_session_id = None # 新版 API 会话 ID (假设)
            self.last_active_time = 0

            # 登录状态管理 (保持不变)
            self.is_login_triggered = False
            self.waiting_for_verification = {}

            # 模型配置 (旧版 API 相关，新版可能需要不同处理)
            self.models = {
                1: {"name": "deepseek r1", "id": 6, "can_network": True},
                2: {"name": "Step2", "id": 2, "can_network": True},
                3: {"name": "Step-R mini", "id": 4, "can_network": False},
                4: {"name": "Step 2-文学大师版", "id": 5, "can_network": False}
            }
            # 注意：current_model_id 对新版 API 可能无效
            self.current_model_id = self.config.get('current_model_id', 6)

            # 图片识别配置 (保持不变)
            self.image_config = self.config.get('image_config', {})
            self.imgprompt = self.image_config.get('imgprompt', '解释下图片内容')
            self.pic_trigger_prefix = self.image_config.get('trigger', '识图')

            # 图片识别状态管理 (区分或共享)
            self.waiting_for_image = {} # 新版 API 单图等待
            self.multi_image_data = {} # 旧版 API 多图等待
            self.max_images = 3 # 旧版 API 限制

            # 参考图视频功能状态管理 (旧版 API 功能)
            self.video_ref_waiting = {}

            # 镜头语言映射 (旧版 API 功能)
            self.camera_movements = {
                "拉近": "镜头拉近", "拉远": "镜头拉远", "向左": "镜头向左",
                "向右": "镜头向右", "向上": "镜头向上", "向下": "镜头向下",
                "禁止": "镜头禁止"
            }

            # Token 刷新时间由 LoginHandler 管理

            if self.client is None:
                self.client = httpx.Client(http2=False, timeout=30.0) # Set http2 to False

            # 尝试同步服务器状态 (此功能可能仅适用于旧版 API)
            if self.current_api_version == 'old':
                 self._sync_server_state()
            else:
                 logger.info("[Yuewen] Skipping state sync for new API version.")


            # 最近消息记录 (分享功能依赖，主要用于旧版)
            self.last_message = {
                'chat_id': None,
                'messages': [],
                'last_time': 0
            }

            logger.info("[Yuewen] inited")
        except Exception as e:
            logger.error(f"[Yuewen] 初始化失败: {str(e)}")
            raise e

    def on_receive_message(self, e_context: EventContext):
        """处理接收到的消息"""
        context = e_context['context']
        
        # 只处理图片类型消息
        if context.type != ContextType.IMAGE:
            return
        
        # 获取用户信息
        msg = context.kwargs.get("msg")
        is_group = context.kwargs.get("isgroup", False)
        
        # 生成等待ID
        if is_group:
            group_id = msg.other_user_id if msg else None
            real_user_id = msg.actual_user_id if msg and hasattr(msg, "actual_user_id") else None
            waiting_id = f"{group_id}_{real_user_id}" if real_user_id else group_id
        else:
            real_user_id = msg.from_user_id if msg else None
            waiting_id = real_user_id
        
        # 处理参考图生成视频的图片上传
        if waiting_id in self.video_ref_waiting:
            logger.debug("[Yuewen] 收到参考图片，准备生成视频")
            if hasattr(context, 'kwargs') and 'msg' in context.kwargs:
                msg = context.kwargs['msg']
                if hasattr(msg, '_prepare_fn') and not msg._prepared:
                    try:
                        msg._prepare_fn()
                        msg._prepared = True
                        if hasattr(msg, 'content'):
                            logger.debug(f"[Yuewen] 参考图片准备完成，保存路径: {msg.content}")
                            # 获取参考图配置
                            video_ref_config = self.video_ref_waiting.pop(waiting_id)
                            # 处理参考图生成视频
                            result = self._handle_video_ref_image(
                                msg.content,
                                video_ref_config['prompt'],
                                e_context,
                                video_ref_config['use_rephrase'],
                                video_ref_config.get('camera_list', [])
                            )
                            # 只有在处理失败且返回错误消息时才回复
                            if result:
                                reply = Reply()
                                reply.type = ReplyType.TEXT
                                reply.content = result
                                e_context["channel"].send(reply, e_context["context"])
                            e_context.action = EventAction.BREAK_PASS
                            return
                    except Exception as e:
                        logger.error(f"[Yuewen] 参考图片处理失败: {e}")
                        # 清理状态
                        self.video_ref_waiting.pop(waiting_id, None)
                        # 发送错误消息
                        reply = Reply()
                        reply.type = ReplyType.TEXT
                        reply.content = "❌ 参考图片处理失败，请重试"
                        e_context["channel"].send(reply, e_context["context"])
                        e_context.action = EventAction.BREAK_PASS
                        return
        
        # 只有当用户在等待上传图片状态时才处理
        if waiting_id in self.waiting_for_image or waiting_id in self.multi_image_data:
            logger.debug("[Yuewen] 收到等待中的图片消息")
            if hasattr(context, 'kwargs') and 'msg' in context.kwargs:
                msg = context.kwargs['msg']
                if hasattr(msg, '_prepare_fn') and not msg._prepared:
                    try:
                        msg._prepare_fn()
                        msg._prepared = True
                        if hasattr(msg, 'content'):
                            logger.debug(f"[Yuewen] 图片准备完成，保存路径: {msg.content}")
                    except Exception as e:
                        logger.error(f"[Yuewen] 图片准备失败: {e}")
        else:
            # 关键修改：明确忽略不需要处理的图片
            logger.info("[Yuewen] 不在等待图片状态，忽略图片消息")
            e_context.action = EventAction.CONTINUE

    def _generate_traceparent(self):
        trace_id = ''.join(random.choices('0123456789abcdef', k=32))
        span_id = ''.join(random.choices('0123456789abcdef', k=16))
        return f"00-{trace_id}-{span_id}-01"

    def _generate_tracestate(self):
        return f"yuewen@rsid={random.getrandbits(64):016x}"

    def _update_headers(self):
        """根据当前 API 版本更新通用请求头"""
        headers = self.login_handler.base_headers.copy()
        # 使用 self.current_base_url 代替硬编码的 URL
        base_url = self.current_base_url
        token = self.config.get('oasis_token', '')
        webid = self.config.get('oasis_webid', '')

        # 基本 Cookie 组件
        cookie_parts = []
        if webid:
             cookie_parts.append(f"Oasis-Webid={webid}")
        # 注意：新 API 可能需要不同的或额外的 Cookie
        # if self.current_api_version == 'new':
        #     # 添加新 API 特有的 cookie (如果需要)
        #     cookie_parts.append("some_new_cookie=value")
        if token:
             cookie_parts.append(f"Oasis-Token={token}")

        cookie_string = "; ".join(cookie_parts)

        # 两个版本通用的 Header
        common_headers = {
            'Cookie': cookie_string,
            'oasis-webid': webid,
            'origin': base_url, # 使用当前版本的 base_url
            'referer': f'{base_url}/', # Referer 可能需要根据具体端点调整
            'oasis-appid': '10200',
            'oasis-platform': 'web',
            'oasis-language': 'zh', # 新增，新版可能需要
            'connect-protocol-version': '1', # 两个版本似乎都需要
            'canary': 'false', # 两个版本似乎都需要
            'priority': 'u=1, i', # 两个版本似乎都需要
            'x-waf-client-type': 'fetch_sdk' # 两个版本似乎都需要
        }
        headers.update(common_headers)

        # --- 旧版 API 特有 Headers ---
        if self.current_api_version == 'old':
            logger.debug("[Yuewen] Adding Old API specific headers (RUM trace).")
            headers.update({
                 'x-rum-traceparent': self._generate_traceparent(),
                 'x-rum-tracestate': self._generate_tracestate(),
                 # 可能还有 'oasis-mode': '2' 等旧版特有的，根据需要添加回
                 'oasis-mode': '2',
            })
            # 确保移除新版可能添加的不兼容 header (如果 common_headers 中有的话)

        # --- 新版 API 特有 Headers ---
        elif self.current_api_version == 'new':
             logger.debug("[Yuewen] Adding New API specific headers (if any).")
             # 添加新版 API 特有的 Headers，例如 'Sec-Fetch-Dest': 'empty' 等
             # headers.update({ 'some-new-header': 'new-value'})
             # 移除旧版特有的 header
             headers.pop('x-rum-traceparent', None)
             headers.pop('x-rum-tracestate', None)
             headers.pop('oasis-mode', None) # 假设新版不需要

        # logger.debug(f"[Yuewen] Updated headers for {self.current_api_version}: {headers}") # Debugging
        return headers

    def _handle_commands(self, message):
        """处理内置命令，增加 API 版本检查"""
        msg = message.strip().lower()

        # --- 模型相关命令 (假设仅旧版有效) ---
        if msg.startswith("切换模型"):
            if self.current_api_version == 'new':
                 return "⚠️ 新版 API 不支持手动切换模型。"
            try:
                model_num = int(msg.split()[-1])
                return self._switch_model(model_num) # 旧版逻辑
            except:
                return "⚠️ 无效的模型编号，使用「打印模型」查看"

        if msg == "打印模型":
            if self.current_api_version == 'new':
                 return "⚠️ 新版 API 无需打印模型列表。"
            return self._list_models() # 旧版逻辑

        # --- 联网命令 (假设仅旧版通过此命令控制) ---
        current_model = next((m for m in self.models.values() if m['id'] == self.current_model_id), None)
        if msg in ["开启联网", "关闭联网"]:
             if self.current_api_version == 'new':
                 # 新版联网在发送消息时控制 (config.enableSearch)
                 # 可以考虑修改 config 并提示用户
                 enable = msg == "开启联网"
                 self.config['network_mode'] = enable
                 self.save_config(self.config)
                 status = "启用" if enable else "关闭"
                 return f"✅ 新版 API 联网模式已设置为 {status} (下次对话生效)"
             else: # 旧版逻辑
                 if not current_model or not current_model['can_network']:
                     return "⚠️ 当前旧版模型不支持联网"
                 return self._handle_network_command(msg)

        # --- 其他命令 (如果还有) ---

        return None # 没有匹配的内置命令

    def _switch_model(self, model_num):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _switch_model called in new API mode, which is not supported.")
            return "⚠️ 此功能仅支持旧版 API。"
        if model_num not in self.models:
            return f"⚠️ 无效模型编号，可用：{', '.join(map(str, self.models.keys()))}"
        
        model_info = self.models[model_num]
        if self.current_model_id == model_info['id']:
            return f"✅ 已经是{model_info['name']}模型"
        
        # 先尝试刷新令牌
        if not self.login_handler.refresh_token():
            return "⚠️ 令牌刷新失败，请重试"
        
        # 切换模型
        if not self._call_set_model(model_info['id']):
            return "⚠️ 模型切换失败，请重试"
        
        self.current_model_id = model_info['id']
        self.config['current_model_id'] = self.current_model_id
        self.login_handler.save_config()
        
        # 如果是deepseek r1模型，强制开启联网模式
        if model_info['id'] == 6:
            self.config['network_mode'] = True
            if not self._enable_search(True):
                logger.warning("[Yuewen] 联网模式启用失败")
            if not self._enable_deep_thinking():
                logger.warning("[Yuewen] 深度思考模式启用失败")
        
        # 创建新会话
        self.current_chat_id = None
        if not self.create_chat():
            return "⚠️ 新会话创建失败"
        
        # 最后再次同步确认服务器状态
        if not self._sync_server_state():
            logger.warning("[Yuewen] 服务器状态同步失败")
        
        return f"✅ 已切换至{model_info['name']}"

    def _call_set_model(self, model_id):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _call_set_model called in new API mode, which is not supported.")
            return False # Return False as the original function does on failure
        """切换模型"""
        for retry in range(2):
            headers = self._update_headers()
            headers['Content-Type'] = 'application/json'
            try:
                response = self.client.post(
                    'https://yuewen.cn/api/proto.user.v1.UserService/SetModelInUse',
                    headers=headers,
                    json={"modelId": model_id}
                )
                if response.status_code == 200:
                    data = response.json()
                    if data.get("result") == "RESULT_CODE_SUCCESS":
                        return True
                elif response.status_code == 401 and retry == 0:
                    if self.login_handler.refresh_token():
                        continue
                    else:
                        logger.error("[Yuewen] Token刷新失败")
                logger.error(f"[Yuewen] 模型切换失败: {response.text}")
                return False
            except Exception as e:
                if retry == 0:
                    continue
                logger.error(f"[Yuewen] 模型切换失败: {str(e)}")
                return False
        return False

    def _list_models(self):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _list_models called in new API mode, which is not supported.")
            return "⚠️ 此功能仅支持旧版 API。"
        output = ["可用模型："]
        for num, info in self.models.items():
            status = "（支持联网）" if info['can_network'] else ""
            current = " ← 当前使用" if info['id'] == self.current_model_id else ""
            output.append(f"{num}. {info['name']}{status}{current}")
        return '\n'.join(output)

    def _enable_search(self, enable: bool):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _enable_search called in new API mode, which is not supported.")
            return False # Return False as the original function does on failure
        """切换联网模式"""
        for retry in range(2):
            headers = self._update_headers()
            headers.update({
                'Content-Type': 'application/json',
                'referer': f'https://yuewen.cn/chats/{self.current_chat_id}' if self.current_chat_id else 'https://yuewen.cn/chats/new',
                'canary': 'false',
                'connect-protocol-version': '1',
                'oasis-appid': '10200',
                'oasis-mode': '2',
                'oasis-platform': 'web',
                'priority': 'u=1, i',
                'x-waf-client-type': 'fetch_sdk'
            })
            endpoint = "EnableSearch" if enable else "DisableSearch"
            try:
                response = self.client.post(
                    f'https://yuewen.cn/api/proto.user.v1.UserService/{endpoint}',
                    headers=headers,
                    json={}
                )
                if response.status_code == 200:
                    data = response.json()
                    if data.get("result") == "RESULT_CODE_SUCCESS":
                        logger.info(f"[Yuewen] 联网模式{'启用' if enable else '关闭'}成功")
                        return True
                    elif not data:  # 空响应也视为成功
                        logger.info(f"[Yuewen] 联网模式{'启用' if enable else '关闭'}成功(空响应)")
                        return True
                elif response.status_code == 401 and retry == 0:
                    if self.login_handler.refresh_token():
                        continue
                    else:
                        logger.error("[Yuewen] Token刷新失败")
                logger.error(f"[Yuewen] 联网模式切换失败: {response.text}")
                return False
            except Exception as e:
                if retry == 0:
                    continue
                logger.error(f"[Yuewen] 联网模式切换失败: {str(e)}")
                return False
        return False

    def _handle_network_command(self, message):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _handle_network_command called directly in new API mode, which is not supported (should be handled via _handle_commands).")
            return "⚠️ 此功能仅支持旧版 API。"
        enable = message == "开启联网"
        
        # 先尝试刷新令牌
        if not self.login_handler.refresh_token():
            return "⚠️ 令牌刷新失败，请重试"
            
        # 如果当前没有会话，先创建会话
        if not self.current_chat_id:
            if not self.create_chat():
                return "⚠️ 会话创建失败"
            
        if self._enable_search(enable):
            self.config['network_mode'] = enable
            self.login_handler.save_config()
            status = "启用" if enable else "关闭"
            return f"✅ 联网模式已{status}"
        return f"⚠️ 联网模式{'启用' if enable else '关闭'}失败"

    def create_chat(self, chat_name="新话题"):
        """创建新会话 (根据 API 版本选择逻辑)"""
        if self.current_api_version == 'new':
            return self._create_chat_new(chat_name)
        else:
            # 确保调用旧版逻辑
            return self._create_chat_old(chat_name)

    def _create_chat_old(self, chat_name="新话题"):
        """创建旧版 (yuewen.cn) 会话 - **代码与旧代码.py完全一致**"""
        logger.debug("[Yuewen] Calling _create_chat_old")
        for retry in range(2):
            headers = self._update_headers() # 获取适配旧版的headers
            headers['Content-Type'] = 'application/json'
            # 使用当前 base_url
            url = f'{self.current_base_url}/api/proto.chat.v1.ChatService/CreateChat'
            try:
                response = self.client.post(
                    url,
                    headers=headers,
                    json={'chatName': chat_name}
                )
                if response.status_code == 200:
                    data = response.json()
                    if 'chatId' in data:
                        self.current_chat_id = data['chatId']
                        self.current_chat_session_id = None # 清空新版 ID
                        self.last_active_time = time.time()
                        logger.info(f"[Yuewen][Old API] 新建会话成功 ID: {self.current_chat_id}")
                        # 同步状态 (旧版API创建会话后可能需要)
                        self._sync_server_state()
                        return True
                elif response.status_code == 401 and retry == 0:
                    if self.login_handler.refresh_token():
                        continue
                    else:
                        logger.error("[Yuewen][Old API] Token刷新失败")
                logger.error(f"[Yuewen][Old API] 创建会话失败: {response.text}")
                return False
            except Exception as e:
                # 保持原来的重试逻辑
                if retry == 0 and isinstance(e, (httpx.RequestError, httpx.TimeoutException)):
                     # 如果是网络错误也尝试刷新token看是否解决
                     if self.login_handler.refresh_token():
                         continue
                logger.error(f"[Yuewen][Old API] 创建会话失败: {str(e)}")
                # 在第一次尝试失败后继续重试，只有第二次失败才返回False
                if retry < 1 : continue # 明确添加 continue
                return False
        return False # 所有重试失败后返回

    def _create_chat_new(self, chat_name="新话题"):
        """创建新版 (stepfun.com) 会话"""
        logger.debug("[Yuewen] Calling _create_chat_new")
        for retry in range(2):
            headers = self._update_headers() # 获取适配新版的headers
            headers['Content-Type'] = 'application/json'
            # 假设新版创建会话的端点和方式 (需要根据实际抓包确认)
            url = f'{self.current_base_url}/api/agent/capy.agent.v1.AgentService/CreateChatSession'
            logger.info(f"[Yuewen][New API] Attempting to create chat session at {url}")
            try:
                # 假设新版 API 创建会话不需要 chatName，请求体为空 {}
                response = self.client.post(
                    url,
                    headers=headers,
                    json={}
                )
                if response.status_code == 200:
                    data = response.json()
                    # 假设新版返回 chatSessionId (需要根据实际抓包确认)
                    session_data = data.get('chatSession')
                    if session_data and session_data.get('chatSessionId'):
                        self.current_chat_session_id = session_data['chatSessionId']
                        self.current_chat_id = None # 清空旧版 ID
                        self.last_active_time = time.time()
                        logger.info(f"[Yuewen][New API] 新建会话成功 SessionID: {self.current_chat_session_id}")
                        return True
                    else:
                         logger.error(f"[Yuewen][New API] 创建会话失败: 响应中缺少 chatSessionId - {response.text}")
                         return False # 解析失败直接返回
                elif response.status_code == 401 and retry == 0:
                    if self.login_handler.refresh_token():
                        continue
                    else:
                        logger.error("[Yuewen][New API] Token刷新失败")
                        return False # Token 刷新失败直接返回
                else:
                     logger.error(f"[Yuewen][New API] 创建会话失败: HTTP {response.status_code} - {response.text}")
                     # 其他 HTTP 错误，如果不是 401 且是第一次尝试，也可以选择重试
                     if retry < 1: continue
                     return False # 重试后仍失败或非 401 错误直接返回
            except Exception as e:
                # 保持原来的重试逻辑
                if retry == 0 and isinstance(e, (httpx.RequestError, httpx.TimeoutException)):
                    if self.login_handler.refresh_token():
                        continue
                logger.error(f"[Yuewen][New API] 创建会话失败: {str(e)}", exc_info=True) # Add exc_info=True
                if retry < 1 : continue # 明确添加 continue
                return False
        return False # 所有重试失败后返回

    def _construct_protocol_packet(self, message):
        """构造旧版 API 的协议包 - **代码与旧代码.py完全一致**"""
        if self.current_api_version == 'new':
            logger.warning("[Yuewen] _construct_protocol_packet called in new API mode. This should not happen.")
            # 在新模式下调用旧构造器是错误的，返回None或抛出异常
            return None

        # --- Start: 旧代码逻辑 ---
        if not self.current_chat_id:
            logger.error("[Yuewen][Old API] Missing chatId for constructing old packet.")
            return None # 需要 chatId

        payload = {
            "chatId": self.current_chat_id,
            "messageInfo": {
                "text": message,
                "author": {"role": "user"}
            },
            "messageMode": "SEND_MESSAGE",
            "modelId": self.current_model_id # 旧API使用 modelId
        }
        try:
            json_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
            encoded = json_str.encode('utf-8')
            # 旧版 Connect 协议: Flag (0x00) + Length (big-endian 4 bytes) + JSON
            protocol_header = struct.pack('>BI', 0, len(encoded))
            return protocol_header + encoded
        except Exception as e:
             logger.error(f"[Yuewen][Old API] Failed to construct protocol packet: {e}")
             return None
        # --- End: 旧代码逻辑 ---

    def _construct_protocol_packet_new(self, content, attachments=None):
        """为新版 API 构造请求体 (Connect Protocol Framed)"""
        logger.debug(f"[Yuewen] Calling _construct_protocol_packet_new with content: '{content[:50]}...'")
        if not self.current_chat_session_id:
             logger.error("[Yuewen][New API] Missing chatSessionId for constructing packet.")
             return None

        # 新版 API 的 payload 结构 (基于之前的分析和假设)
        payload = {
            "message": {
                "chatSessionId": self.current_chat_session_id,
                "content": {
                    "userMessage": {
                        "qa": {
                            "content": content # 发送纯文本内容
                        }
                        # attachments 会在下面添加 (如果存在)
                    }
                }
            },
            "config": {
                # 新版 API 可能直接指定模型名称字符串
                "model": "deepseek-r1", # 或者根据 self.current_model_id 映射？需要确认
                "enableReasoning": True, # 假设需要
                # 新版联网开关可能在这里控制
                "enableSearch": self.config.get('network_mode', True)
            }
        }

        # 如果有附件 (图片)，添加到 payload
        if attachments:
            # 确保 qa 存在
            if 'qa' not in payload['message']['content']['userMessage']:
                 payload['message']['content']['userMessage']['qa'] = {}
            payload['message']['content']['userMessage']['qa']['attachments'] = attachments
            # 如果附件存在但文本内容为空，确保 content 字段存在
            if not payload['message']['content']['userMessage']['qa'].get('content'):
                 payload['message']['content']['userMessage']['qa']['content'] = ""

        try:
            # Connect 协议: Flag (0x00) + Length (big-endian 4 bytes) + JSON
            json_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
            encoded_json = json_str.encode('utf-8')
            length = len(encoded_json)
            prefix = struct.pack('>BI', 0, length)
            framed_data = prefix + encoded_json
            logger.debug(f"[Yuewen][New API] Framed request data length: {len(framed_data)}")
            return framed_data
        except Exception as e:
            logger.error(f"[Yuewen][New API] 构造请求包失败: {e}")
            return None

    def _parse_stream_response(self, response, start_time):
        """解析旧版 API 的流式响应 - **代码与旧代码.py完全一致**"""
        if self.current_api_version == 'new':
            logger.warning("[Yuewen] _parse_stream_response called in new API mode. This should not happen.")
            return "内部错误：调用了旧版 API 的响应解析器"

        # --- Start: 旧代码逻辑 ---
        buffer = bytearray()
        text_buffer = []
        has_thinking_stage = False
        is_done = False
        user_message_id = None
        ai_message_id = None

        try:
            current_model = next((m for m in self.models.values() if m['id'] == self.current_model_id), None)
            model_name = current_model['name'] if current_model else f"未知模型(ID: {self.current_model_id})"

            logger.debug(f"[Yuewen][Old API] 开始处理响应，使用模型: {model_name}")
            logger.debug(f"[Yuewen][Old API] 当前会话ID: {self.current_chat_id}")

            for chunk in response.iter_bytes():
                buffer.extend(chunk)
                while len(buffer) >= 5:
                    try:
                        msg_type, length = struct.unpack('>BI', buffer[:5])
                    except struct.error:
                        logger.warning(f"[Yuewen][Old API] Struct unpack error on buffer prefix: {buffer[:10]}. Clearing buffer.")
                        buffer.clear() # 清理损坏的 buffer
                        break # 跳出内部 while，处理下一个 chunk

                    if len(buffer) < 5 + length:
                        break # 数据包不完整，等待更多数据

                    packet = buffer[5:5+length]
                    buffer = buffer[5+length:] # 从 buffer 中移除已处理的数据包

                    try:
                        data = json.loads(packet.decode('utf-8'))

                        if 'textEvent' in data:
                            event = data['textEvent']
                            if event.get('stage') == 'TEXT_STAGE_THINKING':
                                has_thinking_stage = True
                                continue
                            if event.get('stage') and event.get('stage') != 'TEXT_STAGE_SOLUTION':
                                continue
                            content = event.get('text', '')
                            if content:
                                text_buffer.append(content)

                        if 'startEvent' in data:
                            start_event = data['startEvent']
                            ai_message_id = start_event.get('messageId')
                            parent_id = start_event.get('parentMessageId')
                            if parent_id:
                                user_message_id = parent_id

                        if 'doneEvent' in data:
                            is_done = True
                            # 旧版在这里可以考虑 break，因为 doneEvent 通常是最后的？
                            # 但为了保险起见，继续处理完 buffer 中剩余内容
                            # logger.debug("[Yuewen][Old API] Done event received.")

                    except json.JSONDecodeError as e:
                        logger.error(f"[Yuewen][Old API] 解析数据包 JSON 失败: {e}. Packet: {packet!r}")
                        continue # 跳过这个损坏的包
                    except Exception as e:
                        logger.error(f"[Yuewen][Old API] 处理数据包时发生未知错误: {e}")
                        continue # 跳过这个包

            # 循环结束后检查是否收到 done 事件
            if not is_done:
                # 如果文本缓冲区有内容，可能只是没收到 done，尝试返回已有内容
                if text_buffer:
                     logger.warning("[Yuewen][Old API] Stream ended but doneEvent not found, returning buffered text.")
                else:
                     logger.error("[Yuewen][Old API] Stream ended but doneEvent not found and no text received.")
                     return "响应未完成或为空，请重试" # 返回错误信息

            cost_time = time.time() - start_time
            final_text = ''.join(text_buffer)
            # 调用统一的文本后处理函数
            final_text = self._process_final_text(final_text)

            # 更新最近消息记录 (用于分享)
            if self.current_chat_id and user_message_id and ai_message_id:
                logger.debug(f"[Yuewen][Old API] 记录消息ID - User: {user_message_id}, AI: {ai_message_id}")
                self.last_message = {
                    'chat_id': self.current_chat_id,
                    'messages': [
                        {'messageId': ai_message_id, 'messageIndex': 1}, # 假设AI消息是第1条被选
                        {'messageId': user_message_id} # 假设用户消息是参考
                    ],
                    'last_time': time.time()
                }

            if final_text:
                network_mode = "联网" if self.config.get('network_mode', False) else "未联网"
                status_info = f"使用{model_name}模型{network_mode}模式回答（耗时{cost_time:.2f}秒）：\n"
                # 旧版 API 返回带分享提示的字符串
                share_info = "\n\n3分钟内发送yw分享获取回答图片"
                return f"{status_info}{final_text}{share_info}"

            logger.warning(f"[Yuewen][Old API] No valid text reply received (cost: {cost_time:.2f}s).")
            return f"未收到有效回复（耗时{cost_time:.2f}秒）"

        except httpx.StreamError as e:
             logger.error(f"[Yuewen][Old API] Stream error during parsing: {e}")
             return f"读取响应流时出错: {e}"
        except struct.error as e:
             logger.error(f"[Yuewen][Old API] Error unpacking stream frame: {e}")
             return f"解析响应帧时出错: {e}"
        except Exception as e:
            logger.error(f"[Yuewen][Old API] 解析响应失败: {e}", exc_info=True)
            return f"响应解析失败: {str(e)}"
        # --- End: 旧代码逻辑 ---

    def _parse_response_new(self, response, start_time):
        """解析新版 API 的流式响应 (返回 Reply 对象)"""
        logger.debug(f"[Yuewen][New API] _parse_response_new called")
        text_buffer = []
        final_reply = None # 最终的 Reply 对象
        image_reply_generated = False # 标记是否已处理图片响应
        is_done = False # 标记是否收到结束信号
        buffer = bytearray()

        # --- Log file setup (optional but helpful for debugging) ---
        try:
            plugin_dir = os.path.dirname(os.path.abspath(__file__))
            log_file_path = os.path.join(plugin_dir, "stepfun_response.log") # Use a different name
            with open(log_file_path, 'w', encoding='utf-8') as f:
                f.write(f"--- New StepFun Response Log Start ({time.strftime('%Y-%m-%d %H:%M:%S')}) ---\n")
            logger.info(f"[Yuewen][New API] Logging response stream to: {log_file_path}")
        except Exception as e:
            logger.error(f"[Yuewen][New API] Failed to setup response log file: {e}")
            log_file_path = None # Disable logging if setup fails
        # --- End Log file setup ---

        try:
            for chunk in response.iter_bytes():
                buffer.extend(chunk)

                # --- Log raw chunks (optional) ---
                # if log_file_path:
                #      try:
                #          with open(log_file_path, 'ab') as f: # Append bytes
                #              f.write(chunk)
                #      except Exception as e:
                #          logger.error(f"[Yuewen][New API] Failed to write raw chunk to log: {e}")
                # --- End Log raw chunks ---


                while len(buffer) >= 5: # 需要至少 5 字节来解析 flag 和 length
                    flags = buffer[0]
                    try:
                        # 读取长度 (大端序，4字节无符号整数)
                        length = struct.unpack('>I', buffer[1:5])[0]
                    except struct.error:
                        logger.error(f"[Yuewen][New API] Struct unpack error on length bytes: {buffer[1:5]}. Buffer: {buffer[:20]}...")
                        # 尝试跳过损坏的头部，但这很危险，可能导致后续解析错误
                        buffer = buffer[5:] # 移除假定的损坏头部
                        continue # 继续处理 buffer 中剩余部分

                    if len(buffer) < 5 + length:
                        # 数据包不完整，等待下一个 chunk
                        break

                    # 提取消息体并更新 buffer
                    message_bytes = buffer[5 : 5 + length]
                    buffer = buffer[5 + length:] # 移除已处理的消息

                    # 检查结束标志位 (0x02)
                    if flags & 0x02:
                        logger.info("[Yuewen][New API] End stream flag (0x02) detected.")
                        is_done = True
                        # 收到结束标志后，理论上不应再有数据包，但仍需处理完当前消息体

                    if length > 0: # 只有长度大于 0 才尝试解析 JSON
                        decoded_json_str = "" # 用于日志记录
                        try:
                            decoded_json_str = message_bytes.decode('utf-8')
                            # --- Log decoded JSON chunk ---
                            if log_file_path:
                                 try:
                                     with open(log_file_path, 'a', encoding='utf-8') as f:
                                         f.write(f"--- Chunk Start (Length: {length}, Flags: {flags}) ---\n")
                                         f.write(decoded_json_str + "\n")
                                         f.write("--- Chunk End ---\n")
                                 except Exception as e:
                                     logger.error(f"[Yuewen][New API] Failed to write JSON chunk to log file: {e}")
                            # --- End Log decoded JSON chunk ---

                            data = json.loads(decoded_json_str)
                            # logger.debug(f"[Yuewen][New API] Received JSON data: {data}") # 打印解析后的数据

                            # --- 事件处理 ---
                            # 新版 API 的事件结构可能嵌套在 'data.event' 中
                            event_wrapper = data.get('data', {})
                            event_data = event_wrapper.get('event', {}) # 获取实际的事件内容

                            if not event_data:
                                # logger.debug(f"[Yuewen][New API] Chunk data does not contain 'data.event'. Data: {data}")
                                continue # 如果没有事件数据，处理下一个包

                            # 1. 文本事件
                            if 'textEvent' in event_data:
                                text_content = event_data['textEvent'].get('text')
                                if isinstance(text_content, str):
                                    text_buffer.append(text_content)
                                    logger.debug(f"[Yuewen][New API] Appended text: '{text_content}'")

                            # 2. 消息事件（可能包含图片生成）
                            elif 'messageEvent' in event_data:
                                msg_event = event_data.get('messageEvent', {})
                                assistant_msg = msg_event.get('message', {}).get('content', {}).get('assistantMessage', {})
                                creation_items = assistant_msg.get('creation', {}).get('items', [])

                                if creation_items:
                                    logger.info("[Yuewen][New API] Detected image creation response.")
                                    for item in creation_items:
                                        # 检查是否是图片生成任务，并且状态是进行中或待处理
                                        if item.get('type') == 'CREATION_TYPE_GEN_IMAGE' and item.get('state') in ['CREATION_STATE_RUNNING', 'CREATION_STATE_PENDING', 'CREATION_STATE_SUCCESS']: # 也处理直接成功的状态
                                            creation_id = item.get('creationId')
                                            # firstCreationRecordId 可能在 item 或 assistant_msg.creation 里
                                            record_id = item.get('firstCreationRecordId') or assistant_msg.get('creation', {}).get('firstCreationRecordId')

                                            if creation_id and record_id:
                                                logger.info(f"[Yuewen][New API] Found Image Creation Task: CreationID={creation_id}, RecordID={record_id}, State={item.get('state')}")
                                                # 如果状态已经是成功，直接尝试提取结果
                                                if item.get('state') == 'CREATION_STATE_SUCCESS':
                                                     resources = item.get('result', {}).get('genImage', {}).get('resources', [])
                                                     if resources:
                                                         image_url = resources[0].get('resource', {}).get('image', {}).get('url')
                                                         if image_url:
                                                             logger.info("[Yuewen][New API] Image URL found directly in messageEvent.")
                                                             final_reply = Reply(ReplyType.IMAGE_URL, image_url)
                                                             image_reply_generated = True
                                                             break # 找到图片，跳出 items 循环

                                                # 如果不是直接成功，或者直接成功但未找到URL，启动轮询
                                                if not image_reply_generated:
                                                    logger.info("[Yuewen][New API] Starting polling for image result...")
                                                    polling_start_time = time.time()
                                                    image_url = self._get_image_result_new(creation_id, record_id)
                                                    polling_cost_time = time.time() - polling_start_time
                                                    if image_url:
                                                        logger.info(f"[Yuewen][New API] Image URL retrieved via polling ({polling_cost_time:.2f}s).")
                                                        final_reply = Reply(ReplyType.IMAGE_URL, image_url)
                                                    else:
                                                        logger.error("[Yuewen][New API] Failed to retrieve image URL via polling.")
                                                        final_reply = Reply(ReplyType.ERROR, f"图像生成失败或超时（轮询耗时{polling_cost_time:.2f}秒）")
                                                    image_reply_generated = True
                                                    break # 尝试完图片处理，跳出 items 循环
                                            else:
                                                 logger.warning("[Yuewen][New API] Missing creationId or recordId in image creation item.")
                                    # 如果处理了图片（无论成功失败），跳出外层 while 循环
                                    if image_reply_generated:
                                         break # 跳出内部 while 循环

                            # 3. 其他事件（忽略）
                            elif ('reasoningEvent' in event_data or
                                  'pipelineEvent' in event_data or
                                  'heartBeatEvent' in event_data or
                                  'messageDoneEvent' in event_data or # 这个可能有用，标记消息结束
                                  'doneEvent' in event_data or # 这个是整个流结束
                                  'startEvent' in event_data):
                                # logger.debug(f"[Yuewen][New API] Ignoring event type: {list(event_data.keys())[0]}")
                                if 'messageDoneEvent' in event_data:
                                    logger.debug("[Yuewen][New API] Received messageDoneEvent.")
                                if 'doneEvent' in event_data:
                                     logger.debug("[Yuewen][New API] Received doneEvent (stream end).")
                                     is_done = True # 确保 is_done 被设置
                                pass
                            else:
                                logger.warning(f"[Yuewen][New API] Unhandled event type in chunk: {list(event_data.keys())}")

                        except json.JSONDecodeError:
                            logger.error(f"[Yuewen][New API] Failed to decode JSON chunk: {message_bytes!r}")
                            if log_file_path:
                                try:
                                    with open(log_file_path, 'a', encoding='utf-8') as f:
                                        f.write(f"--- FAILED JSON DECODE (Length: {length}, Flags: {flags}) ---\n")
                                        f.write(repr(message_bytes) + "\n") # 记录原始字节
                                        f.write("--- Chunk End ---\n")
                                except Exception as e:
                                     logger.error(f"[Yuewen][New API] Failed to write raw chunk to log file: {e}")
                            continue # 跳过这个损坏的包
                        except Exception as e:
                            logger.error(f"[Yuewen][New API] Error processing message chunk: {e}", exc_info=True)
                            continue # 跳过处理出错的包

                    # 如果内部循环因为图片处理而 break，这里也 break 外层循环
                    if image_reply_generated:
                         break

            # --- 循环结束后处理结果 ---
            logger.debug(f"[Yuewen][New API] Parsing loop finished. is_done={is_done}, image_generated={image_reply_generated}")
            cost_time = time.time() - start_time

            if image_reply_generated:
                # final_reply 应该已经在上面设置好了 (IMAGE_URL 或 ERROR)
                if final_reply is None: # 理论上不应发生，加个保险
                     logger.error("[Yuewen][New API] Image generated but final_reply is None!")
                     final_reply = Reply(ReplyType.ERROR, "图像处理后发生未知错误")
            elif text_buffer: # 如果有文本内容
                final_text = ''.join(text_buffer)
                final_text = self._process_final_text(final_text) # 应用格式化
                # 新版 API 返回 Reply 对象
                status_info = f"使用新版API回答（耗时{cost_time:.2f}秒）："
                final_reply = Reply(ReplyType.TEXT, f"{status_info}\n\n{final_text}") # 在状态和内容间加换行
            elif is_done: # 流正常结束但没有任何内容
                logger.warning(f"[Yuewen][New API] Stream finished successfully but no text or image generated (cost: {cost_time:.2f}s).")
                final_reply = Reply(ReplyType.INFO, f"未收到有效回复（耗时{cost_time:.2f}秒）") # 使用 INFO 类型
            else: # 异常结束（没收到 is_done 信号）
                if buffer: # 且 buffer 中还有数据
                     logger.error(f"[Yuewen][New API] Stream ended unexpectedly with remaining buffer data ({len(buffer)} bytes).")
                     final_reply = Reply(ReplyType.ERROR, "响应流意外中断 (数据未完整接收)")
                else: # buffer 也空了
                     logger.error("[Yuewen][New API] Stream ended unexpectedly without end flag or content.")
                     final_reply = Reply(ReplyType.ERROR, "响应流意外中断")

        except httpx.StreamError as e:
            logger.error(f"[Yuewen][New API] Stream error during parsing: {e}")
            final_reply = Reply(ReplyType.ERROR, f"读取响应流时出错: {e}")
        except struct.error as e:
             logger.error(f"[Yuewen][New API] Error unpacking stream frame: {e}")
             final_reply = Reply(ReplyType.ERROR, f"解析响应帧时出错: {e}")
        except Exception as e:
            logger.error(f"[Yuewen][New API] 解析响应失败: {e}", exc_info=True)
            final_reply = Reply(ReplyType.ERROR, f"响应解析失败: {str(e)}")

        # 最终确保 final_reply 不是 None
        if final_reply is None:
             logger.error("[Yuewen][New API] Reached end of _parse_response_new without generating a reply.")
             final_reply = Reply(ReplyType.ERROR, "解析响应时发生未知内部错误")

        return final_reply

    def _process_final_text(self, text):
         """统一的文本后处理函数 - **代码与旧代码.py完全一致**"""
         # --- Start: 旧代码逻辑 ---
         processed_text = (
             text.replace('\u200b', '')
             .replace('\r\n', '\n')
             .replace('\r', '\n')
         )
         processed_text = re.sub(r'\n(\d+\.|\-|\*)\s*', r'\n\n\1 ', processed_text)
         lines = processed_text.split('\n')
         processed_lines = []
         for i, line in enumerate(lines):
             is_list_item = line.startswith(('- ', '* ')) or re.match(r'^\d+\.\s', line)
             is_prev_line_blank = (i > 0 and processed_lines[-1] == '')
             if i > 0 and is_list_item and not is_prev_line_blank:
                processed_lines.append('')
             processed_lines.append(line)

         processed_text = '\n'.join(processed_lines)
         processed_text = re.sub(r'\n{3,}', '\n\n', processed_text)
         processed_text = processed_text.strip()
         # --- End: 旧代码逻辑 ---
         return processed_text

    def send_message(self, message_content):
        """消息发送主逻辑 (入口点，返回 Reply 对象)"""
        # 处理内置命令 (如模型切换、联网开关等)
        # 注意：这些命令可能只对旧版 API 有效，需要检查 api_version
        cmd_response_str = self._handle_commands(message_content)
        if cmd_response_str:
            # 包装命令响应为 Reply 对象
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = cmd_response_str
            return reply # 直接返回命令处理结果

        # 检查并创建会话（根据 API 版本）
        session_valid = False
        needs_new_chat = False

        if self.current_api_version == 'new':
            # 新版 API 的会话超时时间可能更长，例如 30 分钟 (1800 秒)
            session_timeout = 1800
            if not self.current_chat_session_id or (time.time() - self.last_active_time) > session_timeout:
                 needs_new_chat = True
            session_valid = bool(self.current_chat_session_id)
        else: # old API
            session_timeout = 180 # 旧版 3 分钟
            if not self.current_chat_id or (time.time() - self.last_active_time) > session_timeout:
                 needs_new_chat = True
            session_valid = bool(self.current_chat_id)

        if needs_new_chat:
             logger.info(f"[{self.current_api_version.upper()} API] Creating new chat session due to missing ID or timeout.")
             if not self.create_chat():
                 # 返回错误 Reply 对象
                 return Reply(ReplyType.ERROR, f"会话创建失败 ({self.current_api_version.upper()} API)")
             # 更新 session_valid 状态
             session_valid = bool(self.current_chat_session_id) if self.current_api_version == 'new' else bool(self.current_chat_id)

        if not session_valid:
            logger.error(f"[{self.current_api_version.upper()} API] Session ID still invalid after attempting creation.")
            return Reply(ReplyType.ERROR, "无法获取有效会话 ID")

        # 调用统一的发送和解析逻辑
        try:
            # 传递原始带触发词的消息内容给内部逻辑处理
            reply = self._send_and_parse_logic(message_content)
            return reply
        except Exception as e:
            logger.error(f"[Yuewen] Error in send_message calling _send_and_parse_logic: {str(e)}", exc_info=True)
            return Reply(ReplyType.ERROR, f"处理消息时发生错误: {str(e)}")

    def _send_and_parse_logic(self, original_message_content):
        """根据 API 版本发送消息并解析响应 (核心重试和分发逻辑)"""
        start_time = time.time()
        response = None
        result_reply = Reply(ReplyType.ERROR, "请求失败，请重试") # 默认错误回复

        # 从原始消息内容中移除触发词以获取要发送的纯文本
        # (如果发送函数内部也处理触发词，这里可以简化，但明确移除更安全)
        content_to_send = re.sub(f'^{self.config.get("trigger_prefix", "yw")}\s*', '', original_message_content, flags=re.IGNORECASE).strip()

        if not content_to_send:
             # 如果移除触发词后内容为空 (例如只发送了 "yw")
             return Reply(ReplyType.INFO, "请输入要发送给跃问的消息内容。")


        for retry in range(2): # 最多尝试2次 (首次 + 1次重试)
            try:
                if self.current_api_version == 'new':
                    logger.debug("[Yuewen] Using New API send/parse logic.")
                    # 调用新版发送函数
                    response = self._send_message_new(content_to_send) # 传递处理过的 content
                    if response:
                         # 调用新版解析函数 (应返回 Reply 对象)
                         result_reply = self._parse_response_new(response, start_time)
                    else:
                         # 发送函数返回 None 通常意味着严重错误，设置错误回复
                         result_reply = Reply(ReplyType.ERROR, "发送消息失败 (内部错误)")

                else: # old API
                    logger.debug("[Yuewen] Using Old API send/parse logic.")
                    # 调用旧版发送函数
                    response = self._send_message_old(content_to_send) # 传递处理过的 content
                    if response:
                         # 调用旧版解析函数 (返回字符串)
                         parsed_text = self._parse_stream_response(response, start_time)
                         # 将旧版解析结果包装成 Reply 对象
                         result_reply = Reply(ReplyType.TEXT, parsed_text)
                    else:
                         result_reply = Reply(ReplyType.ERROR, "发送消息失败 (内部错误)")


                # --- 检查响应和解析结果 ---
                # 检查 HTTP 状态码 (如果 response 对象存在)
                status_code = response.status_code if response else None
                is_successful_parse = (result_reply.type != ReplyType.ERROR and result_reply.content not in [None, ""])

                # 如果解析成功且状态码是 200 (或 response 不存在但解析"成功" - 理论不应发生)，则跳出重试
                if is_successful_parse and (status_code == 200 or response is None):
                     logger.debug(f"[{self.current_api_version.upper()}] Request successful on try {retry+1}.")
                     break # 成功，退出重试循环

                # --- 处理错误和重试 ---
                error_message = result_reply.content if result_reply.type == ReplyType.ERROR else f"请求失败 (状态码: {status_code})"
                logger.warning(f"[{self.current_api_version.upper()}] Request/Parse failed on try {retry+1}. Status: {status_code}, Message: {error_message}")

                # 如果是第一次尝试失败 (retry == 0) 并且遇到 401 或网络错误，尝试刷新 Token 并重试
                needs_refresh_retry = (retry == 0 and (status_code == 401 or response is None)) # response is None 暗示网络错误

                if needs_refresh_retry:
                    logger.info(f"[{self.current_api_version.upper()}] Attempting token refresh before retry...")
                    if self.login_handler.refresh_token():
                        logger.info(f"[{self.current_api_version.upper()}] Token refreshed successfully, retrying...")
                        continue # 继续下一次循环（重试）
                    else:
                        logger.error(f"[{self.current_api_version.upper()}] Token refresh failed.")
                        result_reply = Reply(ReplyType.ERROR, "Token 刷新失败，请尝试重新登录。")
                        break # Token 刷新失败，退出循环
                else:
                     # 如果不是需要刷新的错误，或者已经是第二次尝试，记录最终错误并退出
                     logger.error(f"[{self.current_api_version.upper()}] Request failed, not retrying or retry failed.")
                     # 使用当前的 result_reply 作为最终错误返回
                     break # 退出循环

            except (httpx.HTTPError, struct.error) as e: # 捕获网络和打包错误
                logger.error(f"[{self.current_api_version.upper()}] Request exception on try {retry+1}: {e}")
                if retry == 0:
                    logger.info(f"[{self.current_api_version.upper()}] Attempting token refresh after exception...")
                    if self.login_handler.refresh_token():
                         logger.info(f"[{self.current_api_version.upper()}] Token refreshed, retrying...")
                         continue # 重试
                # 如果刷新失败或已经是第二次尝试，设置错误回复并退出
                result_reply = Reply(ReplyType.ERROR, f"请求异常: {e}")
                break # 退出循环
            except Exception as e:
                logger.error(f"[{self.current_api_version.upper()}] General error in send/parse loop on try {retry+1}: {e}", exc_info=True)
                result_reply = Reply(ReplyType.ERROR, f"处理错误: {e}")
                break # 退出循环

        # 循环结束后，返回最终的 result_reply (可能是成功结果或最终的错误信息)
        # 更新最后活动时间（仅在成功时）
        if result_reply.type not in [ReplyType.ERROR, ReplyType.INFO]:
             self.last_active_time = time.time()

        return result_reply


    def _send_message_old(self, content_to_send):
        """发送旧版 API 消息 - **代码与旧代码.py中的发送逻辑一致**"""
        logger.debug("[Yuewen] Calling _send_message_old")
        if not self.current_chat_id:
            logger.error("[Yuewen][Old API] Missing current_chat_id for sending message.")
            return None # 返回 None 表示发送失败

        # 构造旧版协议包
        protocol_data = self._construct_protocol_packet(content_to_send)
        if not protocol_data:
            logger.error("[Yuewen][Old API] Failed to construct protocol packet.")
            return None

        headers = self._update_headers() # 获取适配旧版的 headers
        # 旧版 API 使用 connect+json
        headers['Content-Type'] = 'application/connect+json'
        url = f'{self.current_base_url}/api/proto.chat.v1.ChatMessageService/SendMessageStream'

        try:
            # 发送请求，将 httpx 异常向上抛出给 _send_and_parse_logic 处理
            response = self.client.post(url, headers=headers, content=protocol_data, timeout=60) # 增加超时
            # 不在这里检查状态码，让调用者处理
            return response
        except httpx.HTTPError as e:
            logger.error(f"[Yuewen][Old API] SendMessageStream HTTPError: {e}")
            raise e # 重新抛出，由上层处理重试
        except struct.error as e: # 捕获打包错误
             logger.error(f"[Yuewen][Old API] SendMessageStream Struct Error: {e}")
             raise e # 重新抛出
        except Exception as e:
             logger.error(f"[Yuewen][Old API] SendMessageStream Exception: {e}")
             raise e # 重新抛出

    def _send_message_new(self, content_to_send, attachments=None):
        """发送新版 API 消息"""
        logger.debug(f"[Yuewen] Calling _send_message_new with content: '{content_to_send[:50]}...', attachments: {'yes' if attachments else 'no'}")
        if not self.current_chat_session_id:
            logger.error("[Yuewen][New API] Missing current_chat_session_id for sending message.")
            return None

        # 构造新版协议包，传入纯文本内容和可能的附件
        protocol_data = self._construct_protocol_packet_new(content_to_send, attachments=attachments)
        if not protocol_data:
            logger.error("[Yuewen][New API] Failed to construct protocol packet.")
            return None

        headers = self._update_headers() # 获取适配新版的 headers
        headers['Content-Type'] = 'application/connect+json' # 新版也用 connect+json
        url = f'{self.current_base_url}/api/agent/capy.agent.v1.AgentService/ChatStream' # 假设的端点

        logger.debug(f"[Yuewen][New API] Sending ChatStream request to {url}")
        try:
            # 发送请求，将 httpx 异常向上抛出给 _send_and_parse_logic 处理
            response = self.client.post(url, headers=headers, content=protocol_data, timeout=120) # 新版可能需要更长超时
            # 不在这里检查状态码，让调用者处理
            return response
        except httpx.HTTPError as e:
            logger.error(f"[Yuewen][New API] ChatStream HTTPError: {e}")
            raise e # 重新抛出，由上层处理重试
        except struct.error as e: # 捕获打包错误
             logger.error(f"[Yuewen][New API] ChatStream Struct Error: {e}")
             raise e # 重新抛出
        except Exception as e:
             logger.error(f"[Yuewen][New API] ChatStream Exception: {e}")
             raise e # 重新抛出

    def _get_image_result_new(self, creation_id, record_id):
        """获取新版 API 图片生成结果"""
        logger.debug(f"[Yuewen][New API] _get_image_result_new called for creationId={creation_id}")
        # TODO: Implement polling/streaming logic for image result
        # TODO: Handle Connect protocol
        # TODO: Parse response and extract URL
        # Return URL string or None
        # raise NotImplementedError("_get_image_result_new not implemented yet")

        poll_interval = 3 # seconds
        max_poll_time = 180 # seconds
        start_poll_time = time.time()

        while time.time() - start_poll_time < max_poll_time:
            logger.debug(f"[Yuewen][New API] Polling image result for creationId={creation_id}, recordId={record_id}")
            headers = self._update_headers()
            headers['Content-Type'] = 'application/connect+json'
            url = f'{self.current_base_url}/api/capy.creation.v1.CreationService/GetCreationRecordResultStream'

            # Construct request payload with framing
            payload = {
                "creationId": creation_id,
                "creationRecordId": record_id
            }
            try:
                json_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
                encoded_json = json_str.encode('utf-8')
                length = len(encoded_json)
                prefix = struct.pack('>BI', 0, length)
                request_data = prefix + encoded_json
            except Exception as e:
                logger.error(f"[Yuewen][New API] Failed to construct GetCreationRecordResultStream request data: {e}")
                return None

            try:
                response = self.client.post(url, headers=headers, content=request_data, timeout=30)

                if response.status_code == 200:
                    # --- Parse Framed Response --- 
                    response_buffer = bytearray()
                    image_url = None
                    is_response_done = False
                    response_data = response.read() # Read entire response for simplicity first
                    response_buffer.extend(response_data)
                    
                    # In a real stream, you would loop here, similar to _parse_response_new
                    # For now, assume single frame response based on cURL
                    if len(response_buffer) >= 5:
                        res_flags = response_buffer[0]
                        try:
                             res_length = struct.unpack('>I', response_buffer[1:5])[0]
                             if len(response_buffer) >= 5 + res_length:
                                 res_message_bytes = response_buffer[5 : 5 + res_length]
                                 res_decoded_json = res_message_bytes.decode('utf-8')
                                 res_data = json.loads(res_decoded_json)
                                 logger.debug(f"[Yuewen][New API] GetCreationRecordResultStream response data: {res_data}")

                                 # Extract state and URL
                                 record_state = res_data.get('body', {}).get('record', {}).get('state')
                                 if record_state == 'CREATION_RECORD_STATE_SUCCESS':
                                     resources = res_data.get('body', {}).get('record', {}).get('result', {}).get('genImage', {}).get('resources', [])
                                     if resources:
                                          image_url = resources[0].get('resource', {}).get('image', {}).get('url')
                                          if image_url:
                                               logger.info("[Yuewen][New API] Image generation successful.")
                                               return image_url
                                          else:
                                               logger.error("[Yuewen][New API] Image generation success state but URL not found.")
                                               return None # Failed to find URL
                                 elif record_state == 'CREATION_RECORD_STATE_FAILED':
                                      logger.error("[Yuewen][New API] Image generation failed according to poll result.")
                                      return None # Explicit failure
                                 elif record_state == 'CREATION_RECORD_STATE_RUNNING' or record_state == 'CREATION_RECORD_STATE_PENDING':
                                       logger.debug(f"[Yuewen][New API] Image generation still in progress (State: {record_state})...")
                                       # Continue polling
                                 else:
                                      logger.warning(f"[Yuewen][New API] Unknown image generation state: {record_state}")
                                      # Continue polling, maybe temporary state

                        except (struct.error, json.JSONDecodeError, KeyError, IndexError) as e:
                             logger.error(f"[Yuewen][New API] Error parsing GetCreationRecordResultStream response frame: {e}")
                             # Don't return None immediately, maybe next poll works
                        except Exception as e:
                             logger.error(f"[Yuewen][New API] Unexpected error parsing GetCreationRecordResultStream response: {e}")
                             # Don't return None immediately

                elif response.status_code == 401:
                     logger.warning("[Yuewen][New API] Token expired during image polling. Attempting refresh...")
                     if not self.login_handler.refresh_token():
                         logger.error("[Yuewen][New API] Token refresh failed during polling. Aborting.")
                         return None
                     # Token refreshed, loop will continue with updated headers
                else:
                    logger.error(f"[Yuewen][New API] Polling request failed with status {response.status_code}: {response.text[:200]}")
                    # Consider returning None if error persists?

            except httpx.HTTPError as e:
                logger.error(f"[Yuewen][New API] Polling request HTTPError: {e}")
                # Consider returning None if error persists?
            except Exception as e:
                logger.error(f"[Yuewen][New API] Polling request Exception: {e}")
                # Consider returning None if error persists?

            # Wait before next poll
            time.sleep(poll_interval)

        logger.error("[Yuewen][New API] Image generation timed out after polling.")
        return None # Timeout

    def _handle_error(self, response):
        # Keep this generic error handler
        try:
            error = response.json()
            return f"服务错误: {error.get('error', '未知错误')}"
        except:
            return f"HTTP错误 {response.status_code}: {response.text[:200]}"

    def _get_image_data(self, msg, content):
        """获取图片数据"""
        try:
            # 如果已经是二进制数据，直接返回
            if isinstance(content, bytes):
                logger.debug(f"[Yuewen] 处理二进制数据，大小: {len(content)} 字节")
                return content

            logger.debug(f"[Yuewen] 开始处理图片，类型: {type(content)}")
            
            # 统一的文件读取函数
            def read_file(file_path):
                try:
                    with open(file_path, 'rb') as f:
                        data = f.read()
                        logger.debug(f"[Yuewen] 成功读取文件: {file_path}, 大小: {len(data)} 字节")
                        return data
                except Exception as e:
                    logger.error(f"[Yuewen] 读取文件失败 {file_path}: {e}")
                    return None
            
            # 按优先级尝试不同的读取方式
            if isinstance(content, str):
                # 1. 如果是文件路径，直接读取
                if os.path.isfile(content):
                    data = read_file(content)
                    if data:
                        return data
                
                # 2. 如果是URL，尝试下载
                if content.startswith(('http://', 'https://')):
                    logger.debug(f"[Yuewen] 尝试从URL下载: {content}")
                    try:
                        response = requests.get(content, timeout=30)
                        if response.status_code == 200:
                            return response.content
                    except Exception as e:
                        logger.error(f"[Yuewen] 从URL下载失败: {e}")
            
            # 3. 尝试从msg.content读取
            if hasattr(msg, 'content') and os.path.isfile(msg.content):
                data = read_file(msg.content)
                if data:
                    return data
            
            # 4. 如果文件未下载，尝试下载
            if hasattr(msg, '_prepare_fn') and not msg._prepared:
                logger.debug("[Yuewen] 尝试下载图片...")
                try:
                    msg._prepare_fn()
                    msg._prepared = True
                    time.sleep(1)  # 等待文件准备完成
                    
                    if hasattr(msg, 'content') and os.path.isfile(msg.content):
                        data = read_file(msg.content)
                        if data:
                            return data
                except Exception as e:
                    logger.error(f"[Yuewen] 下载图片失败: {e}")
            
            logger.error(f"[Yuewen] 无法获取图片数据")
            return None
            
        except Exception as e:
            logger.error(f"[Yuewen] 获取图片数据失败: {e}")
            return None

    def _process_image(self, image_path, prompt, e_context):
        """处理图片识别请求"""
        try:
            start_time = time.time()  # 在开始处理时就开始计时
            # 获取图片数据
            msg = e_context['context'].kwargs.get('msg')
            logger.info("[Yuewen] 开始处理图片识别请求")
            
            image_bytes = self._get_image_data(msg, image_path)
            if not image_bytes:
                return "获取图片失败，请重试"

            # 上传图片
            file_id = self._upload_image(image_bytes)
            if not file_id:
                return "图片上传失败，请重试"
            
            # --> 添加文件状态检查 <--
            if not self._check_file_status(file_id):
                logger.error(f"[Yuewen][Old API Process Image] File status check failed for file_id: {file_id}")
                return "图片处理失败，请重试"
            
            # 如果没有当前会话或会话已超时，才创建新会话
            if not self.current_chat_id or (time.time() - self.last_active_time) > 180:
                if not self.create_chat():
                    return "创建会话失败，请重试"
            
            # 获取图片尺寸
            try:
                import io
                from PIL import Image
                img = Image.open(io.BytesIO(image_bytes))
                width, height = img.size
                size = len(image_bytes)
                logger.info(f"[Yuewen] 图片尺寸: {width}x{height}, 大小: {size} 字节")
            except:
                width = height = 800
                size = len(image_bytes)
                logger.warning("[Yuewen] 无法获取图片尺寸，使用默认值")
            
            # 构建带图片的消息
            message = {
                "chatId": self.current_chat_id,
                "messageInfo": {
                    "text": prompt,
                    "attachments": [{
                        "attachmentType": "image/jpeg",
                        "attachmentId": file_id,
                        "name": f"n_v{random.getrandbits(128):032x}.jpg",
                        "width": str(width),
                        "height": str(height),
                        "size": str(size),
                        "usedPercent": -1
                    }],
                    "author": {"role": "user"}
                },
                "messageMode": "SEND_MESSAGE",
                "modelId": self.current_model_id
            }
            
            # 发送消息
            json_str = json.dumps(message, separators=(',', ':'), ensure_ascii=False)
            encoded = json_str.encode('utf-8')
            protocol_header = struct.pack('>BI', 0, len(encoded))
            
            headers = self._update_headers()
            headers['Content-Type'] = 'application/connect+json'
            
            response = self.client.post(
                'https://yuewen.cn/api/proto.chat.v1.ChatMessageService/SendMessageStream',
                headers=headers,
                content=protocol_header + encoded
            )
            
            if response.status_code != 200:
                logger.error(f"[Yuewen] 发送图片消息失败: HTTP {response.status_code}")
                return "请求失败，请重试"
            
            self.last_active_time = time.time()  # 更新最后活动时间
            return self._parse_stream_response(response, start_time)
            
        except Exception as e:
            logger.error(f"[Yuewen] 处理图片失败: {e}")
            return "处理失败，请重试"

    def _upload_image(self, image_bytes):
        """上传图片到旧版 API 服务器 - **代码与旧代码.py完全一致**"""
        if self.current_api_version == 'new':
            logger.warning("[Yuewen] _upload_image (old API) called in new API mode.")
            return None # 在新模式下调用旧上传是错误的

        logger.debug("[Yuewen][Old API] Executing _upload_image.")
        # --- Start: 旧代码逻辑 ---
        try:
            if not image_bytes:
                logger.error("[Yuewen][Old API] 图片数据为空")
                return None

            file_size = len(image_bytes)
            logger.debug(f"[Yuewen][Old API] 准备上传图片，大小: {file_size} 字节")
            file_name = f"n_v{random.getrandbits(128):032x}.jpg"
            logger.debug(f"[Yuewen][Old API] 生成的文件名: {file_name}")

            headers = self._update_headers() # 获取适配旧版的 headers
            # 添加旧版上传特有的 headers
            headers.update({
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9', # 可能通用，但在 update_headers 里设置更佳
                'cache-control': 'no-cache',
                'content-type': 'image/jpeg', # 明确指定
                'content-length': str(file_size), # 明确指定
                # 'oasis-appid': '10200', # 已在 update_headers
                # 'oasis-platform': 'web', # 已在 update_headers
                # 'origin': 'https://yuewen.cn', # 已在 update_headers
                'pragma': 'no-cache',
                # 'priority': 'u=1, i', # 已在 update_headers
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'stepchat-meta-size': str(file_size), # 旧版特有？
                # 'x-waf-client-type': 'fetch_sdk' # 已在 update_headers
            })

            # 旧版 referer 可能需要带 chat ID
            if self.current_chat_id:
                headers['referer'] = f'https://yuewen.cn/chats/{self.current_chat_id}'
            else:
                 headers['referer'] = f'https://yuewen.cn/chats/' # 备用

            upload_url = f'{self.current_base_url}/api/storage?file_name={file_name}'
            logger.debug(f"[Yuewen][Old API] 开始上传图片到: {upload_url}")

            for retry in range(2):
                try:
                    response = self.client.put(upload_url, headers=headers, content=image_bytes, timeout=45) # PUT 请求

                    if response.status_code == 200:
                        upload_result = response.json()
                        file_id = upload_result.get('id')
                        if file_id:
                            logger.debug(f"[Yuewen][Old API] 文件上传成功，ID: {file_id}")
                            # 旧版上传成功后，通常需要检查文件状态
                            if self._check_file_status(file_id): # <--- 在上传成功后检查状态
                                 logger.info(f"[Yuewen][Old API] File status check successful for ID: {file_id}")
                                 return file_id # 返回文件 ID
                            else:
                                 logger.error(f"[Yuewen][Old API] File status check failed after upload for ID: {file_id}")
                                 return None # 文件状态检查失败
                        else:
                             logger.error(f"[Yuewen][Old API] Upload success but file ID not found in response: {upload_result}")
                             return None

                    elif response.status_code == 401 and retry == 0:
                        logger.warning("[Yuewen][Old API] Token expired during upload, refreshing...")
                        if self.login_handler.refresh_token():
                            # 刷新成功后，需要更新 headers 再次尝试
                            headers = self._update_headers() # 重新获取基础 headers
                            # 重新添加上传特定 headers
                            headers.update({
                                'accept': '*/*', 'accept-language': 'zh-CN,zh;q=0.9',
                                'cache-control': 'no-cache', 'content-type': 'image/jpeg',
                                'content-length': str(file_size), 'pragma': 'no-cache',
                                'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors',
                                'sec-fetch-site': 'same-origin', 'stepchat-meta-size': str(file_size)
                            })
                            if self.current_chat_id: headers['referer'] = f'https://yuewen.cn/chats/{self.current_chat_id}'
                            else: headers['referer'] = 'https://yuewen.cn/chats/'
                            logger.info("[Yuewen][Old API] Token refreshed, retrying upload...")
                            continue # 重试
                        else:
                            logger.error("[Yuewen][Old API] Token refresh failed.")
                            return None # 刷新失败，直接返回
                    else:
                        logger.error(f"[Yuewen][Old API] 上传失败: HTTP {response.status_code} - {response.text[:200]}")
                        # 其他错误，如果是第一次尝试，可以选择重试
                        if retry < 1: continue
                        return None # 重试后仍失败或非 401 错误

                except httpx.HTTPError as e:
                    logger.error(f"[Yuewen][Old API] 上传 HTTPError: {e}")
                    if retry == 0:
                        # 网络错误也尝试刷新 token 重试
                        if self.login_handler.refresh_token(): continue
                    return None # 重试失败或刷新失败
                except Exception as e:
                    logger.error(f"[Yuewen][Old API] 上传未知错误: {e}", exc_info=True)
                    # 未知错误通常不重试
                    return None
            # 循环结束仍未成功
            logger.error("[Yuewen][Old API] Upload failed after all retries.")
            return None
        except Exception as e: # 捕获最外层的意外错误
            logger.error(f"[Yuewen][Old API] 上传图片函数 B 失败: {e}", exc_info=True)
            return None
        # --- End: 旧代码逻辑 ---

    def _upload_image_new(self, image_bytes):
        """上传图片到新版 StepFun API (使用 requests)"""
        logger.debug("[Yuewen][New API] Executing _upload_image_new.")
        if not image_bytes:
            logger.error("[Yuewen][New API] Image data is empty for upload.")
            return None

        # 新版上传端点和参数 (基于 cURL 分析)
        upload_url = f'{self.current_base_url}/api/resource/image' # 端点确认
        file_name = f"upload_{int(time.time() * 1000)}.jpg" # 简单文件名
        mime_type = 'image/jpeg' # 假设为 jpeg

        # 使用 requests 处理 multipart/form-data
        # 注意：这里引入了 requests 依赖
        try:
            import requests
        except ImportError:
            logger.error("[Yuewen] 'requests' library is required for new API image upload but not installed.")
            return None

        # 准备 Headers (从 _update_headers 获取通用部分，去除 Content-Type)
        base_headers = self._update_headers()
        upload_headers = {
            'Accept': '*/*', # 通常 multipart 不需要严格的 accept
            'Accept-Language': base_headers.get('Accept-Language', 'zh-CN,zh;q=0.9'),
            'Origin': self.current_base_url,
            'Referer': f'{self.current_base_url}/chats/', # 通用 referer
            'User-Agent': base_headers.get('User-Agent', 'Mozilla/5.0'),
            'X-Waf-Client-Type': base_headers.get('X-Waf-Client-Type', 'fetch_sdk'),
            'oasis-appid': base_headers.get('oasis-appid', '10200'),
            'oasis-platform': base_headers.get('oasis-platform', 'web'),
             # multipart/form-data 会由 requests 自动设置 Content-Type 和 boundary
        }

        # 准备 Cookies (从 _update_headers 获取，或直接从 config 读取)
        token = self.config.get('oasis_token', '')
        webid = self.config.get('oasis_webid', '')
        cookies = {
            'Oasis-Webid': webid,
            # 新版可能需要的 cookie (根据抓包)
            'i18next': 'zh',
            'sidebar_state': 'false', # 这些可能不是必须的
            'Oasis-Token': token
        }

        # 准备文件和表单数据
        files_data = {
            'file': (file_name, image_bytes, mime_type) # (文件名, 文件字节, Content-Type)
        }
        form_data = {
            'scene_id': 'image', # 场景 ID (根据抓包)
            'mime_type': mime_type # 表单里也带 mime_type
        }

        for retry in range(2):
             logger.debug(f"[Yuewen][New API] Attempting image upload (retry {retry})...")
             try:
                 # 每次尝试前确保使用最新的 token
                 cookies['Oasis-Token'] = self.config.get('oasis_token', '')

                 response = requests.post(
                     upload_url,
                     headers=upload_headers,
                     cookies=cookies,
                     files=files_data,
                     data=form_data,
                     timeout=45
                 )

                 if response.status_code == 200:
                     try:
                         result = response.json()
                         # 新版 API 返回的结构可能包含 rid, url, meta 等
                         if result.get('rid'):
                             logger.info(f"[Yuewen][New API] Image uploaded successfully. RID: {result.get('rid')}. Full response: {result}")
                             # 返回整个 JSON 字典，以便后续构造附件
                             return result
                         else:
                             logger.error(f"[Yuewen][New API] Image upload succeeded but RID not found in response: {response.text}")
                             return None
                     except json.JSONDecodeError:
                         logger.error(f"[Yuewen][New API] Failed to decode JSON from successful image upload response: {response.text}")
                         return None
                 elif response.status_code == 401 and retry == 0:
                      logger.warning("[Yuewen][New API] Token expired during image upload. Attempting refresh...")
                      if not self.login_handler.refresh_token():
                          logger.error("[Yuewen][New API] Token refresh failed during upload. Aborting.")
                          return None
                      # Token 刷新成功，循环会自动重试
                      logger.info("[Yuewen][New API] Token refreshed, retrying upload...")
                      continue
                 else:
                     logger.error(f"[Yuewen][New API] Image upload failed with status {response.status_code}: {response.text[:200]}")
                     # 其他错误，第一次尝试失败后重试
                     if retry < 1: continue
                     return None # 重试后仍失败

             except requests.exceptions.RequestException as e:
                  logger.error(f"[Yuewen][New API] Image upload requests exception: {e}")
                  if retry == 0:
                      logger.info("[Yuewen][New API] Retrying upload after requests exception...")
                      # 网络错误也尝试刷新 token
                      if not self.login_handler.refresh_token():
                           logger.error("[Yuewen][New API] Token refresh failed after requests exception. Aborting upload retry.")
                           return None
                      continue # 重试
                  else:
                      return None # 重试失败
             except Exception as e:
                 logger.error(f"[Yuewen][New API] Unexpected error during image upload: {e}", exc_info=True)
                 return None # 其他未知错误

        logger.error("[Yuewen][New API] Image upload failed after all retries.")
        return None # 所有重试失败

    def on_handle_context(self, e_context: EventContext):
        context = e_context['context']
        content = context.content

        # --- 1. 处理接收到的图片 (如果正在等待) ---
        if context.type == ContextType.IMAGE:
            msg = context.kwargs.get("msg")
            is_group = context.kwargs.get("isgroup", False)
            # 生成等待 ID
            if is_group:
                group_id = msg.other_user_id if msg else None
                real_user_id = msg.actual_user_id if msg and hasattr(msg, "actual_user_id") else None
                waiting_id = f"{group_id}_{real_user_id}" if real_user_id else group_id
            else:
                real_user_id = msg.from_user_id if msg else None
                waiting_id = real_user_id
                
            # 如果不在等待状态，直接返回
            if waiting_id not in self.waiting_for_image and waiting_id not in self.multi_image_data and waiting_id not in self.video_ref_waiting:
                e_context.action = EventAction.CONTINUE
                return
        
        # 只处理文本或特定等待中的图片消息
        if e_context['context'].type != ContextType.TEXT and e_context['context'].type != ContextType.IMAGE:
            return

        content = e_context['context'].content
        if not content:
            return

        # --- 2. 处理文本消息 ---
        if context.type == ContextType.TEXT:
            # 只有在处理文本命令时才同步状态，防止对每个图片都同步 (且仅旧版 API)
            if self.current_api_version == 'old' and time.time() - self.last_active_time > 180:  # 3分钟没活动就检查同步
                self._sync_server_state()

            content_text = content.strip()
            if not content_text: return # 忽略空消息

        # 获取用户信息
        msg = e_context['context'].kwargs.get("msg")
        is_group = e_context['context'].kwargs.get("isgroup", False)
        
        # 生成等待ID
        if is_group:
            group_id = msg.other_user_id if msg else None
            real_user_id = msg.actual_user_id if msg and hasattr(msg, "actual_user_id") else None
            waiting_id = f"{group_id}_{real_user_id}" if real_user_id else group_id
        else:
            real_user_id = msg.from_user_id if msg else None
            waiting_id = real_user_id

        # 处理图片消息
        if e_context['context'].type == ContextType.IMAGE: 
            # Check if waiting for image (New API - single image for now)
            if self.current_api_version == 'new' and waiting_id in self.waiting_for_image:
                logger.debug(f"[Yuewen] Handling image upload for New API (waiting_id: {waiting_id})")
                try:
                    image_prompt = self.waiting_for_image.pop(waiting_id) # Get prompt and clear state
                    logger.info(f"[Yuewen][New API] Received image for prompt: '{image_prompt}'")
                    
                    # Get image data
                    image_data = self._get_image_data(msg, content)
                    if not image_data:
                        raise ValueError("获取图片数据失败")
 
                    # Upload image using new API method
                    upload_result = self._upload_image_new(image_data)
                    if not upload_result or not isinstance(upload_result, dict):
                        raise ValueError("新版 API 图片上传失败或返回格式无效")
 
                    rid = upload_result.get('rid')
                    if not rid:
                        logger.error(f"[Yuewen][New API] Failed to get RID from upload result: {upload_result}")
                        raise ValueError("新版 API 图片上传失败 (缺少RID)")
 
                    # Extract other details from upload result for attachments payload
                    img_url = upload_result.get('url', '') # Get the primary URL
                    img_meta = upload_result.get('meta', {}) # Get meta info (width, height)
                    img_mimetype = upload_result.get('mimeType', 'image/jpeg') # Get mimeType, fallback
 
                    # Construct attachments payload matching cURL structure
                    attachments = [{
                        "resource": {
                            "image": {
                                "rid": rid,
                                "url": img_url,
                                "meta": img_meta,
                                "mimeType": img_mimetype
                            },
                            "rid": rid # rid also exists at this level in cURL
                        }
                    }]
                     
                    logger.info(f"[Yuewen][New API] Sending message with image RID: {rid}, URL: {img_url}, Meta: {img_meta}, and prompt: '{image_prompt}'")
                    # --- Start: Added Session Check --- 
                    # Check if session is valid before sending
                    needs_new_chat = False
                    if not self.current_chat_session_id or (time.time() - self.last_active_time) > 1800:
                        needs_new_chat = True
                     
                    if needs_new_chat:
                        logger.info(f"[Yuewen][New API Image Handling] Creating new chat session due to missing ID or timeout.")
                        if not self.create_chat():
                            raise ValueError(f"会话创建失败 ({self.current_api_version.upper()} API)")
                        else:
                            pass # Explicitly do nothing if creation succeeds
                         
                    if not self.current_chat_session_id:
                          raise ValueError("无法获取有效会话 ID")
                    # --- End: Added Session Check --- 

                    # 使用新版 API 发送带附件的消息
                    # 注意：_send_and_parse_logic 需要能处理带附件的发送
                    # 这里直接调用 _send_message_new 和 _parse_response_new 更清晰
                    start_time = time.time()
                    response = self._send_message_new(content_to_send=image_prompt, attachments=attachments) # Use content_to_send keyword
                    if response:
                        reply = self._parse_response_new(response, start_time)

                    e_context['reply'] = reply
                    e_context.action = EventAction.BREAK_PASS
                    return
                 
                except Exception as e:
                    logger.error(f"[Yuewen][New API] 处理图片识别失败: {e}", exc_info=True)
                    reply = Reply(ReplyType.ERROR, f"处理图片失败: {e}")
                    e_context['reply'] = reply
                    e_context.action = EventAction.BREAK_PASS
                    # Clean up waiting state on error
                    self.waiting_for_image.pop(waiting_id, None)
                    return
                 
            # --- Old API Multi-Image Handling --- 
            elif self.current_api_version == 'old' and waiting_id in self.multi_image_data:
                logger.debug(f"[Yuewen] Handling image upload for Old API (multi-image, waiting_id: {waiting_id})")
                # Keep existing old multi-image logic here...
                try:
                    multi_data = self.multi_image_data[waiting_id]
                    # 获取图片数据
                    image_data = self._get_image_data(msg, content)
                    if not image_data:
                        reply = Reply()
                        reply.type = ReplyType.TEXT
                        reply.content = "获取图片失败，请重试"
                        e_context['reply'] = reply
                        e_context.action = EventAction.BREAK_PASS
                        return
                    
                    # 上传图片 (Old API)
                    file_id = self._upload_image(image_data)
                    if not file_id:
                        reply = Reply()
                        reply.type = ReplyType.TEXT
                        reply.content = "图片上传失败，请重试"
                        e_context['reply'] = reply
                        e_context.action = EventAction.BREAK_PASS
                        return
                    
                    # 检查文件状态 (Old API)
                    if not self._check_file_status(file_id):
                        reply = Reply()
                        reply.type = ReplyType.TEXT
                        reply.content = "图片处理失败，请重试"
                        e_context['reply'] = reply
                        e_context.action = EventAction.BREAK_PASS
                        return
                    
                    # 获取图片尺寸 (Old API)
                    try:
                        import io
                        from PIL import Image
                        img = Image.open(io.BytesIO(image_data))
                        width, height = img.size
                        size = len(image_data)
                    except:
                        width = height = 800
                        size = len(image_data)
                    
                    # 添加图片信息
                    multi_data['images'].append({
                        'file_id': file_id,
                        'width': width,
                        'height': height,
                        'size': size
                    })
                    multi_data['current'] += 1
                    
                    # 如果图片数量达到要求
                    if multi_data['current'] >= multi_data['count']:
                        # 处理所有图片 (Old API)
                        result = self._process_multi_images(multi_data['images'], multi_data['prompt'], e_context)
                        reply = Reply()
                        reply.type = ReplyType.TEXT
                        reply.content = result
                        e_context['reply'] = reply
                        e_context.action = EventAction.BREAK_PASS
                        # 清理状态
                        del self.multi_image_data[waiting_id]
                    else:
                        # 继续等待下一张图片
                        reply = Reply()
                        reply.type = ReplyType.TEXT
                        reply.content = f"请发送第{multi_data['current'] + 1}张图片"
                        e_context['reply'] = reply
                        e_context.action = EventAction.BREAK_PASS
                    return
                    
                except Exception as e:
                    logger.error(f"[Yuewen][Old API] 处理多图片失败: {e}")
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = "处理图片失败，请重试"
                    e_context['reply'] = reply
                    e_context.action = EventAction.BREAK_PASS
                    # 清理状态
                    self.multi_image_data.pop(waiting_id, None)
                    return
            else:
                 # Image received but not waiting for it, ignore or log
                 logger.info(f"[Yuewen] Received image from {waiting_id} but not in waiting state. Ignoring.")
                 e_context.action = EventAction.CONTINUE # Let other plugins handle if needed
                 return 

        trigger = self.config.get("trigger_prefix", "yw")
        # 增加容错处理,移除多余空格并统一格式
        content = content.strip()
        if not content.lower().startswith(trigger.lower()):
            return
            
        # 移除触发词,处理多余空格
        content = re.sub(f'^{trigger}\\s*', '', content, flags=re.IGNORECASE).strip()
        
        # 获取用户ID
        msg = e_context['context'].kwargs.get("msg")
        is_group = e_context['context'].kwargs.get("isgroup", False)
        
        # 生成用户ID
        if is_group:
            group_id = msg.other_user_id if msg else None
            real_user_id = msg.actual_user_id if msg and hasattr(msg, "actual_user_id") else None
            user_id = f"{group_id}_{real_user_id}" if real_user_id else group_id
        else:
            real_user_id = msg.from_user_id if msg else None
            user_id = real_user_id
        
        # Handle version switching command
        if content.lower().startswith("切换版本"):
            parts = content.lower().split()
            if len(parts) == 2 and parts[1] in ['新版', '旧版']:
                new_version = 'new' if parts[1] == '新版' else 'old'
                if new_version == self.current_api_version:
                    reply_text = f"✅ 当前已是 {parts[1]} API 版本"
                else:
                    self.config['api_version'] = new_version
                    self.save_config(self.config)
                    self.current_api_version = new_version
                    self.current_base_url = self.base_urls.get(new_version, self.base_urls['old'])
                    self.current_chat_id = None # Reset chat on version switch
                    self.current_chat_session_id = None # Reset new version chat id
                    self.last_message = {'chat_id': None, 'messages': [], 'last_time': 0} # Reset last message
                    reply_text = f"✅ 已切换到 {parts[1]} API 版本。新会话将启动。"
                    # Optionally re-sync state after switching
                    self._sync_server_state()
            else:
                reply_text = "⚠️ 使用方法: yw 切换版本 [新版|旧版]"

            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = reply_text
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return
        
        # 检查是否是登录命令
        if content.lower() == "登录":
            return self._initiate_login(e_context, user_id)
        
        # 检查是否正在等待验证码
        if user_id in self.waiting_for_verification:
            if content.isdigit() and len(content) == 4:
                return self._verify_login(e_context, user_id, content)
            elif len(content) == 11 and content.isdigit():
                return self._send_verification_code(e_context, user_id, content)
        
        # 检查登录状态，如果配置文件丢失或token无效，自动提示登录
        if not self._check_login_status():
            # 如果是可自动注册的场景，尝试自动注册
            if not self.config.get('oasis_webid'):
                if self.login_handler.register_device():
                    logger.info("[Yuewen] 设备自动注册成功")
                else:
                    logger.error("[Yuewen] 设备自动注册失败")
                    
            if not self.is_login_triggered:
                self.is_login_triggered = True
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "⚠️ 跃问账号未登录或已失效，请先发送\"yw登录\"进行登录"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
        
        if not content:
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = "请输入要发送给跃问的消息"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 处理参考图生成视频命令
        if content.lower().startswith('参考图'):
            result = self._handle_video_ref_request(content, e_context, user_id)
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = result
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 处理识图命令,增加容错处理
        pic_trigger = self.pic_trigger_prefix
        
        # Separate handling for new and old API for image commands
        if self.current_api_version == 'new':
             # New API: Simplified single image command
             img_cmd_match_new = re.match(f'^{pic_trigger}\s*(.*)$', content, re.IGNORECASE)
             if img_cmd_match_new:
                 prompt = img_cmd_match_new.group(1).strip() if img_cmd_match_new.group(1) else self.imgprompt
                 
                 # Check for referenced image (replying to an image)
                 ref_type = e_context["context"].get("ref_type")
                 ref_content = e_context["context"].get("ref_content")
                 
                 if ref_type == "image" and ref_content:
                     logger.info("[Yuewen][New API] Detected referenced image, processing directly.")
                     try:
                         image_data = self._get_image_data(msg, ref_content)
                         if not image_data: raise ValueError("获取引用图片数据失败")
                         rid = self._upload_image_new(image_data)
                         if not rid: raise ValueError("新版 API 图片上传失败")
                         
                         attachments = [{
                             "resource": {
                                 "image": {"rid": rid, "url": "", "meta": {}, "mimeType": "image/jpeg"}, 
                                 "rid": rid
                             }
                         }]
                         logger.info(f"[Yuewen][New API] Sending message with referenced image RID: {rid} and prompt: '{prompt}'")
                         
                         # Directly call send/parse logic for new API with attachments
                         start_time = time.time()
                         response = self._send_message_new(content=prompt, attachments=attachments)
                         if response:
                             reply = self._parse_response_new(response, start_time)
                         else:
                             reply = Reply(ReplyType.ERROR, "发送图片消息失败 (无响应)")
                             
                     except Exception as e:
                         logger.error(f"[Yuewen][New API] 处理引用图片失败: {e}", exc_info=True)
                         reply = Reply(ReplyType.ERROR, f"处理引用图片失败: {e}")
                     
                     e_context['reply'] = reply
                     e_context.action = EventAction.BREAK_PASS
                     return
                 else:
                     # Not a referenced image, ask user to send one
                     self.waiting_for_image[waiting_id] = prompt # Use single image wait state
                     reply = Reply(ReplyType.TEXT, "请发送图片")
                     e_context['reply'] = reply
                     e_context.action = EventAction.BREAK_PASS
                     return
                     
        elif self.current_api_version == 'old':
             # Old API: Keep existing multi-image command logic
             img_cmd_match = re.match(f'^{pic_trigger}\s*(\d)?\s*(.*)$', content, re.IGNORECASE)
             if img_cmd_match:
                 img_count = int(img_cmd_match.group(1)) if img_cmd_match.group(1) else 1
                 prompt = img_cmd_match.group(2).strip() if img_cmd_match.group(2) else self.imgprompt
                 
                 # Check image count validity (Old API specific)
                 if img_count < 1 or img_count > self.max_images:
                     reply = Reply(ReplyType.TEXT, f"图片数量必须在1-{self.max_images}之间")
                     e_context['reply'] = reply
                     e_context.action = EventAction.BREAK_PASS
                     return
                 
                 # Check for referenced image first (Old API)
                 ref_type = e_context["context"].get("ref_type")
                 ref_content = e_context["context"].get("ref_content")
                 
                 if ref_type == "image" and ref_content:
                     logger.info("[Yuewen][Old API] 检测到引用图片，直接处理")
                     try:
                         # Use old processing function _process_image (assumes single image for ref)
                         # Make sure _process_image exists and handles single image
                         # If not, adapt or call _process_multi_images with count=1
                         result = self._process_image(ref_content, prompt, e_context) 
                         reply = Reply(ReplyType.TEXT, result)
                     except Exception as e:
                         logger.error(f"[Yuewen][Old API] 处理引用图片失败: {e}")
                         reply = Reply(ReplyType.TEXT, "处理引用图片失败，请重试")
                     
                     e_context['reply'] = reply
                     e_context.action = EventAction.BREAK_PASS
                     return
                 else:
                     # Old API: Initialize multi-image upload state if not a reference
                     logger.info(f"[Yuewen][Old API] Initializing multi-image wait state for {waiting_id}, count={img_count}")
                     self.multi_image_data[waiting_id] = {
                         'count': img_count,
                         'current': 0,
                         'images': [],
                         'prompt': prompt
                     }
                     reply = Reply(ReplyType.TEXT, f"请发送第1张图片" if img_count > 1 else "请发送图片")
                     e_context['reply'] = reply
                     e_context.action = EventAction.BREAK_PASS
                     return

        # 处理其他命令,增加容错处理
        if content.lower() == "新建会话":
            self.current_chat_id = None
            reply = Reply()
            reply.type = ReplyType.TEXT
            if self.create_chat():
                reply.content = "✅ 新会话已创建"
            else:
                reply.content = "❌ 新会话创建失败"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 处理分享命令
        if content.lower() == "分享":
            if (time.time() - self.last_message['last_time']) > 180:  # 超过3分钟
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "请先发起对话吧"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
                
            if not self.last_message['chat_id'] or not self.last_message['messages']:
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "没有可分享的对话"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
                
            image_url = self._get_share_image(
                self.last_message['chat_id'],
                self.last_message['messages']
            )
            
            if not image_url:
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "获取分享图片失败"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
                
            reply = Reply()
            reply.type = ReplyType.IMAGE_URL
            reply.content = image_url
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 处理文生视频命令
        if content.lower().startswith('视频'):
            video_prompt = content[2:].strip()  # 去掉"视频"两个字
            result = self._handle_video_request(video_prompt, e_context)
            if result:  # 只有在处理失败时才需要回复
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = result
                e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        try:
            # send_message now returns a Reply object, assign it directly
            reply = self.send_message(content)
            if reply: # Ensure send_message returned a valid Reply
                 e_context['reply'] = reply
            else: # Handle cases where send_message might return None (though it shouldn't based on current logic)
                 logger.error("[Yuewen] send_message returned None unexpectedly.")
                 reply = Reply(ReplyType.ERROR, "处理消息时内部错误")
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
        except Exception as e:
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = f"处理消息时发生错误: {str(e)}"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS

    def _enable_deep_thinking(self):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _enable_deep_thinking called in new API mode, which is not supported.")
            return False # Return False as the original function does on failure
        """开启深度思考模式"""
        for retry in range(2):
            headers = self._update_headers()
            headers['Content-Type'] = 'application/json'
            headers['referer'] = f'https://yuewen.cn/chats/{self.current_chat_id}' if self.current_chat_id else 'https://yuewen.cn/chats/new'
            try:
                response = self.client.post(
                    'https://yuewen.cn/api/proto.user.v1.UserService/EnableDeepThinking',
                    headers=headers,
                    json={}
                )
                if response.status_code == 200:
                    data = response.json()
                    if data.get("modelInUse", {}).get("id") == 6:
                        logger.info("[Yuewen] 深度思考模式启用成功")
                        return True
                elif response.status_code == 401 and retry == 0:
                    if self.login_handler.refresh_token():
                        continue
                    else:
                        logger.error("[Yuewen] Token刷新失败")
                logger.error(f"[Yuewen] 深度思考模式启用失败: {response.text}")
                return False
            except Exception as e:
                if retry == 0:
                    continue
                logger.error(f"[Yuewen] 深度思考模式启用失败: {str(e)}")
                return False
        return False

    def _sync_server_state(self):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _sync_server_state called in new API mode, which is not supported.")
            return False # Return False as the original function does on failure
        """同步服务器端的模型和联网状态"""
        for retry in range(2):  # 添加重试机制
            try:
                logger.info("[Yuewen] 开始同步服务器状态")
                headers = self._update_headers()
                headers['Content-Type'] = 'application/json'
                
                # 获取服务器端用户状态
                response = self.client.post(
                    'https://yuewen.cn/api/proto.user.v1.UserService/GetUser',
                    headers=headers,
                    json={}
                )
                
                if response.status_code == 401 and retry == 0:
                    # 尝试刷新令牌
                    if self.login_handler.refresh_token():
                        logger.info("[Yuewen] 令牌刷新成功,重试获取状态")
                        continue
                    else:
                        logger.error("[Yuewen] Token刷新失败")
                        return False
                
                if response.status_code != 200:
                    logger.error(f"[Yuewen] 获取用户状态失败: HTTP {response.status_code}")
                    return False
                    
                data = response.json()
                if 'user' not in data:
                    logger.error("[Yuewen] 获取用户状态失败: 无效响应")
                    return False
                    
                user_data = data['user']
                server_model_id = user_data.get('modelInUse')
                server_search_enabled = user_data.get('enableSearch', False)
                
                changes_made = False
                
                # 检查模型是否需要同步
                if server_model_id != self.current_model_id:
                    logger.info(f"[Yuewen] 检测到模型不一致: 本地({self.current_model_id}) vs 服务器({server_model_id})")
                    if self._call_set_model(self.current_model_id):
                        logger.info(f"[Yuewen] 已将服务器模型同步为: {self.current_model_id}")
                        changes_made = True
                        
                        # 如果是 r1 模型，确保启用深度思考
                        if self.current_model_id == 6:
                            if self._enable_deep_thinking():
                                logger.info("[Yuewen] 已启用深度思考模式")
                            else:
                                logger.warning("[Yuewen] 深度思考模式启用失败")
                    else:
                        logger.error("[Yuewen] 模型同步失败")
                        return False
                
                # 检查联网状态是否需要同步
                current_model = next((m for m in self.models.values() if m['id'] == self.current_model_id), None)
                if current_model and current_model['can_network']:
                    config_network_mode = self.config.get('network_mode', True)
                    if server_search_enabled != config_network_mode:
                        logger.info(f"[Yuewen] 检测到联网状态不一致: 本地({config_network_mode}) vs 服务器({server_search_enabled})")
                        if self._enable_search(config_network_mode):
                            logger.info(f"[Yuewen] 已将服务器联网状态同步为: {config_network_mode}")
                            changes_made = True
                        else:
                            logger.error("[Yuewen] 联网状态同步失败")
                            return False
                
                # 如果有任何更改，创建新会话
                if changes_made:
                    self.current_chat_id = None
                    if not self.create_chat():
                        logger.error("[Yuewen] 创建新会话失败")
                        return False
                        
                return True
                
            except Exception as e:
                if retry == 0:
                    # 如果是第一次失败,尝试刷新令牌后重试
                    if self.login_handler.refresh_token():
                        logger.info("[Yuewen] 令牌刷新成功,重试获取状态")
                        continue
                logger.error(f"[Yuewen] 同步服务器状态失败: {str(e)}")
                return False
        return False

    def _check_file_status(self, file_id):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _check_file_status called in new API mode, which is not supported.")
            return False # Return False as the original function does on failure
        """检查文件状态"""
        max_retries = 5  # 最大重试次数
        retry_interval = 0.5  # 重试间隔(秒)
        
        headers = self._update_headers()
        headers.update({
            'Content-Type': 'application/json',
            'canary': 'false',
            'connect-protocol-version': '1',
            'oasis-appid': '10200',
            'oasis-mode': '2',
            'oasis-platform': 'web',
            'priority': 'u=1, i',
            'x-waf-client-type': 'fetch_sdk'
        })
        
        for i in range(max_retries):
            try:
                response = self.client.post(
                    'https://yuewen.cn/api/proto.file.v1.FileService/GetFileStatus',
                    headers=headers,
                    json={"id": file_id}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("fileStatus") == 1:  # 1表示成功
                        return True
                    elif not data.get("needFurtherCall", True):  # 如果不需要继续查询
                        return False
                elif response.status_code == 401:
                    if self.login_handler.refresh_token():
                        continue
                    return False
                    
                time.sleep(retry_interval)
            except Exception as e:
                logger.error(f"[Yuewen] 检查文件状态失败: {str(e)}")
                if i < max_retries - 1:  # 如果不是最后一次重试
                    time.sleep(retry_interval)
        
        return False

    def _process_multi_images(self, images, prompt, e_context):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _process_multi_images called in new API mode, which is not supported.")
            return "⚠️ 此功能仅支持旧版 API。"
        """处理多张图片"""
        try:
            if not self.current_chat_id:
                if not self.create_chat():
                    return "创建会话失败"
            
            # 构建带多图片的消息
            attachments = []
            for idx, img in enumerate(images, 1):
                attachments.append({
                    "attachmentType": "image/jpeg",
                    "attachmentId": img['file_id'],
                    "name": f"{idx}.jpg",
                    "width": str(img['width']),
                    "height": str(img['height']),
                    "size": str(img['size']),
                    "usedPercent": -1
                })
            
            message = {
                "chatId": self.current_chat_id,
                "messageInfo": {
                    "text": prompt,
                    "attachments": attachments,
                    "author": {"role": "user"}
                },
                "messageMode": "SEND_MESSAGE",
                "modelId": self.current_model_id
            }
            
            # 发送消息
            json_str = json.dumps(message, separators=(',', ':'), ensure_ascii=False)
            encoded = json_str.encode('utf-8')
            protocol_header = struct.pack('>BI', 0, len(encoded))
            
            headers = self._update_headers()
            headers['Content-Type'] = 'application/connect+json'
            
            start_time = time.time()
            response = self.client.post(
                'https://yuewen.cn/api/proto.chat.v1.ChatMessageService/SendMessageStream',
                headers=headers,
                content=protocol_header + encoded,
                timeout=30
            )
            
            if response.status_code != 200:
                logger.error(f"[Yuewen] 发送多图片消息失败: HTTP {response.status_code}")
                return "请求失败，请重试"
            
            self.last_active_time = time.time()
            return self._parse_stream_response(response, start_time)
            
        except Exception as e:
            logger.error(f"[Yuewen] 处理多图片失败: {e}")
            return "处理失败，请重试"

    def _get_share_image(self, chat_id, messages):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _get_share_image called in new API mode, which is not supported.")
            return None # Return None as the original function does on failure
        """获取分享图片"""
        try:
            # 第一步：获取分享ID
            headers = self._update_headers()
            headers.update({
                'Content-Type': 'application/json',
                'canary': 'false',
                'connect-protocol-version': '1',
                'oasis-appid': '10200',
                'oasis-mode': '2',
                'oasis-platform': 'web',
                'x-waf-client-type': 'fetch_sdk'
            })
            
            share_data = {
                "chatId": chat_id,
                "selectedMessageList": messages,
                "needTitle": True
            }
            
            response = self.client.post(
                'https://yuewen.cn/api/proto.chat.v1.ChatService/ChatShareSelectMessage',
                headers=headers,
                json=share_data
            )
            
            if response.status_code != 200:
                return None
                
            share_result = response.json()
            chat_share_id = share_result.get('chatShareId')
            if not chat_share_id:
                return None
                
            # 第二步：生成分享图片
            poster_data = {
                "chatShareId": chat_share_id,
                "pageSize": 10,
                "shareUrl": f"https://yuewen.cn/share/{chat_share_id}?utm_source=share&utm_content=web_image_share&version=2",
                "width": 430,
                "scale": 3
            }
            
            response = self.client.post(
                'https://yuewen.cn/api/proto.shareposter.v1.SharePosterService/GenerateChatSharePoster',
                headers=headers,
                json=poster_data
            )
            
            if response.status_code != 200:
                return None
                
            poster_result = response.json()
            return poster_result.get('staticUrl')
            
        except Exception as e:
            logger.error(f"[Yuewen] 获取分享图片失败: {e}")
            return None

    def _handle_video_ref_request(self, content, e_context, user_id):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _handle_video_ref_request called in new API mode, which is not supported.")
            return "⚠️ 此功能仅支持旧版 API。"
        """处理参考图生成视频请求"""
        try:
            # 解析命令，检查是否需要润色
            prompt = content.replace("参考图", "", 1).strip()
            use_rephrase = False
            camera_list = []
            
            # 检查是否有润色标志
            if "-润色" in prompt:
                use_rephrase = True
                prompt = prompt.replace("-润色", "").strip()
            
            # 检查是否有镜头语言
            for short_name, full_name in self.camera_movements.items():
                if f"-{short_name}" in prompt:
                    camera_list.append(full_name)
                    prompt = prompt.replace(f"-{short_name}", "").strip()
            
            if not prompt:
                return "❌ 请提供提示词"
            
            # 检查是否有引用图片
            ref_type = e_context["context"].get("ref_type")
            ref_content = e_context["context"].get("ref_content")
            
            if ref_type == "image" and ref_content:
                logger.info("[Yuewen] 检测到引用图片，直接处理")
                try:
                    return self._handle_video_ref_image(ref_content, prompt, e_context, use_rephrase, camera_list)
                except Exception as e:
                    logger.error(f"[Yuewen] 处理引用图片失败: {e}")
                    return "❌ 处理引用图片失败，请重试"
            
            # 创建等待图片状态
            msg = e_context['context'].kwargs.get("msg")
            is_group = e_context['context'].kwargs.get("isgroup", False)
            
            # 生成等待ID
            if is_group:
                group_id = msg.other_user_id if msg else None
                real_user_id = msg.actual_user_id if msg and hasattr(msg, "actual_user_id") else None
                waiting_id = f"{group_id}_{real_user_id}" if real_user_id else group_id
            else:
                real_user_id = msg.from_user_id if msg else None
                waiting_id = real_user_id
            
            # 设置等待状态
            self.video_ref_waiting[waiting_id] = {
                'prompt': prompt,
                'use_rephrase': use_rephrase,
                'camera_list': camera_list
            }
            
            return "请发送一张参考图片用于生成视频"
            
        except Exception as e:
            logger.error(f"[Yuewen] 处理参考图视频请求失败: {e}")
            return "❌ 处理请求失败，请重试"

    def _handle_video_ref_image(self, image_path, prompt, e_context, use_rephrase=False, camera_list=None):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _handle_video_ref_image called in new API mode, which is not supported.")
            return "⚠️ 此功能仅支持旧版 API。"
        """处理参考图生成视频"""
        try:
            # 开始计时
            start_time = time.time()
            
            # 获取图片数据
            msg = e_context['context'].kwargs.get('msg')
            logger.info("[Yuewen] 开始处理参考图生成视频请求")
            
            image_bytes = self._get_image_data(msg, image_path)
            if not image_bytes:
                return "❌ 获取图片失败，请重试"

            # 上传图片
            file_id = self._upload_image(image_bytes)
            if not file_id:
                return "❌ 图片上传失败，请重试"
            
            # 检查文件状态
            if not self._check_file_status(file_id):
                return "❌ 图片处理失败，请重试"
            
            # 润色提示词(如果需要)
            if use_rephrase:
                rephrased_prompt = self._rephrase_video_prompt(prompt)
                if rephrased_prompt:
                    logger.info(f"[Yuewen] 提示词润色成功: {prompt} -> {rephrased_prompt}")
                    prompt = rephrased_prompt
                else:
                    logger.warning("[Yuewen] 提示词润色失败，使用原始提示词")
                
            # 发送视频生成请求
            video_task = self._send_video_ref_request(prompt, file_id, camera_list)
            if not video_task:
                return "❌ 视频生成请求失败，请重试"
            
            video_sqid = video_task.get("videoSqid")
            if not video_sqid:
                return "❌ 无效的视频任务ID"
            
            # 发送正在处理的消息
            reply = Reply()
            reply.type = ReplyType.TEXT
            
            camera_info = f"使用镜头语言: {', '.join(camera_list)}" if camera_list else ""
            reply.content = f"视频生成任务已提交，预计需要3-5分钟，请耐心等待...\n提示词: {prompt}\n{camera_info}"
            e_context["channel"].send(reply, e_context["context"])
            
            # 轮询任务状态
            video_url = self._check_video_ref_status(video_sqid)
            if not video_url:
                return "❌ 视频生成失败或超时，请重试"
            
            # 计算总耗时
            total_time = time.time() - start_time
            logger.info(f"[Yuewen] 视频生成完成，耗时: {total_time:.2f}秒")
            
            # 发送成功消息
            success_message = f"✅ 视频生成成功！耗时: {total_time:.2f}秒"
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = success_message
            e_context["channel"].send(reply, e_context["context"])
            
            # 发送视频URL
            video_reply = Reply(ReplyType.VIDEO_URL, video_url)
            e_context["channel"].send(video_reply, e_context["context"])
            
            return None  # 返回None表示已经处理完毕
            
        except Exception as e:
            logger.error(f"[Yuewen] 处理参考图生成视频失败: {e}")
            return "❌ 处理失败，请重试"

    def _send_video_ref_request(self, prompt, file_id, camera_list=None):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _send_video_ref_request called in new API mode, which is not supported.")
            return None # Return None as the original function does on failure
        """发送参考图生成视频请求"""
        try:
            headers = self._update_headers()
            headers.update({
                'Content-Type': 'application/json',
                'canary': 'false',
                'connect-protocol-version': '1',
                'oasis-appid': '10200',
                'oasis-mode': '2',
                'oasis-platform': 'web',
                'x-waf-client-type': 'fetch_sdk'
            })
            
            image_url = f"https://yuewen.cn/api/storage?id={file_id}"
            
            # 构建请求数据
            data = {
                "prompt": prompt,
                "imageUrl": image_url,
                "type": "VIDEO_TASK_TYPE_IMAGE_TO_VIDEO"
            }
            
            # 添加镜头语言列表
            if camera_list and len(camera_list) > 0:
                data["cameraList"] = camera_list
            
            for retry in range(2):
                try:
                    response = self.client.post(
                        'https://yuewen.cn/api/proto.video.v1.VideoService/PostVideoTask',
                        headers=headers,
                        json=data
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if "videoId" in data and "videoSqid" in data:
                            logger.info(f"[Yuewen] 视频任务创建成功: ID={data['videoId']}, SQID={data['videoSqid']}")
                            return data
                    elif response.status_code == 401 and retry == 0:
                        if self.login_handler.refresh_token():
                            continue
                        else:
                            logger.error("[Yuewen] Token刷新失败")
                    logger.error(f"[Yuewen] 创建视频任务失败: {response.text}")
                    return None
                except Exception as e:
                    if retry == 0:
                        continue
                    logger.error(f"[Yuewen] 创建视频任务失败: {str(e)}")
                    return None
            return None
        except Exception as e:
            logger.error(f"[Yuewen] 创建视频任务异常: {str(e)}")
            return None

    def _check_video_ref_status(self, task_id, max_retries=180):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _check_video_ref_status called in new API mode, which is not supported.")
            return None # Return None as the original function does on failure
        """检查视频生成状态"""
        retry_interval = 10  # 重试间隔为10秒
        last_status_time = 0  # 上次状态输出时间
        status_interval = 30  # 状态输出间隔(秒)
        
        headers = self._update_headers()
        headers.update({
            'Content-Type': 'application/json',
            'canary': 'false',
            'connect-protocol-version': '1',
            'oasis-appid': '10200',
            'oasis-mode': '2',
            'oasis-platform': 'web',
            'x-waf-client-type': 'fetch_sdk'
        })
        
        for i in range(max_retries):
            current_time = time.time()
            try:
                response = self.client.post(
                    'https://yuewen.cn/api/proto.video.v1.VideoService/GetVideoFeed',
                    headers=headers,
                    json={"videoSqid": task_id}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    feed = data.get("feed", {})
                    status = feed.get("status")
                    
                    # 输出任务状态(每30秒)
                    if current_time - last_status_time >= status_interval:
                        task_desc = feed.get("taskDesc", "处理中...")
                        logger.info(f"[Yuewen] 视频生成状态: {status}, {task_desc}")
                        last_status_time = current_time
                    
                    # 判断任务是否完成
                    if status == "GENERATED":
                        content = feed.get("content", {})
                        video_url = content.get("url")
                        if video_url:
                            logger.info(f"[Yuewen] 视频生成完成: {video_url}")
                            return video_url
                        else:
                            logger.error("[Yuewen] 视频地址为空")
                            return None
                    elif status == "FAILED":
                        logger.error(f"[Yuewen] 视频生成失败: {feed.get('failReason', '未知错误')}")
                        return None
                elif response.status_code == 401 and i < max_retries - 1:
                    if self.login_handler.refresh_token():
                        headers = self._update_headers()
                        headers.update({
                            'Content-Type': 'application/json',
                            'canary': 'false',
                            'connect-protocol-version': '1',
                            'oasis-appid': '10200',
                            'oasis-mode': '2',
                            'oasis-platform': 'web',
                            'x-waf-client-type': 'fetch_sdk'
                        })
                    else:
                        logger.error("[Yuewen] Token刷新失败")
                
                # 如果不是最后一次轮询，则等待
                if i < max_retries - 1:
                    time.sleep(retry_interval)
                
            except Exception as e:
                logger.error(f"[Yuewen] 检查视频状态失败: {str(e)}")
                if i < max_retries - 1:
                    time.sleep(retry_interval)
        
        logger.error(f"[Yuewen] 视频生成超时")
        return None

    def _refresh_token(self):
        """刷新令牌"""
        # 限制刷新频率，避免频繁请求
        current_time = time.time()
        if current_time - self.last_token_refresh < 60:  # 1分钟内只刷新一次
            logger.debug("[Yuewen] 令牌刷新频率限制，跳过")
            return False
        
        try:
            logger.info("[Yuewen] 开始刷新令牌")
            headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'canary': 'false',
                'connect-protocol-version': '1',
                'content-type': 'application/json',
                'cookie': f"Oasis-Webid={self.config['oasis_webid']}; Oasis-Token={self.config['oasis_token']}",
                'oasis-appid': '10200',
                'oasis-mode': '2',
                'oasis-platform': 'web',
                'oasis-webid': self.config['oasis_webid'],
                'origin': 'https://yuewen.cn',
                'priority': 'u=1, i',
                'x-waf-client-type': 'fetch_sdk'
            }
            
            response = self.client.post(
                'https://yuewen.cn/passport/proto.api.passport.v1.PassportService/RefreshToken',
                headers=headers,
                json={}
            )
            
            if response.status_code == 200:
                data = response.json()
                access_token = data.get('accessToken', {}).get('raw')
                refresh_token = data.get('refreshToken', {}).get('raw')
                
                if access_token and refresh_token:
                    # 更新令牌
                    self.config['oasis_token'] = f"{access_token}...{refresh_token}"
                    self.login_handler.config = self.config
                    self.login_handler.save_config()
                    
                    self.last_token_refresh = current_time
                    logger.info("[Yuewen] 令牌刷新成功")
                    return True
                
            logger.error(f"[Yuewen] 令牌刷新失败: HTTP {response.status_code}")
            return False
        except Exception as e:
            logger.error(f"[Yuewen] 令牌刷新异常: {str(e)}")
            return False

    def _handle_video_request(self, content, e_context):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _handle_video_request called in new API mode, which is not supported.")
            return "⚠️ 此功能仅支持旧版 API。"
        """处理文生视频请求"""
        try:
            # 解析命令
            # 处理润色和镜头语言选项
            prompt = content
            use_rephrase = False
            camera_list = []
            
            # 检查是否有润色标志
            if "-润色" in content:
                use_rephrase = True
                prompt = content.replace("-润色", "").strip()
            
            # 检查是否有镜头语言
            for short_name, full_name in self.camera_movements.items():
                if f"-{short_name}" in prompt:
                    camera_list.append(full_name)
                    prompt = prompt.replace(f"-{short_name}", "").strip()
            
            if not prompt:
                return "❌ 请提供视频生成提示词"
                
            # 润色提示词(如果需要)
            if use_rephrase:
                rephrased_prompt = self._rephrase_video_prompt(prompt)
                if rephrased_prompt:
                    logger.info(f"[Yuewen] 提示词润色成功: {prompt} -> {rephrased_prompt}")
                    prompt = rephrased_prompt
                else:
                    logger.warning("[Yuewen] 提示词润色失败，使用原始提示词")
                
            # 发送视频生成请求
            video_task = self._send_video_gen_request(prompt, camera_list=camera_list)
            if not video_task:
                return "❌ 视频生成请求失败，请重试"
                
            video_sqid = video_task.get("videoSqid")
            if not video_sqid:
                return "❌ 无效的视频任务ID"
                
            # 发送正在处理的消息
            reply = Reply()
            reply.type = ReplyType.TEXT
            
            camera_info = f"使用镜头语言: {', '.join(camera_list)}" if camera_list else ""
            reply.content = f"视频生成任务已提交，预计需要3-5分钟，请耐心等待...\n提示词: {prompt}\n{camera_info}"
            e_context["channel"].send(reply, e_context["context"])
            
            # 轮询任务状态
            video_url = self._check_video_status(video_sqid, max_retries=36)  # 最多等待6分钟
            if not video_url:
                return "❌ 视频生成失败或超时，请重试"
                
            # 发送成功消息
            success_message = f"✅ 视频生成成功！"
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = success_message
            e_context["channel"].send(reply, e_context["context"])
            
            # 发送视频URL
            video_reply = Reply(ReplyType.VIDEO_URL, video_url)
            e_context["channel"].send(video_reply, e_context["context"])
            
            return None  # 返回None表示已经处理完毕
            
        except Exception as e:
            logger.error(f"[Yuewen] 处理视频生成请求失败: {e}")
            return "❌ 处理请求失败，请重试"

    def _send_video_gen_request(self, prompt, resolution="992*544", use_pro_model=False, camera_list=None):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _send_video_gen_request called in new API mode, which is not supported.")
            return None # Return None as the original function does on failure
        """发送视频生成请求"""
        try:
            headers = self._update_headers()
            headers.update({
                'Content-Type': 'application/json',
                'canary': 'false',
                'connect-protocol-version': '1',
                'oasis-appid': '10200', 
                'oasis-mode': '2',
                'oasis-platform': 'web',
                'x-waf-client-type': 'fetch_sdk'
            })
            
            # 构建请求数据
            data = {
                "prompt": prompt,
                "type": "VIDEO_TASK_TYPE_TEXT_TO_VIDEO",
            }
            
            # 添加镜头语言列表
            if camera_list and len(camera_list) > 0:
                data["cameraList"] = camera_list
                
            for retry in range(2):
                try:
                    response = self.client.post(
                        'https://yuewen.cn/api/proto.video.v1.VideoService/PostVideoTask',
                        headers=headers,
                        json=data
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if "videoId" in data and "videoSqid" in data:
                            logger.info(f"[Yuewen] 视频任务创建成功: ID={data['videoId']}, SQID={data['videoSqid']}")
                            return data
                    elif response.status_code == 401 and retry == 0:
                        if self._refresh_token():
                            headers = self._update_headers()
                            headers.update({
                                'Content-Type': 'application/json',
                                'canary': 'false',
                                'connect-protocol-version': '1',
                                'oasis-appid': '10200',
                                'oasis-mode': '2',
                                'oasis-platform': 'web',
                                'x-waf-client-type': 'fetch_sdk'
                            })
                            continue
                        else:
                            logger.error("[Yuewen] Token刷新失败")
                    logger.error(f"[Yuewen] 创建视频任务失败: {response.text}")
                    return None
                except Exception as e:
                    if retry == 0:
                        continue
                    logger.error(f"[Yuewen] 创建视频任务失败: {str(e)}")
                    return None
            return None
        except Exception as e:
            logger.error(f"[Yuewen] 创建视频任务异常: {str(e)}")
            return None

    def _check_video_status(self, task_id, max_retries=36):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _check_video_status called in new API mode, which is not supported.")
            return None # Return None as the original function does on failure
        """检查视频生成状态"""
        retry_interval = 10  # 重试间隔为10秒
        last_status_time = 0  # 上次状态输出时间
        status_interval = 30  # 状态输出间隔(秒)
        
        headers = self._update_headers()
        headers.update({
            'Content-Type': 'application/json',
            'canary': 'false',
            'connect-protocol-version': '1',
            'oasis-appid': '10200',
            'oasis-mode': '2',
            'oasis-platform': 'web',
            'x-waf-client-type': 'fetch_sdk'
        })
        
        for i in range(max_retries):
            current_time = time.time()
            try:
                response = self.client.post(
                    'https://yuewen.cn/api/proto.video.v1.VideoService/GetVideoFeed',
                    headers=headers,
                    json={"videoSqid": task_id}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    feed = data.get("feed", {})
                    status = feed.get("status")
                    
                    # 输出任务状态(每30秒)
                    if current_time - last_status_time >= status_interval:
                        task_desc = feed.get("taskDesc", "处理中...")
                        logger.info(f"[Yuewen] 视频生成状态: {status}, {task_desc}")
                        last_status_time = current_time
                    
                    # 判断任务是否完成
                    if status == "GENERATED":
                        content = feed.get("content", {})
                        video_url = content.get("url")
                        if video_url:
                            logger.info(f"[Yuewen] 视频生成完成: {video_url}")
                            return video_url
                        else:
                            logger.error("[Yuewen] 视频地址为空")
                            return None
                    elif status == "FAILED":
                        logger.error(f"[Yuewen] 视频生成失败: {feed.get('failReason', '未知错误')}")
                        return None
                elif response.status_code == 401 and i < max_retries - 1:
                    if self._refresh_token():
                        headers = self._update_headers()
                        headers.update({
                            'Content-Type': 'application/json',
                            'canary': 'false',
                            'connect-protocol-version': '1',
                            'oasis-appid': '10200',
                            'oasis-mode': '2',
                            'oasis-platform': 'web',
                            'x-waf-client-type': 'fetch_sdk'
                        })
                    else:
                        logger.error("[Yuewen] Token刷新失败")
                
                # 如果不是最后一次轮询，则等待
                if i < max_retries - 1:
                    time.sleep(retry_interval)
                    
            except Exception as e:
                logger.error(f"[Yuewen] 检查视频状态失败: {str(e)}")
                if i < max_retries - 1:
                    time.sleep(retry_interval)
        
        logger.error(f"[Yuewen] 视频生成超时")
        return None

    def _rephrase_video_prompt(self, prompt):
        if self.current_api_version == 'new':
            logger.warning(f"[Yuewen] Function _rephrase_video_prompt called in new API mode, which is not supported.")
            return None # Return None as the original function does on failure
        """润色视频提示词"""
        try:
            headers = self._update_headers()
            headers.update({
                'Content-Type': 'application/json',
                'canary': 'false',
                'connect-protocol-version': '1',
                'oasis-appid': '10200',
                'oasis-mode': '2',
                'oasis-platform': 'web',
                'x-waf-client-type': 'fetch_sdk'
            })
            
            for retry in range(2):
                try:
                    response = self.client.post(
                        'https://yuewen.cn/api/proto.video.v1.VideoService/RephraseVideoPrompt',
                        headers=headers,
                        json={"prompt": prompt}
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if "rephrasedPrompt" in data:
                            return data["rephrasedPrompt"]
                    elif response.status_code == 401 and retry == 0:
                        if self._refresh_token():
                            continue
                        else:
                            logger.error("[Yuewen] Token刷新失败")
                    logger.error(f"[Yuewen] 提示词润色失败: {response.text}")
                    return None
                except Exception as e:
                    if retry == 0:
                        continue
                    logger.error(f"[Yuewen] 提示词润色失败: {str(e)}")
                    return None
            return None
        except Exception as e:
            logger.error(f"[Yuewen] 提示词润色异常: {str(e)}")
            return None

    def _check_login_status(self):
        """检查登录状态"""
        # 检查配置是否完整
        if not self.config.get('oasis_webid') or not self.config.get('oasis_token'):
            logger.info("[Yuewen] 缺少登录信息，需要登录")
            return False
        
        # 尝试刷新token验证是否有效
        try:
            if self.login_handler.refresh_token():
                logger.info("[Yuewen] 登录状态有效")
                return True
        except Exception as e:
            logger.error(f"[Yuewen] Token refresh check failed: {e}", exc_info=True) # Log exception if refresh fails

        # 如果刷新失败，标记为需要登录
        self.config["need_login"] = True
        self.save_config(self.config)
        logger.info("[Yuewen] 登录已失效，需要重新登录")
        return False

    def _initiate_login(self, e_context, user_id):
        """发起登录流程"""
        try:
            # 如果没有注册设备，先注册
            if not self.config.get('oasis_webid'):
                if not self.login_handler.register_device():
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = "❌ 设备注册失败，请稍后重试"
                    e_context['reply'] = reply
                    e_context.action = EventAction.BREAK_PASS
                    return
            
            # 提示用户输入手机号
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = "📱 请输入您的11位手机号码"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            
            # 将用户标记为等待验证码状态(临时存储空字符串)
            self.waiting_for_verification[user_id] = ""
            return
        except Exception as e:
            logger.error(f"[Yuewen] 发起登录流程失败: {e}")
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = f"❌ 登录流程启动失败: {str(e)}"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

    def _send_verification_code(self, e_context, user_id, phone_number):
        """发送验证码"""
        try:
            if self.login_handler.send_verify_code(phone_number):
                # 更新用户状态，保存手机号
                self.waiting_for_verification[user_id] = phone_number
                
                reply = Reply()
                reply.type = ReplyType.TEXT
                # 修改这里，移除手机号码显示
                reply.content = "✅ 验证码已发送，请输入收到的4位数验证码"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
            else:
                # 发送验证码失败
                self.waiting_for_verification.pop(user_id, None)
                
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "❌ 验证码发送失败，请重新发送\"yw登录\"进行登录"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
        except Exception as e:
            logger.error(f"[Yuewen] 发送验证码失败: {e}")
            self.waiting_for_verification.pop(user_id, None)
            
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = f"❌ 验证码发送失败: {str(e)}"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

    def _verify_login(self, e_context, user_id, verify_code):
        """验证登录"""
        try:
            # 获取之前保存的手机号
            phone_number = self.waiting_for_verification.get(user_id)
            if not phone_number:
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "❌ 验证失败：请先发送手机号获取验证码"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
            
            # 验证登录
            login_result = self.login_handler.verify_login(phone_number, verify_code)
            if login_result:
                # 清除等待状态
                self.waiting_for_verification.pop(user_id, None)
                
                # 确保配置保存成功 - 这里已经在 verify_login 方法内保存了
                
                # 创建新会话
                self.create_chat()
                
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "✅ 登录成功！现在可以正常使用跃问功能了"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
            else:
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "❌ 验证码错误或已过期，请重新发送\"yw登录\"进行登录"
                e_context['reply'] = reply
                e_context.action = EventAction.BREAK_PASS
                return
        except Exception as e:
            logger.error(f"[Yuewen] 验证登录失败: {e}")
            self.waiting_for_verification.pop(user_id, None)
            
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = f"❌ 验证登录失败: {str(e)}"
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS
            return

    # --- New API Image Upload --- 
    def _upload_image_new(self, image_bytes):
        """上传图片到新版 StepFun API"""
        if not image_bytes:
            logger.error("[Yuewen][New API] Image data is empty for upload.")
            return None

        upload_url = f'{self.current_base_url}/api/resource/image'
        file_name = f"upload_{int(time.time())}.jpg" # Simple filename

        # Need to use multipart/form-data, httpx doesn't directly support complex boundary generation like curl easily.
        # Using requests library temporarily for simpler multipart handling.
        # TODO: Refactor to use httpx if possible, or keep requests as a dependency for this.
        import requests # Temporary import

        headers = {
            # Base headers might not apply directly due to different Content-Type
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Origin': self.current_base_url,
            'Referer': f'{self.current_base_url}/chats/', # Generic referer
            'User-Agent': self.login_handler.base_headers.get('User-Agent', 'Mozilla/5.0'), # Reuse user agent
            'X-Waf-Client-Type': 'fetch_sdk',
            'oasis-appid': '10200',
            'oasis-platform': 'web',
        }

        # Construct cookies
        token = self.config.get('oasis_token', '')
        webid = self.config.get('oasis_webid', '')
        cookies = {
            'Oasis-Webid': webid,
            'i18next': 'zh',
            'sidebar_state': 'false',
            'Oasis-Token': token
        }

        files = {
            'file': (file_name, image_bytes, 'image/jpeg') # filename, file-like object, content_type
        }
        data = {
            'scene_id': 'image',
            'mime_type': 'image/jpeg'
        }

        for retry in range(2):
             logger.debug(f"[Yuewen][New API] Attempting image upload (retry {retry})...")
             try:
                 # Update cookies with potentially refreshed token before request
                 cookies['Oasis-Token'] = self.config.get('oasis_token', '')
                 
                 response = requests.post(upload_url, headers=headers, cookies=cookies, files=files, data=data, timeout=45)
                 
                 if response.status_code == 200:
                     try:
                         result = response.json()
                         rid = result.get('rid')
                         if rid:
                             logger.info(f"[Yuewen][New API] Image uploaded successfully. RID: {rid}. Full response: {result}") # Log full response
                             return result # <-- Return the whole result dictionary
                         else:
                             logger.error(f"[Yuewen][New API] Image upload succeeded but RID not found in response: {response.text}")
                             return None
                     except json.JSONDecodeError:
                         logger.error(f"[Yuewen][New API] Failed to decode JSON from successful image upload response: {response.text}")
                         return None
                 elif response.status_code == 401 and retry == 0:
                      logger.warning("[Yuewen][New API] Token expired during image upload. Attempting refresh...")
                      if not self.login_handler.refresh_token():
                          logger.error("[Yuewen][New API] Token refresh failed during upload. Aborting.")
                          return None
                      # Token refreshed, update local token variable for next retry's cookie construction
                      token = self.config.get('oasis_token', '')
                      # No need to continue explicitly, loop will retry
                 else:
                     logger.error(f"[Yuewen][New API] Image upload failed with status {response.status_code}: {response.text[:200]}")
                     # Don't retry on other errors for now
                     return None

             except requests.exceptions.RequestException as e:
                  logger.error(f"[Yuewen][New API] Image upload requests exception: {e}")
                  if retry == 0:
                      logger.info("[Yuewen][New API] Retrying upload after requests exception...")
                      # Maybe refresh token just in case?
                      if not self.login_handler.refresh_token():
                           logger.error("[Yuewen][New API] Token refresh failed after requests exception. Aborting upload retry.")
                           return None
                      token = self.config.get('oasis_token', '') 
                  else:
                      return None # Return None after final retry fails
             except Exception as e:
                 logger.error(f"[Yuewen][New API] Unexpected error during image upload: {e}", exc_info=True)
                 return None # General error
        
        logger.error("[Yuewen][New API] Image upload failed after all retries.")
        return None # Failed after retries

if __name__ == '__main__':
    client = YuewenPlugin()
    client.start_chat()