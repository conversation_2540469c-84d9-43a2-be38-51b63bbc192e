import os
import time
import json
import re
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional

from bridge.context import ContextType
from channel.chat_message import ChatMessage
from config import conf
from common.log import logger

class WechatPadProMessage(ChatMessage):
    """
    WechatPadPro 消息处理类 - 微信iPad协议消息处理
    增强版本：包含用户信息获取、@消息解析、XML处理等功能
    """
    def __init__(self, msg: Dict[str, Any], is_group: bool = False, bot_client=None, bot_wxid: str = "", bot_name: str = ""):
        super().__init__(msg)
        self.msg = msg

        # 新增：机器人相关信息，用于@检测和API调用
        self.bot_client = bot_client  # 用于API调用
        self.bot_wxid = bot_wxid      # 机器人wxid，用于@检测
        self.bot_name = bot_name      # 机器人名称，用于@检测

        # 提取消息基本信息 - 支持8059协议的嵌套字段格式
        self.msg_id = msg.get("msg_id", msg.get("msgid", msg.get("MsgId", msg.get("id", ""))))
        if not self.msg_id:
            self.msg_id = f"msg_{int(time.time())}_{hash(str(msg))}"

        self.create_time = msg.get("create_time", msg.get("timestamp", msg.get("CreateTime", msg.get("createTime", int(time.time())))))
        self.is_group = is_group

        # 提取发送者和接收者ID - 8059协议字段格式处理
        self.from_user_id = self._get_string_value(msg.get("from_user_name", msg.get("fromUserName", msg.get("FromUserName", ""))))
        self.to_user_id = self._get_string_value(msg.get("to_user_name", msg.get("toUserName", msg.get("ToUserName", ""))))

        # 提取消息内容 - 8059协议字段格式处理
        self.content = self._get_string_value(msg.get("content", msg.get("Content", "")))

        # 获取消息类型 - 8059协议字段格式处理
        self.msg_type = msg.get("msg_type", msg.get("type", msg.get("Type", msg.get("MsgType", 0))))

        # 初始化其他字段
        self.sender_wxid = ""      # 实际发送者ID
        self.at_list = []          # 被@的用户列表
        self.ctype = ContextType.UNKNOWN
        self.self_display_name = "" # 机器人在群内的昵称

        # 添加actual_user_id和actual_user_nickname字段，与sender_wxid保持一致
        self.actual_user_id = ""    # 实际发送者ID
        self.actual_user_nickname = "" # 实际发送者昵称

        # 引用消息处理标记
        self.is_processed_text_quote: bool = False
        self.is_processed_image_quote: bool = False

        # 语音消息信息
        self.voice_info = {}

        # 首先转换消息类型
        self._convert_msg_type_to_ctype()
        self.type = self.ctype  # Ensure self.type attribute exists and holds the ContextType value

        # 自动解析消息（新增的核心功能）
        self._parse_message_content()
        self._parse_sender_info()
        self._get_bot_display_name()  # 获取机器人群内昵称
        self._parse_at_info()
        self._clean_at_text()

        # 尝试从MsgSource中提取机器人在群内的昵称
        try:
            msg_source = msg.get("MsgSource", "")
            if msg_source and ("<msgsource>" in msg_source.lower() or msg_source.startswith("<")):
                root = ET.fromstring(msg_source if "<msgsource>" in msg_source.lower() else f"<msgsource>{msg_source}</msgsource>")

                # 查找displayname或其他可能包含群昵称的字段
                for tag in ["selfDisplayName", "displayname", "nickname"]:
                    elem = root.find(f".//{tag}")
                    if elem is not None and elem.text:
                        self.self_display_name = elem.text
                        break
        except Exception as e:
            # 解析失败，保持为空字符串
            pass

    def _convert_msg_type_to_ctype(self):
        """
        Converts the raw message type (self.msg_type) to ContextType (self.ctype).
        8059协议消息类型枚举:
        1: 文本消息
        3: 图片消息
        6: 文件消息
        42: 名片
        47: 视频消息
        47: 表情消息
        48: 地理位置消息
        49: 引用消息
        50: 语音/视频
        51: 状态通知
        490: 小程序消息
        491: 合并转发消息
        492: 链接消息
        493: 动画表情
        494: 音乐消息
        495: 红包消息
        496: 转账消息
        497: 拍一拍
        2001: 支付消息
        10000: 系统消息
        10002: 撤回消息
        """
        raw_type = str(self.msg_type) # Ensure it's a string

        if raw_type == "1":
            self.ctype = ContextType.TEXT
        elif raw_type == "3":
            self.ctype = ContextType.IMAGE
        elif raw_type == "6":
            self.ctype = ContextType.FILE if hasattr(ContextType, 'FILE') else ContextType.XML
        elif raw_type == "42":
            self.ctype = ContextType.XML  # 名片
        elif raw_type == "47":
            # 47可能是视频或表情，需要进一步判断
            self.ctype = ContextType.VIDEO  # 默认视频，可能需要内容分析
        elif raw_type == "48":
            self.ctype = ContextType.XML  # 地理位置
        elif raw_type == "49":
            self.ctype = ContextType.XML  # 引用消息
        elif raw_type == "50":
            self.ctype = ContextType.VOICE  # 语音/视频
        elif raw_type == "51":
            # 状态通知
            if hasattr(ContextType, 'STATUS_SYNC'):
                self.ctype = ContextType.STATUS_SYNC
            else:
                self.ctype = ContextType.SYSTEM
        elif raw_type in ["490", "491", "492", "493", "494", "495", "496", "497"]:
            # 小程序、合并转发、链接、动画表情、音乐、红包、转账、拍一拍
            self.ctype = ContextType.XML
        elif raw_type == "2001":
            self.ctype = ContextType.XML  # 支付消息
        elif raw_type == "10000":
            self.ctype = ContextType.SYSTEM  # 系统消息
        elif raw_type == "10002":
            # 撤回消息
            if hasattr(ContextType, 'RECALLED'):
                self.ctype = ContextType.RECALLED
            else:
                self.ctype = ContextType.SYSTEM
        else:
            # self.ctype remains ContextType.UNKNOWN (as initialized)
            pass

    def _get_string_value(self, value):
        """确保值为字符串类型 - 支持8059协议的嵌套字段格式"""
        if isinstance(value, dict):
            # 8059协议格式: {'str': '实际值'}
            if 'str' in value:
                return value['str']
            # 其他协议格式: {'string': '实际值'}
            elif 'string' in value:
                return value['string']
            else:
                return ""
        return str(value) if value is not None else ""

    # 以下是公开接口方法，提供给外部使用
    def get_content(self):
        """获取消息内容"""
        return self.content

    def get_type(self):
        """获取消息类型"""
        return self.ctype

    def get_msg_id(self):
        """获取消息ID"""
        return self.msg_id

    def get_create_time(self):
        """获取消息创建时间"""
        return self.create_time

    def get_from_user_id(self):
        """获取原始发送者ID"""
        return self.from_user_id

    def get_sender_id(self):
        """获取处理后的实际发送者ID（群聊中特别有用）"""
        return self.sender_wxid or self.from_user_id

    def get_to_user_id(self):
        """获取接收者ID"""
        return self.to_user_id

    def get_at_list(self):
        """获取被@的用户列表"""
        return self.at_list

    def is_at(self, wxid):
        """检查指定用户是否被@"""
        return wxid in self.at_list

    def is_group_message(self):
        """判断是否为群消息"""
        return self.is_group

    # ==================== 新增的消息解析方法 ====================

    def _parse_message_content(self):
        """解析消息内容，处理特殊消息类型"""
        # 检查是否为群聊消息
        if self.from_user_id.endswith("@chatroom"):
            self.is_group = True

        # 根据消息类型进行特殊处理
        if self.ctype == ContextType.VOICE and self.content:
            self._parse_voice_message()
        elif self.ctype == ContextType.IMAGE and self.content:
            self._parse_image_message()
        elif self.ctype == ContextType.VIDEO and self.content:
            self._parse_video_message()
        elif self.ctype == ContextType.EMOJI and self.content:
            self._parse_emoji_message()
        elif self.ctype == ContextType.XML and self.content:
            self._parse_xml_message()

    def _parse_sender_info(self):
        """解析发送者信息，特别是群聊中的真实发送者"""
        if not self.is_group and not self.from_user_id.endswith("@chatroom"):
            # 私聊消息，发送者就是from_user_id
            self.sender_wxid = self.from_user_id
            self.actual_user_id = self.from_user_id
            return

        # 群聊消息处理
        self.is_group = True
        sender_extracted = False

        # 检查是否已经有正确的发送者信息
        if self.sender_wxid and not self.sender_wxid.startswith("未知用户_"):
            sender_extracted = True
            logger.debug(f"[WechatPadProMessage] 发送者信息已设置: {self.sender_wxid}")
        else:
            # 方法1: 尝试解析完整的格式 "wxid:\n消息内容"
            if not self.content.startswith("<"):
                split_content = self.content.split(":\n", 1)
                if len(split_content) > 1 and split_content[0] and not split_content[0].startswith("<"):
                    self.sender_wxid = split_content[0]
                    self.content = split_content[1]
                    sender_extracted = True
                    logger.debug(f"[WechatPadProMessage] 群聊发送者提取(方法1): {self.sender_wxid}")

            # 方法2: 尝试从XML中提取发送者信息（增强版本）
            if not sender_extracted and self.content and self.content.startswith("<"):
                try:
                    root = ET.fromstring(self.content)

                    # 检查根元素类型
                    if root.tag == "msg":
                        # 首先检查根元素的fromusername属性（最常见）
                        fromusername = root.get('fromusername')
                        if fromusername:
                            self.sender_wxid = fromusername
                            sender_extracted = True
                            logger.debug(f"[WechatPadProMessage] 从XML根属性提取发送者: {self.sender_wxid}")

                        # 如果没有找到，再查找子元素
                        if not sender_extracted:
                            # 常见的XML消息格式
                            sender_node = root.find(".//username")
                            if sender_node is not None and sender_node.text:
                                self.sender_wxid = sender_node.text
                                sender_extracted = True
                                logger.debug(f"[WechatPadProMessage] 从XML元素提取发送者: {self.sender_wxid}")

                            # 尝试其他可能的标签
                            if not sender_extracted:
                                for tag in ["fromusername", "sender", "from"]:
                                    sender_node = root.find(f".//{tag}")
                                    if sender_node is not None and sender_node.text:
                                        self.sender_wxid = sender_node.text
                                        sender_extracted = True
                                        logger.debug(f"[WechatPadProMessage] 从XML({tag})提取发送者: {self.sender_wxid}")
                                        break
                    else:
                        # 非msg根元素的处理
                        sender_id_xml = root.get('fromusername')
                        if sender_id_xml:
                            self.sender_wxid = sender_id_xml
                            sender_extracted = True
                            logger.debug(f"[WechatPadProMessage] 从非msg XML属性提取发送者: {self.sender_wxid}")
                        else:
                            # 尝试从元素中提取
                            fromusername_elem = root.find('.//fromusername')
                            if fromusername_elem is not None and fromusername_elem.text:
                                self.sender_wxid = fromusername_elem.text
                                sender_extracted = True
                                logger.debug(f"[WechatPadProMessage] 从非msg XML元素提取发送者: {self.sender_wxid}")
                except Exception as e:
                    logger.debug(f"[WechatPadProMessage] XML解析发送者失败: {e}")

            # 方法3: 尝试从其它字段提取
            if not sender_extracted:
                for key in ["SenderUserName", "sender", "senderId", "fromUser"]:
                    if key in self.msg and self.msg[key]:
                        self.sender_wxid = str(self.msg[key])
                        sender_extracted = True
                        logger.debug(f"[WechatPadProMessage] 从字段提取({key}): {self.sender_wxid}")
                        break

        # 如果仍未提取到发送者，使用默认值
        if not sender_extracted:
            self.sender_wxid = f"未知用户_{self.from_user_id}"
            logger.debug(f"[WechatPadProMessage] 未能提取发送者，使用默认值: {self.sender_wxid}")

        # 设置actual_user_id和actual_user_nickname
        self.actual_user_id = self.sender_wxid
        self.actual_user_nickname = self.sender_wxid  # 初始值，后续可通过API更新

    def _get_bot_display_name(self):
        """获取机器人在群内的昵称"""
        if not self.is_group or not self.bot_wxid:
            return

        try:
            # 尝试从缓存文件中获取机器人群内昵称
            import os
            import json

            # 构建缓存文件路径
            tmp_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "tmp")
            chatrooms_file = os.path.join(tmp_dir, 'wechatpadpro_rooms.json')

            if os.path.exists(chatrooms_file):
                try:
                    with open(chatrooms_file, 'r', encoding='utf-8') as f:
                        chatrooms_info = json.load(f)

                    if self.from_user_id in chatrooms_info:
                        room_info = chatrooms_info[self.from_user_id]

                        # 在成员中查找机器人的信息
                        if "members" in room_info and isinstance(room_info["members"], list):
                            for member in room_info["members"]:
                                if member.get("UserName") == self.bot_wxid:
                                    # 优先使用群内显示名称
                                    if member.get("DisplayName"):
                                        self.self_display_name = member.get("DisplayName")
                                        logger.debug(f"[WechatPadProMessage] 从群成员缓存中获取到机器人群内昵称: {self.self_display_name}")
                                        return
                                    # 其次使用昵称
                                    elif member.get("NickName"):
                                        self.self_display_name = member.get("NickName")
                                        logger.debug(f"[WechatPadProMessage] 从群成员缓存中获取到机器人昵称: {self.self_display_name}")
                                        return
                except Exception as e:
                    logger.error(f"[WechatPadProMessage] 读取群成员缓存失败: {e}")

            # 如果缓存中没有找到，使用机器人名称
            if not self.self_display_name:
                self.self_display_name = self.bot_name
                logger.debug(f"[WechatPadProMessage] 使用机器人名称作为群内昵称: {self.self_display_name}")
        except Exception as e:
            logger.error(f"[WechatPadProMessage] 获取机器人群内昵称失败: {e}")
            self.self_display_name = self.bot_name

    def _parse_at_info(self):
        """解析@信息，提取at_list"""
        self.at_list = []

        try:
            # 方法1: 从MsgSource解析
            msg_source = self.msg.get("MsgSource", "")
            if msg_source:
                try:
                    if "<msgsource>" not in msg_source.lower():
                        msg_source = f"<msgsource>{msg_source}</msgsource>"
                    root = ET.fromstring(msg_source)
                    ats_elem = root.find(".//atuserlist")
                    if ats_elem is not None and ats_elem.text:
                        self.at_list = [x for x in ats_elem.text.strip(",").split(",") if x]
                        logger.debug(f"[WechatPadProMessage] 从MsgSource解析到@列表: {self.at_list}")
                        return  # 成功解析，直接返回
                except Exception as e:
                    logger.debug(f"[WechatPadProMessage] 从MsgSource解析@列表失败: {e}")

            # 方法2: 从多个字段解析@列表（增强版本）
            if not self.at_list:
                for key in ["AtUserList", "at_list", "atlist"]:
                    if key in self.msg:
                        at_value = self.msg[key]
                        if isinstance(at_value, list):
                            self.at_list = [str(x) for x in at_value if x]
                        elif isinstance(at_value, str):
                            self.at_list = [x for x in at_value.strip(",").split(",") if x]

                        if self.at_list:
                            logger.debug(f"[WechatPadProMessage] 从字段{key}解析到@列表: {self.at_list}")
                            return  # 成功解析，直接返回

            # 方法3: 从消息内容中检测@机器人
            if self.is_group and "@" in self.content and self.bot_wxid:
                # 检查是否@了机器人
                if self.bot_name and f"@{self.bot_name}" in self.content:
                    self.at_list.append(self.bot_wxid)
                    logger.debug(f"[WechatPadProMessage] 从消息内容检测到@机器人名称: {self.bot_name}")
                elif self.self_display_name and f"@{self.self_display_name}" in self.content:
                    self.at_list.append(self.bot_wxid)
                    logger.debug(f"[WechatPadProMessage] 从消息内容检测到@机器人群内昵称: {self.self_display_name}")

        except Exception as e:
            logger.debug(f"[WechatPadProMessage] 解析@列表失败: {e}")
            self.at_list = []

        # 确保at_list不为空列表
        if not self.at_list or (len(self.at_list) == 1 and self.at_list[0] == ""):
            self.at_list = []

    def _clean_at_text(self):
        """清理消息内容中的@文本"""
        if not self.is_group or not self.content or "@" not in self.content:
            return

        original_content = self.content
        at_patterns = []

        # 添加可能的@格式
        if self.bot_name:
            at_patterns.extend([
                f"@{self.bot_name} ",      # 带空格
                f"@{self.bot_name}\u2005", # 带特殊空格
                f"@{self.bot_name}",       # 不带空格
            ])

        # 检查是否存在自定义的群内昵称
        if self.self_display_name:
            at_patterns.extend([
                f"@{self.self_display_name} ",      # 带空格
                f"@{self.self_display_name}\u2005", # 带特殊空格
                f"@{self.self_display_name}",       # 不带空格
            ])

        # 按照优先级尝试移除@文本
        for pattern in at_patterns:
            if pattern in self.content:
                self.content = self.content.replace(pattern, "", 1).strip()
                logger.debug(f"[WechatPadProMessage] 匹配到@模式: {pattern}")
                logger.debug(f"[WechatPadProMessage] 去除@后的内容: {self.content}")
                break

        # 通用@清理：去掉所有@用户名格式
        if self.is_group:
            self.content = re.sub(r'@[^\u2005]+\u2005', '', self.content)  # 去掉@信息

    def _parse_voice_message(self):
        """解析语音消息"""
        if not self.content:
            return

        original_content = self.content
        is_xml_content = self.content.startswith("<")

        # 首先尝试从XML中提取发送者信息
        if is_xml_content:
            logger.debug(f"[WechatPadProMessage] 语音消息：尝试从XML提取发送者")
            try:
                # 使用正则表达式从XML字符串中提取fromusername属性或元素
                match = re.search(r'fromusername\s*=\s*["\'](.*?)["\']', original_content)
                if match:
                    self.sender_wxid = match.group(1)
                    logger.debug(f"[WechatPadProMessage] 语音消息：从XML属性提取的发送者ID: {self.sender_wxid}")
                else:
                    # 尝试从元素中提取
                    match = re.search(r'<fromusername>(.*?)</fromusername>', original_content)
                    if match:
                        self.sender_wxid = match.group(1)
                        logger.debug(f"[WechatPadProMessage] 语音消息：从XML元素提取的发送者ID: {self.sender_wxid}")
            except Exception as e:
                logger.debug(f"[WechatPadProMessage] 语音消息XML解析发送者失败: {e}")

        # 如果无法从XML提取，再尝试传统的分割方法
        if not self.sender_wxid and (self.is_group or self.from_user_id.endswith("@chatroom")):
            self.is_group = True
            split_content = original_content.split(":\n", 1)
            if len(split_content) > 1:
                self.sender_wxid = split_content[0]
                logger.debug(f"[WechatPadProMessage] 语音消息：使用分割方法提取的发送者ID: {self.sender_wxid}")
            else:
                # 处理没有换行的情况
                split_content = original_content.split(":", 1)
                if len(split_content) > 1:
                    self.sender_wxid = split_content[0]
                    logger.debug(f"[WechatPadProMessage] 语音消息：使用冒号分割提取的发送者ID: {self.sender_wxid}")

        # 对于私聊消息，使用from_user_id作为发送者ID
        if not self.sender_wxid and not self.is_group:
            self.sender_wxid = self.from_user_id
            self.is_group = False

        # 设置actual_user_id和actual_user_nickname
        self.actual_user_id = self.sender_wxid or self.from_user_id
        self.actual_user_nickname = self.sender_wxid or self.from_user_id

        # 解析语音信息 (保留此功能以获取语音URL等信息)
        try:
            root = ET.fromstring(original_content)
            voice_element = root.find('voicemsg')
            if voice_element is not None:
                self.voice_info = {
                    'voiceurl': voice_element.get('voiceurl'),
                    'length': voice_element.get('length')
                }
                logger.debug(f"[WechatPadProMessage] 解析语音XML成功: voiceurl={self.voice_info['voiceurl']}, length={self.voice_info['length']}")
        except Exception as e:
            logger.debug(f"[WechatPadProMessage] 解析语音消息失败: {e}, 内容: {original_content[:100]}")
            self.voice_info = {}

    def _parse_image_message(self):
        """解析图片消息，处理群聊发送者信息和XML内容分离"""
        original_content = self.content

        logger.debug(f"[WechatPadProMessage] 处理图片消息: msg_id={self.msg_id}, is_group={self.is_group}")

        if self.is_group or self.from_user_id.endswith("@chatroom"):
            self.is_group = True

            # 对于群聊图片消息，内容格式通常是 "wxid:\n<?xml...>"
            if isinstance(self.content, str) and ":\n" in self.content and not self.content.startswith("<?xml"):
                split_content = self.content.split(":\n", 1)
                if len(split_content) == 2 and split_content[1].startswith("<?xml"):
                    # 提取发送者ID
                    self.sender_wxid = split_content[0]
                    # 更新内容为纯XML
                    self.content = split_content[1]
                    logger.debug(f"[WechatPadProMessage] 群聊图片消息发送者提取成功: {self.sender_wxid}")
                else:
                    # 如果格式不匹配，使用已设置的发送者信息或默认值
                    if not self.sender_wxid:
                        self.sender_wxid = f"未知用户_{self.from_user_id}"
                    logger.warning(f"[WechatPadProMessage] 群聊图片消息格式不匹配，使用默认发送者: {self.sender_wxid}")
            else:
                # 如果没有发送者前缀，使用已设置的发送者信息或默认值
                if not self.sender_wxid:
                    self.sender_wxid = f"未知用户_{self.from_user_id}"
                logger.warning(f"[WechatPadProMessage] 群聊图片消息无发送者前缀，使用默认发送者: {self.sender_wxid}")
        else:
            # 私聊消息：使用from_user_id作为发送者ID
            self.sender_wxid = self.from_user_id
            self.is_group = False
            logger.debug(f"[WechatPadProMessage] 私聊图片消息发送者: {self.sender_wxid}")

        # 确保actual_user_id和actual_user_nickname已设置
        self.actual_user_id = self.sender_wxid
        self.actual_user_nickname = self.sender_wxid

        # 解析图片XML信息
        try:
            xml_content_to_parse = ""
            # 现在self.content应该已经是纯XML内容了
            if isinstance(self.content, str) and (self.content.startswith('<?xml') or self.content.startswith("<msg>")):
                xml_content_to_parse = self.content
                logger.debug(f"[WechatPadProMessage] 图片消息XML内容长度: {len(xml_content_to_parse)}")
            elif isinstance(self.content, bytes):
                try:
                    xml_content_to_parse = self.content.decode('utf-8')
                    if not (xml_content_to_parse.startswith('<?xml') or xml_content_to_parse.startswith("<msg>")):
                        xml_content_to_parse = ""  # Not valid XML
                except UnicodeDecodeError:
                    logger.warning(f"[WechatPadProMessage] 图片内容是bytes但无法解码为UTF-8")
                    xml_content_to_parse = ""

            if xml_content_to_parse:
                try:
                    root = ET.fromstring(xml_content_to_parse)
                    img_element = root.find('img')
                    if img_element is not None:
                        # 存储图片信息到消息对象
                        self.img_aeskey = img_element.get('aeskey')
                        self.img_cdnthumbaeskey = img_element.get('cdnthumbaeskey')
                        self.img_md5 = img_element.get('md5')
                        self.img_length = img_element.get('length', '0')
                        self.img_cdnmidimgurl = img_element.get('cdnmidimgurl', '')

                        # 创建图片信息字典
                        self.image_info = {
                            'aeskey': self.img_aeskey,
                            'cdnmidimgurl': self.img_cdnmidimgurl,
                            'length': self.img_length,
                            'md5': self.img_md5
                        }
                        logger.debug(f"[WechatPadProMessage] 解析图片XML成功: aeskey={self.img_aeskey}, length={self.img_length}")

                        if not self.img_aeskey:
                            logger.warning(f"[WechatPadProMessage] 图片XML中缺少aeskey，无法进行缓存")
                    else:
                        logger.warning(f"[WechatPadProMessage] XML中未找到<img>标签")
                        # 初始化默认属性
                        self.img_aeskey = None
                        self.img_length = '0'
                        self.image_info = {'aeskey': '', 'cdnmidimgurl': '', 'length': '0', 'md5': ''}
                except ET.ParseError as xml_err:
                    logger.warning(f"[WechatPadProMessage] 解析图片XML失败: {xml_err}")
                    self.img_aeskey = None
                    self.img_length = '0'
                    self.image_info = {'aeskey': '', 'cdnmidimgurl': '', 'length': '0', 'md5': ''}
            else:
                logger.warning(f"[WechatPadProMessage] 图片内容不是XML格式")
                self.img_aeskey = None
                self.img_length = '0'
                self.image_info = {'aeskey': '', 'cdnmidimgurl': '', 'length': '0', 'md5': ''}

        except Exception as e:
            logger.error(f"[WechatPadProMessage] 图片消息处理出错: {e}")
            # 确保默认属性存在
            if not hasattr(self, 'img_aeskey'):
                self.img_aeskey = None
            if not hasattr(self, 'image_info'):
                self.image_info = {'aeskey': '', 'cdnmidimgurl': '', 'length': '0', 'md5': ''}

    def _parse_video_message(self):
        """解析视频消息"""
        if not self.content:
            return

        original_content = self.content
        is_xml_content = self.content.startswith("<")

        # 首先尝试从XML中提取发送者信息
        if is_xml_content:
            logger.debug(f"[WechatPadProMessage] 视频消息：尝试从XML提取发送者")
            try:
                # 使用正则表达式从XML字符串中提取fromusername属性或元素
                match = re.search(r'fromusername\s*=\s*["\'](.*?)["\']', original_content)
                if match:
                    self.sender_wxid = match.group(1)
                    logger.debug(f"[WechatPadProMessage] 视频消息：从XML属性提取的发送者ID: {self.sender_wxid}")
                else:
                    # 尝试从元素中提取
                    match = re.search(r'<fromusername>(.*?)</fromusername>', original_content)
                    if match:
                        self.sender_wxid = match.group(1)
                        logger.debug(f"[WechatPadProMessage] 视频消息：从XML元素提取的发送者ID: {self.sender_wxid}")
            except Exception as e:
                logger.debug(f"[WechatPadProMessage] 视频消息XML解析发送者失败: {e}")

        # 如果无法从XML提取，再尝试传统的分割方法
        if not self.sender_wxid and (self.is_group or self.from_user_id.endswith("@chatroom")):
            self.is_group = True
            split_content = original_content.split(":\n", 1)
            if len(split_content) > 1:
                self.sender_wxid = split_content[0]
                logger.debug(f"[WechatPadProMessage] 视频消息：使用分割方法提取的发送者ID: {self.sender_wxid}")
            else:
                # 处理没有换行的情况
                split_content = original_content.split(":", 1)
                if len(split_content) > 1:
                    self.sender_wxid = split_content[0]
                    logger.debug(f"[WechatPadProMessage] 视频消息：使用冒号分割提取的发送者ID: {self.sender_wxid}")

        # 对于私聊消息，使用from_user_id作为发送者ID
        if not self.sender_wxid and not self.is_group:
            self.sender_wxid = self.from_user_id
            self.is_group = False

        # 设置actual_user_id和actual_user_nickname
        self.actual_user_id = self.sender_wxid or self.from_user_id
        self.actual_user_nickname = self.sender_wxid or self.from_user_id

        # 解析视频信息
        try:
            root = ET.fromstring(original_content)
            video_element = root.find('videomsg')
            if video_element is not None:
                self.video_info = {
                    'aeskey': video_element.get('aeskey'),
                    'cdnvideourl': video_element.get('cdnvideourl'),
                    'length': video_element.get('length')
                }
                logger.debug(f"[WechatPadProMessage] 解析视频XML成功: aeskey={self.video_info['aeskey']}, length={self.video_info['length']}")
        except Exception as e:
            logger.debug(f"[WechatPadProMessage] 解析视频消息失败: {e}")
            self.video_info = {}

    def _parse_emoji_message(self):
        """解析表情消息"""
        if not self.content:
            return

        original_content = self.content
        is_xml_content = self.content.startswith("<")

        # 首先尝试从XML中提取发送者信息
        if is_xml_content:
            logger.debug(f"[WechatPadProMessage] 表情消息：尝试从XML提取发送者")
            try:
                # 使用正则表达式从XML中提取fromusername属性
                match = re.search(r'fromusername\s*=\s*["\'](.*?)["\']', original_content)
                if match:
                    self.sender_wxid = match.group(1)
                    logger.debug(f"[WechatPadProMessage] 表情消息：从XML提取的发送者ID: {self.sender_wxid}")
            except Exception as e:
                logger.debug(f"[WechatPadProMessage] 表情消息XML解析发送者失败: {e}")

        # 如果无法从XML提取，再尝试传统的分割方法
        if not self.sender_wxid and (self.is_group or self.from_user_id.endswith("@chatroom")):
            self.is_group = True
            split_content = original_content.split(":\n", 1)
            if len(split_content) > 1:
                self.sender_wxid = split_content[0]
                logger.debug(f"[WechatPadProMessage] 表情消息：使用分割方法提取的发送者ID: {self.sender_wxid}")
            else:
                # 处理没有换行的情况
                split_content = original_content.split(":", 1)
                if len(split_content) > 1:
                    self.sender_wxid = split_content[0]
                    logger.debug(f"[WechatPadProMessage] 表情消息：使用冒号分割提取的发送者ID: {self.sender_wxid}")

        # 对于私聊消息，使用from_user_id作为发送者ID
        if not self.sender_wxid and not self.is_group:
            self.sender_wxid = self.from_user_id
            self.is_group = False

        # 设置actual_user_id和actual_user_nickname
        self.actual_user_id = self.sender_wxid or self.from_user_id
        self.actual_user_nickname = self.sender_wxid or self.from_user_id

        # 解析表情信息
        try:
            root = ET.fromstring(original_content)
            emoji_element = root.find('emoji')
            if emoji_element is not None:
                self.emoji_info = {
                    'fromusername': emoji_element.get('fromusername'),
                    'tousername': emoji_element.get('tousername'),
                    'type': emoji_element.get('type'),
                    'idbuffer': emoji_element.get('idbuffer')
                }
                logger.debug(f"[WechatPadProMessage] 解析表情XML成功: type={self.emoji_info['type']}")
        except Exception as e:
            logger.debug(f"[WechatPadProMessage] 解析表情消息失败: {e}")
            self.emoji_info = {}

    def _parse_xml_message(self):
        """解析XML消息，包括引用消息等"""
        if not self.content or not self.content.startswith("<"):
            return

        try:
            root = ET.fromstring(self.content)
            appmsg = root.find("appmsg")

            # 处理引用消息 (Type 57)
            if appmsg is not None and appmsg.findtext("type") == "57":
                refermsg = appmsg.find("refermsg")
                if refermsg is not None:
                    refer_type = refermsg.findtext("type")
                    title = appmsg.findtext("title")  # User's question part / command
                    displayname = refermsg.findtext("displayname")  # Quoter's display name

                    # 设置为引用消息类型
                    self.ctype = ContextType.TEXT  # 引用消息通常作为文本处理

                    # 更新消息内容为用户的问题部分
                    if title:
                        self.content = title
                        logger.debug(f"[WechatPadProMessage] 引用消息处理：提取用户问题: {title}")

                    # 标记为已处理的引用消息
                    if refer_type == "1":  # 文本引用
                        self.is_processed_text_quote = True
                    elif refer_type == "3":  # 图片引用
                        self.is_processed_image_quote = True

            # 群消息发送者处理
            if self.is_group and not self.actual_user_id:
                # 'fromusername' is usually on the root <msg> for group messages if it's the raw XML
                sender_id_xml = root.get('fromusername')
                if sender_id_xml:
                    self.sender_wxid = sender_id_xml
                    self.actual_user_id = sender_id_xml
                    logger.debug(f"[WechatPadProMessage] 从群XML消息提取发送者: {sender_id_xml}")

        except Exception as e:
            logger.debug(f"[WechatPadProMessage] XML消息解析失败: {e}")

    # ==================== 用户信息获取方法 ====================

    async def get_user_nickname(self, user_id: str, group_id: str = None) -> str:
        """获取用户昵称

        Args:
            user_id: 用户ID
            group_id: 群ID（如果是群聊）

        Returns:
            用户昵称
        """
        if not self.bot_client or not user_id:
            return user_id

        try:
            if group_id and group_id.endswith("@chatroom"):
                # 群聊中获取成员昵称
                return await self._get_chatroom_member_nickname(group_id, user_id)
            else:
                # 私聊中获取用户昵称
                return await self._get_user_nickname(user_id)
        except Exception as e:
            logger.error(f"[WechatPadProMessage] 获取用户昵称失败: {e}")
            return user_id

    async def _get_chatroom_member_nickname(self, group_id: str, member_wxid: str) -> str:
        """获取群成员的昵称 - 委托给Channel处理"""
        if not group_id or not member_wxid:
            return member_wxid

        # 注意：这里需要Channel实例来调用其方法
        # 在实际使用中，应该通过Channel的实例来调用
        # 这里只是一个占位实现，实际应该由Channel负责昵称获取
        logger.debug(f"[WechatPadProMessage] 需要通过Channel获取群成员昵称: {group_id}/{member_wxid}")
        return member_wxid

    async def _get_user_nickname(self, user_id: str) -> str:
        """获取用户昵称"""
        if not user_id or not self.bot_client:
            return user_id

        try:
            # 调用bot_client的API获取用户信息
            if hasattr(self.bot_client, 'get_contact_details_list'):
                response = await self.bot_client.get_contact_details_list(user_names=[user_id])
                if response and isinstance(response, dict):
                    contacts = response.get("ContactDetailsList", [])
                    for contact in contacts:
                        if contact.get("UserName") == user_id:
                            nickname = contact.get("NickName", "")
                            if nickname:
                                logger.debug(f"[WechatPadProMessage] 获取到用户昵称: {user_id} -> {nickname}")
                                return nickname

            logger.debug(f"[WechatPadProMessage] 未能获取到用户昵称，使用ID: {user_id}")
            return user_id
        except Exception as e:
            logger.error(f"[WechatPadProMessage] 获取用户昵称出错: {e}")
            return user_id

    # ==================== 向后兼容性方法 ====================

    def update_nickname_if_needed(self):
        """更新昵称信息（如果需要）- 向后兼容方法"""
        if self.actual_user_nickname == self.actual_user_id:
            # 昵称还是ID，需要更新
            if self.is_group:
                # 群聊消息，需要异步获取
                logger.debug(f"[WechatPadProMessage] 群聊消息需要更新昵称: {self.actual_user_id}")
            else:
                # 私聊消息，需要异步获取
                logger.debug(f"[WechatPadProMessage] 私聊消息需要更新昵称: {self.actual_user_id}")

    def is_at_bot(self) -> bool:
        """检查是否@了机器人"""
        if not self.bot_wxid:
            return False
        return self.bot_wxid in self.at_list

    def get_clean_content(self) -> str:
        """获取清理后的消息内容（已去除@文本）"""
        return self.content

    def get_sender_info(self) -> dict:
        """获取发送者信息"""
        return {
            "sender_wxid": self.sender_wxid,
            "actual_user_id": self.actual_user_id,
            "actual_user_nickname": self.actual_user_nickname,
            "from_user_id": self.from_user_id,
            "is_group": self.is_group
        }
