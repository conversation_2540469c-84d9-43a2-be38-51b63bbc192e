# common/async_utils.py
# This file will contain common asynchronous utility functions.

import asyncio
import concurrent.futures
from common.log import logger # Assuming logger is from common.log

def run_async_safely(coro, timeout=60, operation_name="未知操作"):
    """安全地运行异步协程"""
    try:
        # 尝试获取当前事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果循环正在运行，使用asyncio.run_coroutine_threadsafe
            # import concurrent.futures # Already imported at the top
            logger.debug(f"[AsyncUtils] 开始执行异步操作: {operation_name}, 超时时间: {timeout}秒 (via run_coroutine_threadsafe)")
            future = asyncio.run_coroutine_threadsafe(coro, loop)
            try:
                result = future.result(timeout=timeout)
                logger.debug(f"[AsyncUtils] 异步操作完成: {operation_name}")
                return result
            except concurrent.futures.TimeoutError:
                logger.error(f"[AsyncUtils] 异步操作超时: {operation_name}, 超时时间: {timeout}秒")
                raise TimeoutError(f"操作超时: {operation_name}")
        else:
            # 如果循环没有运行，直接运行
            logger.debug(f"[AsyncUtils] 直接运行异步操作: {operation_name} (via loop.run_until_complete)")
            return loop.run_until_complete(coro)
    except RuntimeError:
        # 如果没有事件循环或有其他问题，使用asyncio.run
        logger.debug(f"[AsyncUtils] 使用asyncio.run执行: {operation_name}")
        return asyncio.run(coro)