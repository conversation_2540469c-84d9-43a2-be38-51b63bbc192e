# `wechatpadpro` 通道重构执行计划

## 总体原则

*   **小步快跑，逐步迭代：** 每个步骤尽量小，完成后进行测试（理论或实际），确保稳定性。
*   **优先处理依赖性低、收益高的部分。**
*   **保持向后兼容性：** 在重构过程中，尽量保证对外接口（如 `Channel` 层提供给 `Bridge` 层的接口）的稳定性，或者有计划地进行接口变更。

---

## 阶段一：通用工具函数模块化 (低风险，高复用性)

### 步骤 1：创建/选定公共工具模块
*   **动作：**
    *   在 `common/` 目录下创建 `async_utils.py` (如果不存在)。
    *   在 `common/` 目录下创建 `media_utils.py` (如果不存在，或者并入 `utils.py`)。
    *   在 `common/` 目录下创建 `cache_utils.py` (如果不存在)。
*   **产出：** 准备好的公共模块文件结构。

### 步骤 2：迁移 `_run_async_safely`
*   **动作：** 将 `channel/wechatpadpro/wechatpadpro_channel.py` 中的 `_run_async_safely` 函数完整迁移到 `common/async_utils.py`。
*   **修改：** 在 `wechatpadpro_channel.py` 中导入并使用 `from common.async_utils import run_async_safely`。
*   **测试：** 理论验证调用点是否正确替换。

### 步骤 3：迁移 `_find_ffmpeg_path`
*   **动作：** 将 `channel/wechatpadpro/wechatpadpro_channel.py` 中的 `_find_ffmpeg_path` 函数迁移到 `common/media_utils.py`。
*   **修改：** 在 `wechatpadpro_channel.py` 中导入并使用。
*   **测试：** 理论验证调用点。

### 步骤 4：重构图片缓存管理为通用 `FileSystemCacheManager`
*   **动作：**
    *   在 `common/cache_utils.py` 中定义 `FileSystemCacheManager` 类。
        *   `__init__(self, cache_dir: str, max_age_seconds: int, cleanup_interval_hours: int, file_extensions: List[str])`
        *   `get_cached_file_path(self, item_id: str) -> Optional[str]`
        *   `save_to_cache(self, item_id: str, data: bytes) -> str`
        *   `_cleanup_cached_files(self)`
        *   `start_cleanup_task(self)`
    *   在 `WechatPadProChannel.__init__` 中实例化 `self.image_cache_manager = FileSystemCacheManager(...)` 并启动清理任务。
    *   移除 `WechatPadProChannel` 中的原图片缓存管理方法。
*   **修改：** 图片下载和使用缓存的地方，改为调用 `self.image_cache_manager` 的方法。
*   **测试：** 理论验证图片缓存的读写和清理。

---

## 阶段二：增强 `WechatPadProMessage` 的解析能力 (核心职责下沉)

### 步骤 5：在 `WechatPadProMessage` 中实现详细的XML内容解析
*   **动作：**
    *   在 `channel/wechatpadpro/wechatpadpro_message.py` 中，修改 `_parse_message_content()` 或其调用的 `_parse_xml_message()`。
    *   添加逻辑以解析不同类型的XML（引用、小程序、链接、名片等），并将解析结果存入 `WechatPadProMessage` 的新属性中 (如 `self.quote_type`, `self.miniapp_title` 等)。
*   **修改：**
    *   `wechatpadpro_channel.py` 中的 `_process_xml_message` 将被大幅简化。
    *   更新 `WechatPadProChannel._compose_context()` 以使用这些新属性。
*   **测试：** 构造不同XML消息样本，验证 `WechatPadProMessage` 解析。

### 步骤 6：在 `WechatPadProMessage` 中实现媒体消息元数据解析
*   **动作：**
    *   修改 `channel/wechatpadpro/wechatpadpro_message.py` 中的 `_parse_image_message()`, `_parse_voice_message()`, `_parse_video_message()`。
    *   使其解析XML中携带的媒体元数据（CDN信息、尺寸、时长等）并存入 `WechatPadProMessage` 的属性。
*   **修改：** `wechatpadpro_channel.py` 中对应的 `_process_xxx_message` 方法中不再需要重复解析。
*   **测试：** 构造包含元数据的媒体消息样本，验证解析。

---

## 阶段三：引入 `WechatPadProMediaHandler` (分离媒体处理)

### 步骤 7：定义并实现 `WechatPadProMediaHandler` 类
*   **动作：** 在 `channel/wechatpadpro/wechatpadpro_media_handler.py` 中创建此类。
    *   实现 `download_image(self, ...)` (使用 `FileSystemCacheManager`, 异步化)。
    *   实现 `prepare_video_for_sending(self, ...)` (封装视频下载、`ffmpeg` 异步调用等)。
    *   实现 `prepare_voice_segments_for_sending(self, ...)` (封装 `ffmpeg` 异步调用、`split_audio`、SILK转换异步调用等)。
    *   确保临时文件管理。
*   **修改：**
    *   `WechatPadProChannel.__init__` 中实例化 `self.media_handler`。
    *   移除 `WechatPadProChannel` 中被替代的媒体处理方法。
    *   `WechatPadProChannel.send()` 调用 `self.media_handler` 处理媒体。
*   **测试：** 对 `MediaHandler` 的方法进行单元/集成测试。

---

## 阶段四：引入 `WechatPadProApiWrapper` (可选，但推荐)

### 步骤 8：定义并实现 `WechatPadProApiWrapper` 类
*   **动作：** 在 `channel/wechatpadpro/wechatpadpro_api_wrapper.py` 中创建此类。
    *   `__init__(self, bot_client)`。
    *   为 `WechatAPIClient8059` 的业务接口创建对应的业务方法 (如 `send_text_message`, `get_user_info`)。
    *   封装参数构造、响应解析和更具体的业务异常抛出。
*   **修改：**
    *   `WechatPadProChannel.__init__` 中实例化 `self.api_wrapper`。
    *   `WechatPadProChannel` 中所有直接调用 `self.bot` 或 `_call_api` 的地方，改为调用 `self.api_wrapper` 的方法。
    *   `WechatPadProMediaHandler` 也通过注入的 `ApiWrapper` 进行API调用。
*   **测试：** 对 `ApiWrapper` 的方法进行单元测试 (mock `bot_client`)。

---

## 阶段五：最终调整与审查

### 步骤 9：代码审查与接口调整
*   **动作：** 全面审查修改后的所有相关模块。
    *   确保职责划分清晰，接口设计合理。
    *   检查错误处理流程。
    *   更新日志记录和代码注释。
*   **产出：** 经过审查和优化的代码。

### 步骤 10：文档更新
*   **动作：** 更新任何描述该通道架构或实现细节的项目文档。
*   **产出：** 最新的项目文档。