# tyhh
dow插件，通义绘画

# 通义万相绘画插件

一款基于阿里云通义万相的绘画插件，支持多种创作模式和图片处理功能。

## 功能特性

- **文生图**  
  `通义 [提示词] [-比例]`  
  支持5种比例：`-1:1` `-16:9` `-9:16` `-4:3` `-3:4`
![23258](https://github.com/user-attachments/assets/4e2a125e-212c-4df5-8b4c-b45c50314e3c)


- **手绘创作**  
  `通义手绘 [提示词] [-比例] [-风格]`  
  支持风格：`-扁平` `-油画` `-二次元` `-水彩` `-3D`
![23260](https://github.com/user-attachments/assets/ca79afba-e4a4-4954-aaa5-897bf9c7d846)



- **积分查询**  
  `通义积分` 查看当前账号积分

- **原图查看**  
  `t [图片ID] [序号]` 查看高清原图

## 安装部署

1. 将插件文件放入插件目录：


2. 安装依赖库：
   ```bash
   pip install requests Pillow
   ```

3. 配置文件自动生成：
   ```bash
   # 首次运行后生成config.json
   ```

## 配置说明

`config.json` 结构：
```json
{
  "cookie": "登录凭证",
  "last_sign_in_date": "最后签到日期",
  "resolutions": ["支持的分辨率列表"]
}
```

获取cookie流程：
1. 首次使用触发插件发送`通义`进行登录
2. 输入手机号获取验证码
3. 输入6位短信验证码
4. 自动完成cookie配置

## 使用示例

1. 生成赛博朋克风格图片：
   ```
   通义 未来都市夜景，赛博朋克风格 -16:9
   ```

2. 手绘水彩风格创作：
   ```
   通义手绘 山水田园风光 -水彩
   ```

3. 图片放大操作：
   ```
   t 1718102345 2
   ```

## 注意事项

1. 需保持网络畅通（支持代理配置）
2. 首次使用需完成手机验证登录
3. 每日自动签到获取积分
4. 敏感内容受平台策略限制

> 本插件请合理使用使用，创作结果受平台算法限制
