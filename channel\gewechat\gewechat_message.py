import base64
import uuid
import re
from bridge.context import ContextType
from channel.chat_message import ChatMessage
from common.log import logger
from common.tmp_dir import TmpDir
from config import conf
from lib.gewechat import GewechatClient
import requests
import xml.etree.ElementTree as ET
import os


# 私聊信息示例
"""
{
    "TypeName": "AddMsg",
    "Appid": "wx_xxx",
    "Data": {
        "MsgId": 177581074,
        "FromUserName": {
            "string": "wxid_fromuser"
        },
        "ToUserName": {
            "string": "wxid_touser"
        },
        "MsgType": 49,
        "Content": {
            "string": ""
        },
        "Status": 3,
        "ImgStatus": 1,
        "ImgBuf": {
            "iLen": 0
        },
        "CreateTime": 1733410112,
        "MsgSource": "<msgsource>xx</msgsource>\n",
        "PushContent": "xxx",
        "NewMsgId": 5894648508580188926,
        "MsgSeq": 773900156
    },
    "Wxid": "wxid_gewechat_bot"  // 使用gewechat登录的机器人wxid
}
"""

# 群聊信息示例
"""
{
    "TypeName": "AddMsg",
    "Appid": "wx_xxx",
    "Data": {
        "MsgId": 585326344,
        "FromUserName": {
            "string": "xxx@chatroom"
        },
        "ToUserName": {
            "string": "wxid_gewechat_bot" // 接收到此消息的wxid, 即使用gewechat登录的机器人wxid
        },
        "MsgType": 1,
        "Content": {
            "string": "wxid_xxx:\n@name msg_content" // 发送消息人的wxid和消息内容(包含@name)
        },
        "Status": 3,
        "ImgStatus": 1,
        "ImgBuf": {
            "iLen": 0
        },
        "CreateTime": 1733447040,
        "MsgSource": "<msgsource>\n\t<atuserlist><![CDATA[,wxid_wvp31dkffyml19]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>V1_cqxXBat9|v1_cqxXBat9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n",
        "PushContent": "xxx在群聊中@了你",
        "NewMsgId": 8449132831264840264,
        "MsgSeq": 773900177
    },
    "Wxid": "wxid_gewechat_bot"  // 使用gewechat登录的机器人wxid
}
"""

# 群邀请消息示例
"""
{
    "TypeName": "AddMsg",
    "Appid": "wx_xxx",
    "Data": {
        "MsgId": 488566999,
        "FromUserName": {
            "string": "xxx@chatroom"
        },
        "ToUserName": {
            "string": "wxid_gewechat_bot"
        },
        "MsgType": 10002,
        "Content": {
            "string": "53760920521@chatroom:\n<sysmsg type=\"sysmsgtemplate\">\n\t<sysmsgtemplate>\n\t\t<content_template type=\"tmpl_type_profile\">\n\t\t\t<plain><![CDATA[]]></plain>\n\t\t\t<template><![CDATA[\"$username$\"邀请\"$names$\"加入了群聊]]></template>\n\t\t\t<link_list>\n\t\t\t\t<link name=\"username\" type=\"link_profile\">\n\t\t\t\t\t<memberlist>\n\t\t\t\t\t\t<member>\n\t\t\t\t\t\t\t<username><![CDATA[wxid_eaclcf34ny6221]]></username>\n\t\t\t\t\t\t\t<nickname><![CDATA[刘贺]]></nickname>\n\t\t\t\t\t\t</member>\n\t\t\t\t\t</memberlist>\n\t\t\t\t</link>\n\t\t\t\t<link name=\"names\" type=\"link_profile\">\n\t\t\t\t\t<memberlist>\n\t\t\t\t\t\t<member>\n\t\t\t\t\t\t\t<username><![CDATA[wxid_mmwc3zzkfcl922]]></username>\n\t\t\t\t\t\t\t<nickname><![CDATA[郑德娟]]></nickname>\n\t\t\t\t\t\t</member>\n\t\t\t\t\t</memberlist>\n\t\t\t\t\t<separator><![CDATA[、]]></separator>\n\t\t\t\t</link>\n\t\t\t</link_list>\n\t\t</content_template>\n\t</sysmsgtemplate>\n</sysmsg>\n"
        },
        "Status": 4,
        "ImgStatus": 1,
        "ImgBuf": {
            "iLen": 0
        },
        "CreateTime": 1736820013,
        "MsgSource": "<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n",
        "NewMsgId": 5407479395895269893,
        "MsgSeq": 821038175
    },
    "Wxid": "wxid_gewechat_bot"
}
"""

"""
{
    "TypeName": "ModContacts",
    "Appid": "wx_xxx",
    "Data": {
        "UserName": {
            "string": "xxx@chatroom"
        },
        "NickName": {
            "string": "测试2"
        },
        "PyInitial": {
            "string": "CS2"
        },
        "QuanPin": {
            "string": "ceshi2"
        },
        "Sex": 0,
        "ImgBuf": {
            "iLen": 0
        },
        "BitMask": **********,
        "BitVal": 2,
        "ImgFlag": 1,
        "Remark": {},
        "RemarkPyinitial": {},
        "RemarkQuanPin": {},
        "ContactType": 0,
        "RoomInfoCount": 0,
        "DomainList": [
            {}
        ],
        "ChatRoomNotify": 1,
        "AddContactScene": 0,
        "PersonalCard": 0,
        "HasWeiXinHdHeadImg": 0,
        "VerifyFlag": 0,
        "Level": 0,
        "Source": 0,
        "ChatRoomOwner": "wxid_xxx",
        "WeiboFlag": 0,
        "AlbumStyle": 0,
        "AlbumFlag": 0,
        "SnsUserInfo": {
            "SnsFlag": 0,
            "SnsBgobjectId": 0,
            "SnsFlagEx": 0
        },
        "CustomizedInfo": {
            "BrandFlag": 0
        },
        "AdditionalContactList": {
            "LinkedinContactItem": {}
        },
        "ChatroomMaxCount": 10008,
        "DeleteFlag": 0,
        "Description": "\b\u0004\u0012\u001c\n\u0013wxid_xxx0\u0001@\u0000\u0001\u0000\u0012\u001c\n\u0013wxid_xxx0\u0001@\u0000\u0001\u0000\u0012\u001c\n\u0013wxid_xxx0\u0001@\u0000\u0001\u0000\u0018\u0001\"\u0000(\u00008\u0000",
        "ChatroomStatus": 5,
        "Extflag": 0,
        "ChatRoomBusinessType": 0
    },
    "Wxid": "wxid_xxx"
}
"""

# 群聊中移除用户示例
"""
{
    "UserName": {
        "string": "xxx@chatroom"
    },
    "NickName": {
        "string": "AITestGroup"
    },
    "PyInitial": {
        "string": "AITESTGROUP"
    },
    "QuanPin": {
        "string": "AITestGroup"
    },
    "Sex": 0,
    "ImgBuf": {
        "iLen": 0
    },
    "BitMask": **********,
    "BitVal": 2,
    "ImgFlag": 1,
    "Remark": {},
    "RemarkPyinitial": {},
    "RemarkQuanPin": {},
    "ContactType": 0,
    "RoomInfoCount": 0,
    "DomainList": [
        {}
    ],
    "ChatRoomNotify": 1,
    "AddContactScene": 0,
    "PersonalCard": 0,
    "HasWeiXinHdHeadImg": 0,
    "VerifyFlag": 0,
    "Level": 0,
    "Source": 0,
    "ChatRoomOwner": "wxid_xxx",
    "WeiboFlag": 0,
    "AlbumStyle": 0,
    "AlbumFlag": 0,
    "SnsUserInfo": {
        "SnsFlag": 0,
        "SnsBgobjectId": 0,
        "SnsFlagEx": 0
    },
    "CustomizedInfo": {
        "BrandFlag": 0
    },
    "AdditionalContactList": {
        "LinkedinContactItem": {}
    },
    "ChatroomMaxCount": 10037,
    "DeleteFlag": 0,
    "Description": "\b\u0002\u0012\u001c\n\u0013wxid_eacxxxx\u0001@\u0000�\u0001\u0000\u0012\u001c\n\u0013wxid_xxx\u0001@\u0000�\u0001\u0000\u0018\u0001\"\u0000(\u00008\u0000",
    "ChatroomStatus": 4,
    "Extflag": 0,
    "ChatRoomBusinessType": 0
}
"""

# 群聊中移除用户示例
"""
{
    "TypeName": "ModContacts",
    "Appid": "wx_xxx",
    "Data": {
        "UserName": {
            "string": "xxx@chatroom"
        },
        "NickName": {
            "string": "测试2"
        },
        "PyInitial": {
            "string": "CS2"
        },
        "QuanPin": {
            "string": "ceshi2"
        },
        "Sex": 0,
        "ImgBuf": {
            "iLen": 0
        },
        "BitMask": **********,
        "BitVal": 2,
        "ImgFlag": 2,
        "Remark": {},
        "RemarkPyinitial": {},
        "RemarkQuanPin": {},
        "ContactType": 0,
        "RoomInfoCount": 0,
        "DomainList": [
            {}
        ],
        "ChatRoomNotify": 1,
        "AddContactScene": 0,
        "PersonalCard": 0,
        "HasWeiXinHdHeadImg": 0,
        "VerifyFlag": 0,
        "Level": 0,
        "Source": 0,
        "ChatRoomOwner": "wxid_xxx",
        "WeiboFlag": 0,
        "AlbumStyle": 0,
        "AlbumFlag": 0,
        "SnsUserInfo": {
            "SnsFlag": 0,
            "SnsBgobjectId": 0,
            "SnsFlagEx": 0
        },
        "SmallHeadImgUrl": "https://wx.qlogo.cn/mmcrhead/xxx/0",
        "CustomizedInfo": {
            "BrandFlag": 0
        },
        "AdditionalContactList": {
            "LinkedinContactItem": {}
        },
        "ChatroomMaxCount": 10007,
        "DeleteFlag": 0,
        "Description": "\b\u0003\u0012\u001c\n\u0013wxid_xxx0\u0001@\u0000\u0001\u0000\u0012\u001c\n\u0013wxid_xxx0\u0001@\u0000\u0001\u0000\u0012\u001c\n\u0013wxid_xxx0\u0001@\u0000\u0001\u0000\u0018\u0001\"\u0000(\u00008\u0000",
        "ChatroomStatus": 5,
        "Extflag": 0,
        "ChatRoomBusinessType": 0
    },
    "Wxid": "wxid_xxx"
}
"""

class GeWeChatMessage(ChatMessage):
    def __init__(self, msg, client: GewechatClient):
        """初始化消息对象
        Args:
            msg: 原始消息数据
            client: GewechatClient实例
        """
        super().__init__(msg)
        self.msg = msg
        self.client = client
        
        # 检查必要的字段是否存在
        if not msg.get('Data'):
            logger.warning(f"[gewechat] Missing 'Data' in message")
            return
            
        self.create_time = msg.get('Data', {}).get('CreateTime', 0)
        self.msg_id = msg.get('Data', {}).get('NewMsgId', msg.get('Data', {}).get('MsgId', ''))
        self.from_user_id = msg.get('Data', {}).get('FromUserName', {}).get('string', '')
        self.to_user_id = msg.get('Data', {}).get('ToUserName', {}).get('string', '')
        self.content = ''
        self.ctype = ContextType.TEXT
        self.is_group = "@chatroom" in self.from_user_id
        self.actual_user_id = ''
        self.actual_user_nickname = ''
        self.other_user_id = self.from_user_id
        self.other_user_nickname = ''
        self.is_at = False
        self.at_list = []
        self.my_msg = False
        self.self_display_name = ''
        self.parsed_content = None
        self.app_id = conf().get("gewechat_app_id")
        
        # 解析消息
        self._parse_message()

    def _parse_message(self):
        """解析消息内容"""
        # 检查是否是公众号等非用户账号的消息
        if self._is_non_user_message(self.msg.get('Data', {}).get('MsgSource', ''), self.from_user_id):
            self.ctype = ContextType.NON_USER_MSG
            self.content = self.msg.get('Data', {}).get('Content', {}).get('string', '')
            logger.debug(f"[gewechat] detected non-user message from {self.from_user_id}: {self.content}")
            return
            
        # 解析消息内容
        self._parse_message_content()
        
        # 获取群聊或好友的名称
        brief_info_response = self.client.get_brief_info(self.app_id, [self.other_user_id])
        if brief_info_response.get('ret') == 200 and brief_info_response.get('data'):
            brief_info = brief_info_response['data'][0]
            self.other_user_nickname = brief_info.get('nickName', '')
            if not self.other_user_nickname:
                self.other_user_nickname = self.other_user_id
                
        if self.is_group:
            # 如果是群聊消息，获取实际发送者信息
            content_str = self.msg.get('Data', {}).get('Content', {}).get('string', '')
            if ':' in content_str and '\n' in content_str:
                parts = content_str.split(':', 1)
                if len(parts) > 1:
                    self.actual_user_id = parts[0]  # 实际发送者ID
                    # 更新消息内容，去除发送者ID前缀
                    if self.ctype == ContextType.TEXT and self.msg['Data']['MsgType'] != 10002:
                        # 只有文本消息且不是系统消息时才更新内容
                        self.content = parts[1].strip()
                        if self.content.startswith('\n'):
                            self.content = self.content[1:].strip()
                        logger.debug(f"[gewechat] 去除群聊消息发送者ID前缀，处理后内容\n: {self.content}")
            else:
                self.actual_user_id = self.from_user_id  # 如果无法解析，使用from_user_id作为实际发送者ID
        else:
            # 私聊消息，发送者就是实际用户
            self.actual_user_id = self.from_user_id
            
        # 对于系统消息，尝试从消息内容中提取发送者ID
        if self.msg['Data']['MsgType'] == 10002 and "<sysmsg type=\"pat\">" in self.msg['Data']['Content']['string']:
            try:
                # 提取XML部分
                content_str = self.msg['Data']['Content']['string']
                xml_start = content_str.find('<')
                if xml_start != -1:
                    xml_content = content_str[xml_start:]
                    # 清理非法字符
                    xml_content = self.clean_xml_content(xml_content)
                    # 解析XML
                    root = ET.fromstring(xml_content)
                    pat_elem = root.find('.//pat')
                    if pat_elem is not None:
                        from_username_elem = pat_elem.find('fromusername')
                        if from_username_elem is not None and from_username_elem.text:
                            self.actual_user_id = from_username_elem.text
                            logger.debug(f"[gewechat] 从拍一拍消息中提取发送者ID: {self.actual_user_id}")
            except Exception as e:
                logger.error(f"[gewechat] 解析拍一拍消息提取发送者ID失败: {e}")
        
        # 获取用户昵称
        if self.is_group:
            # 从群成员列表中获取实际发送者信息
            chatroom_member_list_response = self.client.get_chatroom_member_list(self.app_id, self.from_user_id)
            if chatroom_member_list_response.get('ret', 0) == 200 and chatroom_member_list_response.get('data', {}).get('memberList', []):
                # 从群成员列表中匹配actual_user_id
                for member_info in chatroom_member_list_response['data']['memberList']:
                    if member_info['wxid'] == self.actual_user_id:
                        # 先获取displayName，如果displayName为空，再获取nickName
                        self.actual_user_nickname = member_info.get('displayName', '')
                        if not self.actual_user_nickname:
                            self.actual_user_nickname = member_info.get('nickName', '')
                        break
        else:
            # 私聊场景，直接获取发送者信息
            brief_info_response = self.client.get_brief_info(self.app_id, [self.actual_user_id])
            if brief_info_response.get('ret') == 200 and brief_info_response.get('data'):
                brief_info = brief_info_response['data'][0]
                self.actual_user_nickname = brief_info.get('nickName', '')
                logger.debug(f"[gewechat] 获取到用户昵称: {self.actual_user_nickname}")
        
        # 如果actual_user_nickname为空，使用actual_user_id作为nickname
        if not self.actual_user_nickname:
            self.actual_user_nickname = self.actual_user_id
            logger.debug(f"[gewechat] 未获取到用户昵称，使用ID: {self.actual_user_id}")
            
        # 如果是拍一拍消息，替换临时的${NICKNAME}标记
        if self.msg['Data']['MsgType'] == 10002 and "<sysmsg type=\"pat\">" in self.msg['Data']['Content']['string']:
            # 处理 ${NICKNAME} 格式（带$符号，大写）
            if "${NICKNAME}" in self.content:
                self.content = self.content.replace("${NICKNAME}", self.actual_user_nickname)
                logger.debug(f"[gewechat] 替换拍一拍消息中的${{NICKNAME}}标记，最终内容: {self.content}")
            
            # 处理 {NICKNAME} 格式（不带$符号，大写）
            if "{NICKNAME}" in self.content:
                self.content = self.content.replace("{NICKNAME}", self.actual_user_nickname)
                logger.debug(f"[gewechat] 替换拍一拍消息中的{{NICKNAME}}标记，最终内容: {self.content}")
            
            # 处理 {nickname} 格式（不带$符号，小写）- 这是hello_plus.py中使用的格式
            if "{nickname}" in self.content:
                self.content = self.content.replace("{nickname}", self.actual_user_nickname)
                logger.debug(f"[gewechat] 替换拍一拍消息中的{{nickname}}标记，最终内容: {self.content}")
            
            # 确保将实际用户昵称存储为类属性，以便在回复处理中使用
            self.pat_nickname = self.actual_user_nickname
        
        # 检查是否被@
        msg_source = self.msg.get('Data', {}).get('MsgSource', '')
        self.is_at = False
        xml_parsed = False
        if msg_source:
            try:
                root = ET.fromstring(msg_source)
                atuserlist_elem = root.find('atuserlist')
                if atuserlist_elem is not None and atuserlist_elem.text:
                    atuserlist = atuserlist_elem.text
                    self.is_at = self.to_user_id in atuserlist
                    # 解析at_list
                    self.at_list = atuserlist.split(',') if ',' in atuserlist else [atuserlist]
                    xml_parsed = True
                    logger.debug(f"[gewechat] is_at: {self.is_at}. atuserlist: {atuserlist}, at_list: {self.at_list}")
            except ET.ParseError:
                pass
        
        # 只有在XML解析失败时才从PushContent中判断
        if not xml_parsed:
            self.is_at = '在群聊中@了你' in self.msg.get('Data', {}).get('PushContent', '')
            logger.debug(f"[gewechat] Parse is_at from PushContent. self.is_at: {self.is_at}")
        
        # 如果是群消息，使用正则表达式去掉@信息
        if self.is_group:
            self.content = re.sub(r'@[^\u2005]+\u2005', '', self.content)  # 去掉@信息
        else:
            # 如果不是群聊消息，保持结构统一，也要设置actual_user_id和actual_user_nickname
            self.actual_user_id = self.other_user_id
            self.actual_user_nickname = self.other_user_nickname
        
        self.my_msg = self.msg.get('Wxid', '') == self.from_user_id  # 消息是否来自自己

    @staticmethod
    def clean_xml_content(content):
        """清理 XML 内容中的非法字符"""
        import re
        # 移除非法字符（如控制字符）
        content = re.sub(r'[\x00-\x1F\x7F]', '', content)
        return content
        
    def _parse_message_content(self):
        """根据消息类型解析消息内容"""
        # 检查是否是ModContacts类型的消息
        if self.msg.get('TypeName') == 'ModContacts':
            # ModContacts消息不需要进一步处理
            self.ctype = ContextType.STATUS_SYNC
            self.content = "群成员信息更新"
            return
            
        # 检查Data字典中是否存在MsgType键
        if 'Data' not in self.msg or 'MsgType' not in self.msg['Data']:
            logger.warning(f"[gewechat] MsgType not found in message: {self.msg}")
            # 设置默认的消息类型和内容
            self.ctype = ContextType.TEXT
            self.content = "无法解析的消息格式"
            return
            
        # 对于其他类型的消息，继续正常处理
        msg_type = self.msg['Data']['MsgType']
        if msg_type == 1:  # 文本消息
            self.ctype = ContextType.TEXT
            self.content = self.msg['Data']['Content']['string']
        elif msg_type == 3:  # 图片消息
            self.ctype = ContextType.IMAGE
            self.content = self._parse_image_message()
        elif msg_type == 34:  # 语音消息
            self.ctype = ContextType.VOICE
            self.content = self._parse_voice_message()
        elif msg_type == 49:  # 引用消息、小程序、公众号等
            self.content = self._parse_app_message()
        elif msg_type == 10002:  # 系统消息（如撤回消息、拍一拍等）
            # 先获取原始内容，用于日志记录
            original_content = self.msg['Data']['Content']['string']
            # 解析系统消息
            parsed_content = self._parse_system_message()
            # 设置解析后的内容
            self.content = parsed_content
            # 记录日志，显示解析前后的内容
            logger.debug(f"[gewechat] 系统消息解析前: {original_content[:100]}...")
            logger.debug(f"[gewechat] 系统消息解析后: {parsed_content}")
        elif msg_type == 10000:  # 群聊通知（如修改群名、更换群主等）
            self.content = self._parse_group_notification()
        elif msg_type == 37:  # 好友添加请求通知
            self.content = self._parse_friend_request()
        elif msg_type == 42:  # 名片消息
            self.content = self._parse_contact_card()
        elif msg_type == 43:  # 视频消息
            self.ctype = ContextType.VIDEO
            self.content = self._parse_video_message()
        elif msg_type == 47:  # 表情消息
            self.ctype = ContextType.EMOJI
            self.content = self._parse_emoji_message()
        elif msg_type == 48:  # 地理位置消息
            self.ctype = ContextType.LOCATION
            self.content = self._parse_location_message()
        elif msg_type == 51:  # 视频号消息
            self.content = self._parse_finder_feed_message()
        elif msg_type == 2000:  # 转账消息
            self.content = self._parse_transfer_message()
        elif msg_type == 2001:  # 红包消息
            self.content = self._parse_red_packet_message()
        else:
            self.content = self.msg['Data']['Content']['string']
    
        # 确保 self.content 不为 None
        if self.content is None:
            self.content = ""  # 设置为空字符串或其他默认值
            logger.warning(f"[gewechat] self.content is None, setting to empty string")

    def _parse_emoji_message(self):
        """解析表情消息"""
        content_xml = self.msg['Data']['Content']['string']
        # 解析表情的 XML 内容，获取表情的 URL 或其他信息
        return content_xml

    def _parse_image_message(self):
        """解析图片消息"""
        img_path = os.path.join(TmpDir().path(), f"{str(self.msg_id)}.png")
        self._prepare_fn = self.download_image
        return img_path

    def _parse_voice_message(self):
        """解析语音消息"""
        if 'ImgBuf' in self.msg['Data'] and 'buffer' in self.msg['Data']['ImgBuf'] and self.msg['Data']['ImgBuf']['buffer']:
            silk_data = base64.b64decode(self.msg['Data']['ImgBuf']['buffer'])
            silk_file_name = f"voice_{str(uuid.uuid4())}.silk"
            silk_file_path = os.path.join(TmpDir().path(), silk_file_name)
            with open(silk_file_path, "wb") as f:
                f.write(silk_data)
            # TODO: silk2mp3
            return silk_file_path
        return ""

    def _parse_app_message(self):
        """解析应用消息（引用消息、小程序、公众号等）"""
        content_xml = self.msg['Data']['Content']['string']
        # Find the position of '<?xml' declaration and remove any prefix
        xml_start = content_xml.find('<?xml version=')
        if xml_start != -1:
            content_xml = content_xml[xml_start:]
        
        try:
            # Now parse the cleaned XML
            root = ET.fromstring(content_xml)
            appmsg = root.find('appmsg')

            if (appmsg is not None):
                msg_type = appmsg.find('type')
                if msg_type is not None and msg_type.text == '57':  # 引用消息
                    self.ctype = ContextType.TEXT
                    refermsg = appmsg.find('refermsg')
                    if refermsg is not None:
                        displayname = refermsg.find('displayname').text
                        quoted_content = refermsg.find('content').text
                        title = appmsg.find('title').text
                        return f"「引用内容\n{displayname}: {quoted_content}」\n{title}"
                    else:
                        return content_xml
                elif msg_type is not None and msg_type.text == '5':  # 可能是公众号文章
                    title = appmsg.find('title').text if appmsg.find('title') is not None else "无标题"
                    if "加入群聊" in title:
                        # 群聊邀请消息
                        self.ctype = ContextType.TEXT
                        return content_xml
                    else:
                        # 公众号文章
                        self.ctype = ContextType.SHARING
                        url = appmsg.find('url').text if appmsg.find('url') is not None else ""
                        return url
                else:  # 其他消息类型，暂时不解析，直接返回XML
                    self.ctype = ContextType.TEXT
                    return content_xml
            else:
                self.ctype = ContextType.TEXT
                return content_xml
        except ET.ParseError:
            self.ctype = ContextType.TEXT
            return content_xml
            
    def _parse_system_message(self):
        """解析系统消息（如撤回消息、拍一拍等）"""
        content = self.msg['Data']['Content']['string']
        
        # 处理拍一拍消息
        if "<sysmsg type=\"pat\">" in content:
            try:
                # 提取XML部分
                xml_start = content.find('<')
                if (xml_start == -1):
                    logger.error("[gewechat] No valid XML content found in pat message")
                    return "收到一个拍一拍消息"
                
                # 提取XML部分
                xml_content = content[xml_start:]
                
                # 清理非法字符
                xml_content = self.clean_xml_content(xml_content)
                
                # 解析XML
                root = ET.fromstring(xml_content)
                pat_elem = root.find('.//pat')
                if pat_elem is not None:
                    # 检查被拍的人是谁
                    to_username_elem = pat_elem.find('tousername')
                    to_username = to_username_elem.text if to_username_elem is not None else ""
                    
                    # 判断被拍的人是否是机器人自己
                    # 使用 self.msg.get('Wxid', '') 进行比较，确保准确性
                    if to_username == self.msg.get('Wxid', ''):
                        # 设置消息类型为PATPAT（被拍）
                        self.ctype = ContextType.PATPAT
                        
                        # 获取拍一拍的模板
                        template_elem = pat_elem.find('template')
                        if template_elem is not None and template_elem.text:
                            # 模板内容可能是 "${wxid_xxx}" 拍了拍我
                            # 处理CDATA部分
                            template_text = template_elem.text
                            if template_text.startswith('<![CDATA[') and template_text.endswith(']]>'):
                                template_text = template_text[9:-3]  # 去除CDATA标记
                            
                            # 获取发送者信息
                            from_username_elem = pat_elem.find('fromusername')
                            from_username = from_username_elem.text if from_username_elem is not None else ""
                            
                            # 替换模板中的变量
                            # 处理多种可能的格式：${wxid}, {wxid}, ${NICKNAME}, {NICKNAME}, {nickname}
                            if "${" + from_username + "}" in template_text:
                                # 使用用户昵称替换wxid
                                template_text = template_text.replace("${" + from_username + "}", "${NICKNAME}")
                            elif "{" + from_username + "}" in template_text:
                                # 处理不带$符号的格式
                                template_text = template_text.replace("{" + from_username + "}", "{NICKNAME}")
                                
                            # 去除多余的引号
                            if template_text.startswith('"') and template_text.endswith('"'):
                                template_text = template_text[1:-1]
                            
                            logger.debug(f"[gewechat] 解析拍一拍消息，最终模板文本: {template_text}")
                            # 返回包含多种格式的模板，以便后续处理
                            return template_text
                        else:
                            # 如果无法获取模板，尝试构建一个默认消息
                            from_username_elem = pat_elem.find('fromusername')
                            if from_username_elem is not None:
                                from_username = from_username_elem.text
                                # 返回包含多种格式的默认消息，以支持不同的替换方式
                                return "${NICKNAME} 拍了拍我"  # 在_parse_message方法中会处理各种格式的替换
                    else:
                        # 不是拍机器人，将消息类型设为TEXT，避免触发机器人回复
                        self.ctype = ContextType.TEXT
                        logger.debug(f"[gewechat] 检测到拍一拍消息，但不是拍机器人(to:{to_username})，忽略")
                        return f"监测到群内拍一拍消息，但不是拍我"
            except ET.ParseError as e:
                logger.error(f"[gewechat] Failed to parse pat message XML: {e}")
            except Exception as e:
                logger.error(f"[gewechat] Error processing pat message: {e}")
            
            return "收到一个拍一拍消息"
        
        # 处理撤回消息
        elif "<sysmsg type=\"revokemsg\">" in content:
            self.ctype = ContextType.REVOKE
            logger.debug(f"[gewechat] Revoke message content: {content}")  # 打印完整的 XML 内容
    
            # 检查 content 是否为空
            if not content.strip():
                logger.warning("[gewechat] Revoke message content is empty")
                return "撤回了一条消息"
    
            try:
                # 提取 XML 部分
                # 假设内容格式为 "用户名称:XML"，找到第一个 '<' 符号
                xml_start = content.find('<')
                if xml_start == -1:
                    logger.error("[gewechat] No valid XML content found")
                    return "撤回了一条消息"

                # 提取 XML 部分
                xml_content = content[xml_start:]

                # 清理非法字符
                xml_content = self.clean_xml_content(xml_content)

                # 打印清理后的 XML 内容
                logger.debug(f"[gewechat] Cleaned XML content: {xml_content}")

                # 解析 XML
                root = ET.fromstring(xml_content)
                revokemsg = root.find('.//revokemsg')
                if revokemsg is not None:
                    replacemsg = revokemsg.find('replacemsg')
                    if replacemsg is not None:
                        return replacemsg.text
                    else:
                        return "撤回了一条消息"
                else:
                    return "撤回了一条消息"
            except ET.ParseError as e:
                logger.error(f"[gewechat] Failed to parse revoke message XML: {e}")
                logger.error(f"[gewechat] Raw XML content: {xml_content}")  # 打印原始 XML 内容
                return "撤回了一条消息"  # 直接设置为默认文本
        
        # 其他系统消息
        
        # 尝试解析群成员变动 (加入/退出)
        try:
            # 提取 XML 部分
            xml_start = content.find('<')
            if xml_start != -1:
                xml_content = content[xml_start:]
                # 清理非法字符
                xml_content = self.clean_xml_content(xml_content)
                # 解析XML
                root = ET.fromstring(xml_content)
                sysmsgtemplate = root.find('.//sysmsgtemplate')
                if sysmsgtemplate is not None:
                    content_template = sysmsgtemplate.find('.//content_template')
                    if content_template is not None and content_template.get('type') == 'tmpl_type_profile':
                        template_elem = content_template.find('.//template')
                        if template_elem is not None and template_elem.text:
                            template_text = template_elem.text
                            # 处理CDATA部分
                            if template_text.startswith('<![CDATA[') and template_text.endswith(']]>'):
                                template_text = template_text[9:-3]  # 去除CDATA标记
                            
                            # 处理加入群聊
                            if '加入了群聊' in template_text:
                                self.ctype = ContextType.JOIN_GROUP
                                inviter_link = content_template.find(".//link[@name='username']//nickname")
                                inviter_nickname = inviter_link.text if inviter_link is not None else "未知用户"
                                invited_link = content_template.find(".//link[@name='names']//nickname")
                                invited_nickname = invited_link.text if invited_link is not None else "未知用户"
                                parsed_content = f'"{inviter_nickname}"邀请"{invited_nickname}"加入了群聊'
                                self.actual_user_nickname = invited_nickname # 记录被邀请人昵称
                                logger.debug(f"[gewechat] Parsed JOIN_GROUP from system message: {parsed_content}")
                                return parsed_content
                            # 处理通过扫描二维码加入群聊
                            elif '通过扫描' in template_text and '分享的二维码加入群聊' in template_text:
                                self.ctype = ContextType.JOIN_GROUP
                                # 获取加入者信息
                                adder_link = content_template.find(".//link[@name='adder']//nickname")
                                adder_nickname = adder_link.text if adder_link is not None else "未知用户"
                                # 获取分享二维码者信息
                                from_link = content_template.find(".//link[@name='from']//nickname")
                                from_nickname = from_link.text if from_link is not None else "未知用户"
                                parsed_content = f'"{adder_nickname}"通过扫描"{from_nickname}"分享的二维码加入群聊'
                                self.actual_user_nickname = adder_nickname # 记录被邀请人昵称
                                logger.debug(f"[gewechat] Parsed QR_CODE_JOIN_GROUP from system message: {parsed_content}")
                                return parsed_content
                            # 处理移出群聊 (需要根据实际XML模板调整关键词和解析逻辑)
                            elif '移出了群聊' in template_text or 'removed' in template_text or 'kicked' in template_text:
                                self.ctype = ContextType.EXIT_GROUP
                                # 解析操作者和被移出者 (示例逻辑, 可能需要调整)
                                operator_link = content_template.find(".//link[@name='username']//nickname") 
                                operator_nickname = operator_link.text if operator_link is not None else "未知用户"
                                removed_link = content_template.find(".//link[@name='names']//nickname") 
                                removed_nickname = removed_link.text if removed_link is not None else "未知用户"
                                parsed_content = f'"{operator_nickname}"将"{removed_nickname}"移出了群聊' # 示例格式
                                self.actual_user_nickname = removed_nickname # 记录被移出人昵称
                                logger.debug(f"[gewechat] Parsed EXIT_GROUP from system message: {parsed_content}")
                                return parsed_content
        except ET.ParseError as e:
            logger.warning(f"[gewechat] Failed to parse system message XML for group changes: {e}")
        except Exception as e:
             logger.error(f"[gewechat] Error processing system message for group changes: {e}")

        return content
        
    def _parse_group_notification(self):
        """解析群聊通知（如修改群名、更换群主等）"""
        content = self.msg['Data']['Content']['string']
        notes_join_group = ["加入群聊", "加入了群聊", "invited", "joined"]  # 可通过添加对应语言的加入群聊通知中的关键词适配更多
        notes_bot_join_group = ["邀请你", "invited you", "You've joined", "你通过扫描"]
        
        if self.is_group:
            if any(note_bot_join_group in content for note_bot_join_group in notes_bot_join_group):  # 邀请机器人加入群聊
                logger.warn("机器人加入群聊消息，不处理~")
                return content
                
        return content
        
    def _parse_friend_request(self):
        """解析好友添加请求通知"""
        self.ctype = ContextType.FRIEND_REQUEST
        return self.msg['Data']['Content']['string']
        
    def _parse_contact_card(self):
        """解析名片消息"""
        self.ctype = ContextType.CONTACT_CARD
        return self.msg['Data']['Content']['string']
        
    def _parse_video_message(self):
        """解析视频消息"""
        return self.msg['Data']['Content']['string']
        
    def _parse_finder_feed_message(self):
        """解析视频号消息"""
        self.ctype = ContextType.SHARING
        return self.msg['Data']['Content']['string']
        
    def _parse_transfer_message(self):
        """解析转账消息"""
        self.ctype = ContextType.TEXT
        return "收到一条转账消息"
        
    def _parse_red_packet_message(self):
        """解析红包消息"""
        self.ctype = ContextType.TEXT
        return "收到一个红包"

    def _parse_location_message(self):
        """解析地理位置消息"""
        content_xml = self.msg['Data']['Content']['string']
        # 解析 XML 内容，获取地理位置信息
        return content_xml

    def download_voice(self):
        try:
            voice_data = self.client.download_voice(self.msg['Wxid'], self.msg_id)
            with open(self.content, "wb") as f:
                f.write(voice_data)
        except Exception as e:
            logger.error(f"[gewechat] Failed to download voice file: {e}")

    def download_image(self):
        try:
            try:
                # 尝试下载高清图片
                content_xml = self.msg['Data']['Content']['string']
                # Find the position of '<?xml' declaration and remove any prefix
                xml_start = content_xml.find('<?xml version=')
                if xml_start != -1:
                    content_xml = content_xml[xml_start:]
                image_info = self.client.download_image(app_id=self.app_id, xml=content_xml, type=1)
            except Exception as e:
                logger.warning(f"[gewechat] Failed to download high-quality image: {e}")
                # 尝试下载普通图片
                image_info = self.client.download_image(app_id=self.app_id, xml=content_xml, type=2)
            if image_info['ret'] == 200 and image_info['data']:
                file_url = image_info['data']['fileUrl']
                logger.info(f"[gewechat] Download image file from {file_url}")
                download_url = conf().get("gewechat_download_url").rstrip('/')
                full_url = download_url + '/' + file_url
                try:
                    file_data = requests.get(full_url).content
                except Exception as e:
                    logger.error(f"[gewechat] Failed to download image file: {e}")
                    return
                with open(self.content, "wb") as f:
                    f.write(file_data)
            else:
                logger.error(f"[gewechat] Failed to download image file: {image_info}")
        except Exception as e:
            logger.error(f"[gewechat] Failed to download image file: {e}")

    def prepare(self):
        if self._prepare_fn:
            self._prepare_fn()

    def _is_non_user_message(self, msg_source: str, from_user_id: str) -> bool:
        """检查消息是否来自非用户账号（如公众号、腾讯游戏、微信团队等）
        
        Args:
            msg_source: 消息的MsgSource字段内容
            from_user_id: 消息发送者的ID
            
        Returns:
            bool: 如果是非用户消息返回True，否则返回False
            
        Note:
            通过以下方式判断是否为非用户消息：
            1. 检查MsgSource中是否包含特定标签
            2. 检查发送者ID是否为特殊账号或以特定前缀开头
        """
        # 检查发送者ID
        special_accounts = ["Tencent-Games", "weixin"]
        if from_user_id in special_accounts or from_user_id.startswith("gh_"):
            logger.debug(f"[gewechat] non-user message detected by sender id: {from_user_id}")
            return True

        # 检查消息源中的标签
        # 示例:<msgsource>\n\t<tips>3</tips>\n\t<bizmsg>\n\t\t<bizmsgshowtype>0</bizmsgshowtype>\n\t\t<bizmsgfromuser><![CDATA[weixin]]></bizmsgfromuser>\n\t</bizmsg>
        non_user_indicators = [
            "<tips>3</tips>",
            "<bizmsgshowtype>",
            "</bizmsgshowtype>",
            "<bizmsgfromuser>",
            "</bizmsgfromuser>"
        ]
        if any(indicator in msg_source for indicator in non_user_indicators):
            logger.debug(f"[gewechat] non-user message detected by msg_source indicators")
            return True

        return False
