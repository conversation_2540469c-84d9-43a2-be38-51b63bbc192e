import os
import time
import json
import web
import cv2
import uuid
import requests
from PIL import Image
from urllib.parse import urlparse, quote
import re

from bridge.context import Context, ContextType
from bridge.reply import Reply, ReplyType
from channel.chat_channel import ChatChannel
from channel.gewechat.gewechat_message import GeWeChatMessage
from common.log import logger
from common.singleton import singleton
from common.tmp_dir import TmpDir
from config import conf, save_config
from lib.gewechat import GewechatClient
from voice.audio_convert import mp3_to_silk, split_audio

MAX_UTF8_LEN = 2048

@singleton
class GeWeChatChannel(ChatChannel):
    NOT_SUPPORT_REPLYTYPE = []

    def __init__(self):
        super().__init__()

        self.base_url = conf().get("gewechat_base_url")
        if not self.base_url:
            logger.error("[gewechat] base_url is not set")
            return
        self.token = conf().get("gewechat_token")
        self.client = GewechatClient(self.base_url, self.token)

        # 如果token为空，尝试获取token
        if not self.token:
            logger.warning("[gewechat] token is not set，trying to get token")
            token_resp = self.client.get_token()
            # {'ret': 200, 'msg': '执行成功', 'data': 'tokenxxx'}
            if token_resp.get("ret") != 200:
                logger.error(f"[gewechat] get token failed: {token_resp}")
                return
            self.token = token_resp.get("data")
            conf().set("gewechat_token", self.token)
            save_config()
            logger.info(f"[gewechat] new token saved: {self.token}")
            self.client = GewechatClient(self.base_url, self.token)

        self.app_id = conf().get("gewechat_app_id")
        if not self.app_id:
            logger.warning("[gewechat] app_id is not set，trying to get new app_id when login")

        self.download_url = conf().get("gewechat_download_url")
        if not self.download_url:
            logger.warning("[gewechat] download_url is not set, unable to download image")

        logger.info(f"[gewechat] init: base_url: {self.base_url}, token: {self.token}, app_id: {self.app_id}, download_url: {self.download_url}")
        
    def startup(self):
        # 如果app_id为空或登录后获取到新的app_id，保存配置
        app_id, error_msg = self.client.login(self.app_id)
        if error_msg:
            logger.error(f"[gewechat] login failed: {error_msg}")
            return

        # 如果原来的self.app_id为空或登录后获取到新的app_id，保存配置
        if not self.app_id or self.app_id != app_id:
            conf().set("gewechat_app_id", app_id)
            save_config()
            logger.info(f"[gewechat] new app_id saved: {app_id}")
            self.app_id = app_id

        # 获取回调地址，示例地址：http://172.17.0.1:9919/v2/api/callback/collect  
        callback_url = conf().get("gewechat_callback_url")
        if not callback_url:
            logger.error("[gewechat] callback_url is not set, unable to start callback server")
            return

        # 创建新线程设置回调地址
        import threading
        def set_callback():
            # 等待服务器启动（给予适当的启动时间）
            import time
            logger.info("[gewechat] sleep 3 seconds waiting for server to start, then set callback")
            time.sleep(3)

            # 设置回调地址，{ "ret": 200, "msg": "操作成功" }
            callback_resp = self.client.set_callback(self.token, callback_url)
            if callback_resp.get("ret") != 200:
                logger.error(f"[gewechat] set callback failed: {callback_resp}")
                return
            logger.info("[gewechat] callback set successfully")

        callback_thread = threading.Thread(target=set_callback, daemon=True)
        callback_thread.start()

        # 从回调地址中解析出端口与url path，启动回调服务器  
        parsed_url = urlparse(callback_url)
        path = parsed_url.path
        # 如果没有指定端口，使用默认端口80
        port = parsed_url.port or 80
        logger.info(f"[gewechat] start callback server: {callback_url}, using port {port}")
        urls = (path, "channel.gewechat.gewechat_channel.Query")
        app = web.application(urls, globals(), autoreload=False)
        web.httpserver.runsimple(app.wsgifunc(), ("0.0.0.0", port))

    def get_segment_durations(self, file_paths):
        """
        获取每段音频的时长
        :param file_paths: 分段文件路径列表
        :return: 每段时长列表（毫秒）
        """
        from pydub import AudioSegment
        durations = []
        for path in file_paths:
            try:
                audio = AudioSegment.from_file(path)
                durations.append(len(audio))
            except Exception as e:
                logger.error(f"[gewechat] 获取音频时长失败: {path}, error: {str(e)}")
                durations.append(0)
        return durations

    def get_video_info(self, video_path):
        """获取视频信息
        Args:
            video_path: 视频文件路径
        Returns:
            tuple: (中间帧图像, 视频时长)
        """
        import cv2
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            logger.error("[gewechat] 无法打开视频文件")
            return None, 10  # 返回默认时长10秒

        # 获取视频的帧率
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            logger.warning("[gewechat] 无法获取有效的帧率，使用默认值30")
            fps = 30

        # 获取视频的总帧数
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 0:
            logger.warning("[gewechat] 无法获取有效的总帧数，使用默认时长10秒")
            duration = 10
        else:
            # 计算视频时长（秒）
            duration = total_frames / fps
            # 确保时长至少为1秒
            if duration < 1:
                duration = 10

        # 获取中间帧
        middle_frame = None
        if total_frames > 0:
            # 计算中间帧的位置
            middle_frame_pos = total_frames // 2
            # 设置视频位置到中间帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame_pos)
            # 读取中间帧
            ret, middle_frame = cap.read()
            if not ret:
                logger.warning("[gewechat] 无法读取中间帧，尝试读取第一帧")
                # 如果无法读取中间帧，回到开始位置读取第一帧
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                ret, middle_frame = cap.read()
                if not ret:
                    logger.error("[gewechat] 无法读取视频帧")
                    middle_frame = None
        else:
            # 如果无法获取总帧数，尝试读取第一帧
            ret, middle_frame = cap.read()
            if not ret:
                logger.error("[gewechat] 无法读取视频帧")
                middle_frame = None

        # 释放视频对象
        cap.release()

        return middle_frame, duration

    def send_video(self, to_wxid, video_url, thumb_url, video_duration):
        """发送视频消息
        Args:
            to_wxid: 接收人wxid
            video_url: 视频URL
            thumb_url: 视频缩略图URL，如果为None则自动生成
            video_duration: 视频时长(秒)
        Returns:
            dict: 发送结果
        """
        video_file_path = None
        thumb_file_path = None
        
        try:
            logger.debug(f"[gewechat] 开始发送视频消息: video_url={video_url}, to_wxid={to_wxid}")
            
            # 下载视频到本地临时目录
            video_file_name = f"video_{str(uuid.uuid4())}.mp4"
            video_file_path = os.path.join(TmpDir().path(), video_file_name)
            logger.debug(f"[gewechat] 视频将保存到: {video_file_path}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            # 判断是否需要使用代理
            proxies = None
            if video_url.startswith(('http://', 'https://')):
                from common.proxy_config import should_use_proxy
                if should_use_proxy(video_url):
                    from common.proxy_config import get_proxies
                    proxies = get_proxies()
                    if proxies:
                        logger.debug(f"[gewechat] 使用代理下载视频: {proxies}")
                else:
                    logger.debug(f"[gewechat] 不使用代理下载视频: {video_url}")

            # 下载视频
            logger.debug(f"[gewechat] 开始下载视频: {video_url}")
            with requests.get(video_url, headers=headers, stream=True, timeout=30, proxies=proxies) as r:
                r.raise_for_status()
                with open(video_file_path, 'wb') as f:
                    for chunk in r.iter_content(chunk_size=8192):
                        f.write(chunk)
            logger.debug(f"[gewechat] 视频下载完成: {video_file_path}")

            # 生成缩略图
            if thumb_url is None:
                logger.debug(f"[gewechat] 缩略图为空，自动生成缩略图")
                middle_frame, duration = self.get_video_info(video_file_path)
                if middle_frame is not None:
                    # 保持原图尺寸
                    image = Image.fromarray(cv2.cvtColor(middle_frame, cv2.COLOR_BGR2RGB))
                    thumb_file_name = f"thumb_{str(uuid.uuid4())}.jpg"
                    thumb_file_path = os.path.join(TmpDir().path(), thumb_file_name)
                    logger.debug(f"[gewechat] 缩略图将保存到: {thumb_file_path}")
                    image.save(thumb_file_path, 'JPEG', quality=95)
                    logger.debug(f"[gewechat] 缩略图生成成功: {thumb_file_path}")
                    # 更新视频时长
                    if duration:
                        video_duration = int(duration)
                        logger.debug(f"[gewechat] 获取到视频时长: {video_duration}秒")
                else:
                    # 如果无法读取视频帧，创建一个默认的黑色缩略图
                    logger.warning("[gewechat] 无法读取视频帧，使用默认黑色缩略图")
                    image = Image.new('RGB', (480, 270), color='black')
                    thumb_file_name = f"thumb_{str(uuid.uuid4())}.jpg"
                    thumb_file_path = os.path.join(TmpDir().path(), thumb_file_name)
                    image.save(thumb_file_path, 'JPEG', quality=95)
                    logger.debug(f"[gewechat] 缩略图生成成功: {thumb_file_path}")
                    # 更新视频时长
                    if duration:
                        video_duration = int(duration)
                    else:
                        video_duration = 10  # 默认10秒
                    logger.debug(f"[gewechat] 获取到视频时长: {video_duration}秒")
            else:
                # 使用提供的缩略图URL
                thumb_file_name = f"thumb_{str(uuid.uuid4())}.jpg"
                thumb_file_path = os.path.join(TmpDir().path(), thumb_file_name)
                logger.debug(f"[gewechat] 缩略图将保存到: {thumb_file_path}")
                
                # 判断是否需要使用代理
                thumb_proxies = None
                if thumb_url.startswith(('http://', 'https://')):
                    from common.proxy_config import should_use_proxy
                    if should_use_proxy(thumb_url):
                        from common.proxy_config import get_proxies
                        thumb_proxies = get_proxies()
                        if thumb_proxies:
                            logger.debug(f"[gewechat] 使用代理下载缩略图: {thumb_proxies}")
                    else:
                        logger.debug(f"[gewechat] 不使用代理下载缩略图: {thumb_url}")
                
                # 下载缩略图
                logger.debug(f"[gewechat] 开始下载缩略图: {thumb_url}")
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                try:
                    with requests.get(thumb_url, headers=headers, stream=True, timeout=30, proxies=thumb_proxies) as r:
                        r.raise_for_status()
                        with open(thumb_file_path, 'wb') as f:
                            for chunk in r.iter_content(chunk_size=8192):
                                f.write(chunk)
                    logger.debug(f"[gewechat] 缩略图下载完成: {thumb_file_path}")
                except Exception as e:
                    # 如果下载失败，创建一个默认的黑色缩略图
                    logger.warning(f"[gewechat] 下载缩略图失败: {str(e)}，使用默认黑色缩略图")
                    image = Image.new('RGB', (480, 270), color='black')
                    image.save(thumb_file_path, 'JPEG', quality=95)
                
                # 更新视频时长
                if video_duration is None:
                    video_duration = 10  # 默认10秒
                else:
                    try:
                        video_duration = int(video_duration)
                    except (TypeError, ValueError):
                        logger.warning(f"[gewechat] 无法将视频时长转换为整数: {video_duration}，使用默认值10秒")
                        video_duration = 10

            # 构造本地URL
            callback_url = conf().get("gewechat_callback_url")
            if not callback_url:
                raise Exception("callback_url not configured")
                
            # 使用callback_url构造视频和缩略图URL
            download_url = callback_url
            
            # 确保路径使用正确的分隔符
            video_file_path = video_file_path.replace('\\', '/')
            thumb_file_path = thumb_file_path.replace('\\', '/')
            
            # 构建正确的URL路径
            local_video_url = f"{download_url}?file={video_file_path}"
            local_thumb_url = f"{download_url}?file={thumb_file_path}"
            
            logger.debug(f"[gewechat] 生成本地视频URL: {local_video_url}")
            logger.debug(f"[gewechat] 生成缩略图URL: {local_thumb_url}")

            # 发送视频
            logger.debug(f"[gewechat] 准备发送视频: app_id={self.app_id}, to_wxid={to_wxid}, video_url={local_video_url}, thumb_url={local_thumb_url}, duration={video_duration}")
            
            # 确保video_duration是整数
            if video_duration is None:
                video_duration = 10  # 默认10秒
            else:
                try:
                    video_duration = int(video_duration)
                except (TypeError, ValueError):
                    logger.warning(f"[gewechat] 无法将视频时长转换为整数: {video_duration}，使用默认值10秒")
                    video_duration = 10
                    
            resp = self.client.post_video(
                self.app_id,
                to_wxid,
                local_video_url,
                local_thumb_url,
                video_duration
            )
            
            if resp.get("ret") != 200:
                error_msg = resp.get("msg", "未知错误")
                logger.error(f"[gewechat] 发送视频失败: {error_msg}, response={resp}")
                return None

            logger.info(f"[gewechat] 视频发送成功: {resp}")
            return resp.get("data")

        except requests.exceptions.RequestException as e:
            logger.error(f"[gewechat] 下载视频失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"[gewechat] 发送视频异常: {str(e)}")
            return None
        finally:
            # 清理临时文件
            try:
                if video_file_path and os.path.exists(video_file_path):
                    os.remove(video_file_path)
                    logger.debug(f"[gewechat] 清理临时视频文件: {video_file_path}")
                if thumb_file_path and os.path.exists(thumb_file_path):
                    os.remove(thumb_file_path)
                    logger.debug(f"[gewechat] 清理临时缩略图文件: {thumb_file_path}")
            except Exception as e:
                logger.warning(f"[gewechat] 清理临时文件失败: {str(e)}")

    def send(self, reply: Reply, context: Context):
        receiver = context["receiver"]
        gewechat_message = context.get("msg")
        if reply.type in [ReplyType.TEXT, ReplyType.ERROR, ReplyType.INFO]:
            reply_text = reply.content
            ats = ""
            if gewechat_message and gewechat_message.is_group:
                ats = gewechat_message.actual_user_id
            self.client.post_text(self.app_id, receiver, reply_text, ats)
            logger.info("[gewechat] Do send text to {}: {}".format(receiver, reply_text))
        elif reply.type == ReplyType.APPMSG:
            # 处理应用消息，直接使用post_app_msg发送
            try:
                appmsg_content = reply.content
                result = self.client.post_app_msg(self.app_id, receiver, appmsg_content)
                if result.get("ret") == 200:
                    logger.info(f"[gewechat] Do send app msg to {receiver}")
                else:
                    logger.error(f"[gewechat] Failed to send app msg: {result}")
                    # 如果发送失败，尝试作为文本发送
                    # 检查是否有备用文本内容
                    backup_content = getattr(reply, "content_backup", None)
                    if backup_content:
                        logger.info(f"[gewechat] 使用备用文本内容发送消息")
                        self.client.post_text(self.app_id, receiver, backup_content, "")
                    else:
                        self.client.post_text(self.app_id, receiver, "消息卡片发送失败，请联系管理员", "")
            except Exception as e:
                logger.error(f"[gewechat] Error sending app msg: {str(e)}")
                # 发生异常时，尝试作为文本发送
                # 检查是否有备用文本内容
                backup_content = getattr(reply, "content_backup", None)
                if backup_content:
                    logger.info(f"[gewechat] 使用备用文本内容发送消息")
                    self.client.post_text(self.app_id, receiver, backup_content, "")
                else:
                    self.client.post_text(self.app_id, receiver, "消息卡片发送失败，请联系管理员", "")
        elif reply.type == ReplyType.XML or reply.type == ReplyType.LINK:
            # 对于 XML 和 LINK 类型，尝试解析 XML 并使用 post_link 发送链接卡片
            xml_content = reply.content
            try:
                # 检查是否是视频直播类型
                if '<type>63</type>' in xml_content and '<finderLive>' in xml_content:
                    # 处理视频直播类型
                    nickname_match = re.search(r'<nickname>(.*?)</nickname>', xml_content, re.DOTALL)
                    desc_match = re.search(r'<desc>(.*?)</desc>', xml_content, re.DOTALL)
                    cover_url_match = re.search(r'<coverUrl>(.*?)</coverUrl>', xml_content, re.DOTALL)
                    
                    title = nickname_match.group(1) if nickname_match else "视频直播"
                    desc = desc_match.group(1) if desc_match else "点击观看直播"
                    link_url = "https://channels.weixin.qq.com/"  # 微信视频号链接
                    
                    # 使用更可靠的默认图片URL
                    thumb_url = "https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico"  # 微信图标
                    
                    # 尝试使用封面图片，但如果失败则使用默认图片
                    try:
                        if cover_url_match and cover_url_match.group(1):
                            cover_url = cover_url_match.group(1).replace("&amp;", "&")
                            if not "<![CDATA[" in cover_url and not "]]>" in cover_url:
                                # 验证URL是否可访问
                                try:
                                    test_resp = requests.head(cover_url, timeout=2)
                                    if test_resp.status_code == 200:
                                        thumb_url = cover_url
                                except:
                                    # 如果无法访问，使用默认图片
                                    pass
                    except Exception as e:
                        logger.warning(f"[gewechat] Error processing cover URL: {str(e)}")
                    
                    logger.debug(f"[gewechat] Extracted live video info: title={title}, desc={desc}, url={link_url}, thumb_url={thumb_url}")
                    
                    # 使用 post_link 发送链接卡片
                    try:
                        result = self.client.post_link(self.app_id, receiver, title, desc, link_url, thumb_url)
                        if result.get("ret") == 200:
                            logger.info(f"[gewechat] Do send live video card to {receiver}: {title}")
                        else:
                            logger.error(f"[gewechat] Failed to send live video card: {result}")
                            # 如果发送失败，尝试作为文本发送
                            self.client.post_text(self.app_id, receiver, f"{title} - {desc}\n请打开微信视频号观看", "")
                    except Exception as e:
                        logger.error(f"[gewechat] Error sending live video card: {str(e)}")
                        # 发生异常时，尝试作为文本发送
                        self.client.post_text(self.app_id, receiver, f"{title} - {desc}\n请打开微信视频号观看", "")
                    return
                
                # 常规链接卡片处理
                # 尝试从 XML 中提取链接信息
                title_match = re.search(r'<title>(.*?)</title>', xml_content, re.DOTALL)
                des_match = re.search(r'<des><!\[CDATA\[(.*?)\]\]></des>', xml_content, re.DOTALL) or re.search(r'<des>(.*?)</des>', xml_content, re.DOTALL)
                url_match = re.search(r'<url><!\[CDATA\[(.*?)\]\]></url>', xml_content, re.DOTALL) or re.search(r'<url>(.*?)</url>', xml_content, re.DOTALL)
                thumb_match = re.search(r'<thumburl><!\[CDATA\[(.*?)\]\]></thumburl>', xml_content, re.DOTALL) or re.search(r'<thumburl>(.*?)</thumburl>', xml_content, re.DOTALL)
                
                title = title_match.group(1) if title_match else "链接卡片"
                desc = des_match.group(1) if des_match else "点击查看详情"
                link_url = url_match.group(1) if url_match else "https://example.com"
                thumb_url = "https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico"  # 默认使用微信图标
                
                # 尝试使用XML中的缩略图，但如果失败则使用默认图片
                try:
                    if thumb_match and thumb_match.group(1):
                        extracted_thumb = thumb_match.group(1).replace("&amp;", "&")
                        if not "<![CDATA[" in extracted_thumb and not "]]>" in extracted_thumb:
                            # 验证URL是否可访问
                            try:
                                test_resp = requests.head(extracted_thumb, timeout=2)
                                if test_resp.status_code == 200:
                                    thumb_url = extracted_thumb
                            except:
                                # 如果无法访问，使用默认图片
                                pass
                except Exception as e:
                    logger.warning(f"[gewechat] Error processing thumb URL: {str(e)}")
                
                # 清理URL中的HTML实体
                link_url = link_url.replace("&amp;", "&")
                thumb_url = thumb_url.replace("&amp;", "&")
                
                # 如果缩略图URL为空或无效，使用默认图片
                if not thumb_url or "<![CDATA[" in thumb_url or "]]>" in thumb_url:
                    thumb_url = "https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico"  # 微信图标
                
                logger.debug(f"[gewechat] Extracted link info: title={title}, desc={desc}, url={link_url}, thumb_url={thumb_url}")
                
                # 使用 post_link 发送链接卡片
                try:
                    result = self.client.post_link(self.app_id, receiver, title, desc, link_url, thumb_url)
                    if result.get("ret") == 200:
                        logger.info(f"[gewechat] Do send link card to {receiver}: {title}")
                    else:
                        logger.error(f"[gewechat] Failed to send link card: {result}")
                        # 如果发送失败，尝试作为文本发送
                        self.client.post_text(self.app_id, receiver, f"{title}\n{desc}\n{link_url}", "")
                except Exception as e:
                    logger.error(f"[gewechat] Error sending link card: {str(e)}")
                    # 发生异常时，尝试作为文本发送
                    self.client.post_text(self.app_id, receiver, f"{title}\n{desc}\n{link_url}", "")
            except Exception as e:
                logger.error(f"[gewechat] Error sending link card: {str(e)}")
                # 发生异常时，尝试作为文本发送
                self.client.post_text(self.app_id, receiver, "卡片消息发送失败，请联系管理员", "")
        elif reply.type == ReplyType.MINIAPP:
            # 对于小程序类型，尝试解析 XML 并使用 post_mini_app 发送
            miniapp_content = reply.content
            try:
                # 尝试从 XML 中提取小程序信息
                # 尝试从 XML 中提取链接信息
                title_match = re.search(r'<title>(.*?)</title>', miniapp_content, re.DOTALL)
                username_match = re.search(r'<username><!\[CDATA\[(.*?)\]\]></username>', miniapp_content, re.DOTALL) or re.search(r'<username>(.*?)</username>', miniapp_content, re.DOTALL)
                appid_match = re.search(r'<appid><!\[CDATA\[(.*?)\]\]></appid>', miniapp_content, re.DOTALL) or re.search(r'<appid>(.*?)</appid>', miniapp_content, re.DOTALL)
                pagepath_match = re.search(r'<pagepath><!\[CDATA\[(.*?)\]\]></pagepath>', miniapp_content, re.DOTALL) or re.search(r'<pagepath>(.*?)</pagepath>', miniapp_content, re.DOTALL)
                weappiconurl_match = re.search(r'<weappiconurl><!\[CDATA\[(.*?)\]\]></weappiconurl>', miniapp_content, re.DOTALL) or re.search(r'<weappiconurl>(.*?)</weappiconurl>', miniapp_content, re.DOTALL)
                
                title = title_match.group(1) if title_match else "小程序"
                user_name = username_match.group(1) if username_match else ""
                mini_app_id = appid_match.group(1) if appid_match else ""
                page_path = pagepath_match.group(1) if pagepath_match else ""
                cover_img_url = weappiconurl_match.group(1) if weappiconurl_match else ""
                
                # 清理URL中的HTML实体
                page_path = page_path.replace("&amp;", "&")
                cover_img_url = cover_img_url.replace("&amp;", "&")
                
                # 如果图标URL为空或无效，使用默认图片
                if not cover_img_url or "<![CDATA[" in cover_img_url or "]]>" in cover_img_url:
                    cover_img_url = "https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico"  # 微信图标
                
                display_name = title
                
                logger.debug(f"[gewechat] Extracted mini app info: title={title}, user_name={user_name}, mini_app_id={mini_app_id}, page_path={page_path}, cover_img_url={cover_img_url}")
                
                if mini_app_id and user_name:
                    # 使用 post_mini_app 发送小程序
                    result = self.client.post_mini_app(
                        self.app_id, 
                        receiver, 
                        mini_app_id, 
                        display_name, 
                        page_path, 
                        cover_img_url, 
                        title, 
                        user_name
                    )
                    if result.get("ret") == 200:
                        logger.info(f"[gewechat] Do send mini app to {receiver}: {title}")
                    else:
                        logger.error(f"[gewechat] Failed to send mini app: {result}")
                        # 如果发送失败，尝试作为文本发送
                        self.client.post_text(self.app_id, receiver, f"小程序: {title}\n请在微信中搜索打开", "")
                else:
                    logger.error(f"[gewechat] Missing required mini app info: appid={mini_app_id}, username={user_name}")
                    self.client.post_text(self.app_id, receiver, f"小程序: {title}\n请在微信中搜索打开", "")
            except Exception as e:
                logger.error(f"[gewechat] Error sending mini app: {str(e)}")
                # 发生异常时，尝试作为文本发送
                self.client.post_text(self.app_id, receiver, "小程序发送失败，请联系管理员", "")
        elif reply.type == ReplyType.VOICE:
            try:
                content = reply.content
                if content.endswith('.mp3'):
                    logger.debug(f"[gewechat] 开始处理语音文件: {content}")
                    # 分割音频文件
                    audio_length_ms, files = split_audio(content, 60 * 1000)  # 按60秒分段
                    logger.debug(f"[gewechat] 语音文件分段完成，共{len(files)}段")
                    
                    # 获取每段时长
                    segment_durations = self.get_segment_durations(files)
                    
                    # 逐段发送
                    for i, (fcontent, segment_duration) in enumerate(zip(files, segment_durations)):
                        try:
                            logger.debug(f"[gewechat] 开始处理第{i+1}段语音，时长{segment_duration}ms: {fcontent}")
                            
                            # 转换为silk格式
                            silk_path = fcontent + '.silk'
                            duration = mp3_to_silk(fcontent, silk_path)
                            
                            # 构建回调URL
                            callback_url = conf().get("gewechat_callback_url")
                            # 使用绝对路径
                            abs_silk_path = os.path.abspath(silk_path)
                            silk_url = callback_url + "?file=" + abs_silk_path
                            
                            # 发送语音
                            logger.debug(f"[gewechat] 准备发送第{i+1}段语音，silk_url: {silk_url}, duration: {duration}")
                            self.client.post_voice(self.app_id, receiver, silk_url, duration)
                            logger.info(f"[gewechat] 成功发送第{i+1}段语音: {silk_url}, duration: {duration/1000.0}秒")
                            
                            # 添加发送间隔，避免发送太快
                            if i < len(files) - 1:
                                sleep_time = min(segment_duration/1000, 3)  # 最多等待3秒
                                logger.debug(f"[gewechat] 等待{sleep_time}秒后发送下一段")
                                time.sleep(sleep_time)
                                
                        except Exception as e:
                            logger.error(f"[gewechat] 发送第{i+1}段语音失败: {str(e)}")
                            continue
                            
                    # 清理临时文件
                    try:
                        for f in files:
                            if os.path.exists(f):
                                os.remove(f)
                            silk_file = f + '.silk'
                            if os.path.exists(silk_file):
                                os.remove(silk_file)
                    except Exception as e:
                        logger.warning(f"[gewechat] 清理临时文件失败: {str(e)}")
                        
                    return
                else:
                    logger.error(f"[gewechat] 语音文件不是mp3格式: {content}, 仅支持mp3格式")
            except Exception as e:
                logger.error(f"[gewechat] 发送语音失败: {str(e)}")
                raise
        elif reply.type == ReplyType.IMAGE_URL:
            img_url = reply.content
            try:
                if img_url.startswith(('http://', 'https://')) and not img_url.startswith(conf().get("gewechat_callback_url", "")):
                    logger.debug(f"[gewechat] 检测到外部图片URL，尝试下载: {img_url}")
                    # 下载图片
                    try:
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                        }
                        
                        # 对特定域名不使用代理
                        proxies = None
                        if "api.317ak.com" in img_url or any(domain in img_url for domain in ["api.317ak.com"]):
                            logger.debug(f"[gewechat] 检测到特定图片API，不使用代理: {img_url}")
                        else:
                            from common.proxy_config import get_proxies
                            proxies = get_proxies()
                            if proxies:
                                logger.debug(f"[gewechat] 使用代理下载图片: {proxies}")
                        
                        response = requests.get(img_url, headers=headers, proxies=proxies, timeout=30)
                        if response.status_code == 200:
                            # 检查内容类型
                            content_type = response.headers.get('Content-Type', '')
                            if not content_type.startswith('image/'):
                                logger.warning(f"[gewechat] 下载的内容不是图片: {content_type}")
                                # 尝试检查内容是否为图片
                                try:
                                    from PIL import Image
                                    import io
                                    Image.open(io.BytesIO(response.content))
                                    # 如果能打开，说明是图片
                                    logger.info(f"[gewechat] 内容可以作为图片打开，继续处理")
                                except Exception as e:
                                    logger.error(f"[gewechat] 内容无法作为图片打开: {str(e)}")
                                    # 如果下载的内容不是图片，尝试作为文本发送
                                    self.client.post_text(self.app_id, receiver, f"下载的内容不是有效图片: {img_url}", "")
                                    return
                            
                            # 检查内容大小
                            if len(response.content) < 100:  # 图片太小，可能不是有效图片
                                logger.warning(f"[gewechat] 下载的图片太小，可能不是有效图片: {len(response.content)} 字节")
                                # 尝试不使用代理重新下载
                                try:
                                    logger.debug(f"[gewechat] 尝试不使用代理重新下载图片: {img_url}")
                                    retry_response = requests.get(img_url, headers=headers, proxies=None, timeout=30)
                                    if retry_response.status_code == 200 and len(retry_response.content) > 100:
                                        logger.info(f"[gewechat] 不使用代理重新下载成功，图片大小: {len(retry_response.content)} 字节")
                                        response = retry_response
                                    else:
                                        # 如果图片太小，尝试作为文本发送
                                        self.client.post_text(self.app_id, receiver, f"下载的图片无效或太小: {img_url}", "")
                                        return
                                except Exception as e:
                                    logger.error(f"[gewechat] 重新下载图片失败: {str(e)}")
                                    # 如果图片太小，尝试作为文本发送
                                    self.client.post_text(self.app_id, receiver, f"下载的图片无效或太小: {img_url}", "")
                                    return
                                    
                            # 保存图片到临时目录
                            img_file_name = f"img_{str(uuid.uuid4())}.png"
                            img_file_path = os.path.join(TmpDir().path(), img_file_name)
                            with open(img_file_path, "wb") as f:
                                f.write(response.content)
                            
                            # 验证保存的图片是否有效
                            try:
                                from PIL import Image
                                img = Image.open(img_file_path)
                                width, height = img.size
                                if width < 10 or height < 10:  # 图片尺寸太小
                                    logger.warning(f"[gewechat] 图片尺寸太小: {width}x{height}")
                                    # 创建一个默认图片
                                    default_img = Image.new('RGB', (200, 200), color='white')
                                    # 在图片上写文字
                                    from PIL import ImageDraw, ImageFont
                                    draw = ImageDraw.Draw(default_img)
                                    try:
                                        # 尝试加载字体，如果失败则使用默认字体
                                        font = ImageFont.truetype("arial.ttf", 20)
                                    except:
                                        font = ImageFont.load_default()
                                    draw.text((10, 90), "原图片无效", fill="black", font=font)
                                    default_img.save(img_file_path)
                                    logger.info(f"[gewechat] 已创建默认图片: {img_file_path}")
                            except Exception as e:
                                logger.error(f"[gewechat] 验证图片失败: {str(e)}")
                                # 创建一个默认图片
                                try:
                                    from PIL import Image, ImageDraw, ImageFont
                                    default_img = Image.new('RGB', (200, 200), color='white')
                                    draw = ImageDraw.Draw(default_img)
                                    try:
                                        font = ImageFont.truetype("arial.ttf", 20)
                                    except:
                                        font = ImageFont.load_default()
                                    draw.text((10, 90), "原图片无效", fill="black", font=font)
                                    default_img.save(img_file_path)
                                    logger.info(f"[gewechat] 已创建默认图片: {img_file_path}")
                                except Exception as e2:
                                    logger.error(f"[gewechat] 创建默认图片失败: {str(e2)}")
                                    # 如果创建默认图片也失败，尝试作为文本发送
                                    self.client.post_text(self.app_id, receiver, f"图片处理失败: {img_url}", "")
                                    return
                            
                            # 构建回调URL
                            callback_url = conf().get("gewechat_callback_url")
                            # 确保路径使用正确的分隔符并进行URL编码
                            img_file_path = img_file_path.replace('\\', '/')
                            encoded_path = quote(img_file_path)
                            img_url = callback_url + "?file=" + encoded_path
                            logger.debug(f"[gewechat] 外部图片已下载并转换为本地URL: {img_url}")
                        else:
                            logger.error(f"[gewechat] 下载外部图片失败，状态码: {response.status_code}")
                            # 如果下载失败，尝试作为文本发送
                            self.client.post_text(self.app_id, receiver, f"图片下载失败，原始URL: {img_url}", "")
                            return
                    except Exception as e:
                        logger.error(f"[gewechat] 下载外部图片异常: {str(e)}")
                        # 如果下载失败，尝试作为文本发送
                        self.client.post_text(self.app_id, receiver, f"图片下载失败，原始URL: {img_url}", "")
                        return
                
                # 发送图片
                self.client.post_image(self.app_id, receiver, img_url)
                logger.info("[gewechat] sendImage url={}, receiver={}".format(img_url, receiver))
            except Exception as e:
                logger.error(f"[gewechat] 发送图片失败: {str(e)}")
                # 如果发送失败，尝试作为文本发送
                self.client.post_text(self.app_id, receiver, f"图片发送失败，URL: {img_url}", "")
        elif reply.type == ReplyType.IMAGE:
            image_storage = reply.content
            # 检查是否为字节对象（bytes）
            if isinstance(image_storage, bytes):
                # 直接使用字节数据
                img_data = image_storage
            else:
                # 处理文件对象
                try:
                    image_storage.seek(0)
                    img_data = image_storage.read()
                except Exception as e:
                    logger.error(f"[gewechat] Error reading image data: {e}")
                    return
                    
            # 保存图片到临时目录
            img_file_name = f"img_{str(uuid.uuid4())}.png"
            img_file_path = os.path.join(TmpDir().path(), img_file_name)
            with open(img_file_path, "wb") as f:
                f.write(img_data)
            # 构建回调URL
            callback_url = conf().get("gewechat_callback_url")
            # 确保路径使用正确的分隔符并进行URL编码
            img_file_path = img_file_path.replace('\\', '/')
            encoded_path = quote(img_file_path)
            img_url = callback_url + "?file=" + encoded_path
            
            try:
                self.client.post_image(self.app_id, receiver, img_url)
                logger.info("[gewechat] sendImage, receiver={}, url={}".format(receiver, img_url))
            except Exception as e:
                logger.error(f"[gewechat] 发送图片失败: {str(e)}")
                # 如果发送失败，尝试作为文本发送
                self.client.post_text(self.app_id, receiver, "图片发送失败，请联系管理员", "")
        elif reply.type == ReplyType.VIDEO_URL:
            try:
                video_url = reply.content
                logger.debug(f"[gewechat] 收到视频URL消息: {video_url}")
                
                # 检查视频URL是否有效
                if not video_url.startswith(('http://', 'https://')):
                    logger.error(f"[gewechat] 无效的视频URL: {video_url}")
                    return
                    
                # 默认视频时长设为10秒
                video_duration = 10
                
                # 不再使用视频URL作为缩略图，让send_video方法自己生成缩略图
                result = self.send_video(receiver, video_url, None, video_duration)
                if result:
                    logger.info(f"[gewechat] 视频发送成功: receiver={receiver}, video_url={video_url}")
                else:
                    logger.error(f"[gewechat] 视频发送失败: receiver={receiver}, video_url={video_url}")
                    # 如果发送失败，尝试作为文本发送
                    self.client.post_text(self.app_id, receiver, f"视频发送失败，URL: {video_url}", "")
            except Exception as e:
                logger.error(f"[gewechat] 处理视频消息异常: {str(e)}", exc_info=True)
                # 如果发送失败，尝试作为文本发送
                self.client.post_text(self.app_id, receiver, f"视频发送失败: {str(e)}", "")

  


class Query:
    def GET(self):
        # 搭建简单的文件服务器，用于向gewechat服务传输语音等文件，但只允许访问tmp目录下的文件
        params = web.input(file="")
        file_path = params.file
        if file_path:
            logger.debug(f"[gewechat] 请求文件: {file_path}")
            
            # 处理路径中的斜杠，确保使用系统分隔符
            file_path = file_path.replace('/', os.path.sep).replace('\\', os.path.sep)
            
            # 确保文件路径是绝对路径
            if not os.path.isabs(file_path):
                # 如果是相对路径，尝试拼接tmp目录路径
                if not os.path.dirname(file_path):
                    file_path = os.path.join("tmp", file_path)
            
            # 使用os.path.abspath清理路径
            clean_path = os.path.abspath(file_path)
            # 获取tmp目录的绝对路径
            tmp_dir = os.path.abspath("tmp")
            
            logger.debug(f"[gewechat] 处理后的文件路径: {clean_path}, tmp目录: {tmp_dir}")
            
            # 检查文件是否存在
            if not os.path.exists(clean_path):
                logger.error(f"[gewechat] File not found: {clean_path}")
                raise web.notfound()
            
            # 检查文件是否在tmp目录下，或者文件名以.silk结尾
            if not (clean_path.startswith(tmp_dir) or clean_path.endswith('.silk')):
                logger.error(f"[gewechat] Forbidden access to file outside tmp directory: file_path={file_path}, clean_path={clean_path}, tmp_dir={tmp_dir}")
                raise web.forbidden()

            try:
                # 设置正确的Content-Type
                content_type = "application/octet-stream"
                if clean_path.endswith('.jpg') or clean_path.endswith('.jpeg'):
                    content_type = "image/jpeg"
                elif clean_path.endswith('.png'):
                    content_type = "image/png"
                elif clean_path.endswith('.mp4'):
                    content_type = "video/mp4"
                elif clean_path.endswith('.silk'):
                    content_type = "audio/silk"
                
                # 设置响应头
                web.header('Content-Type', content_type)
                web.header('Content-Disposition', f'inline; filename="{os.path.basename(clean_path)}"')
                
                # 临时禁用代理，确保本地文件访问不受影响
                original_http_proxy = os.environ.get('HTTP_PROXY', '')
                original_https_proxy = os.environ.get('HTTPS_PROXY', '')
                try:
                    # 临时清除代理环境变量
                    if 'HTTP_PROXY' in os.environ:
                        del os.environ['HTTP_PROXY']
                    if 'HTTPS_PROXY' in os.environ:
                        del os.environ['HTTPS_PROXY']
                    
                    logger.debug(f"[gewechat] 开始读取文件: {clean_path}, Content-Type: {content_type}")
                    with open(clean_path, 'rb') as f:
                        file_content = f.read()
                        logger.debug(f"[gewechat] 文件读取成功: {clean_path}, 大小: {len(file_content)} 字节")
                        return file_content
                finally:
                    # 恢复代理环境变量
                    if original_http_proxy:
                        os.environ['HTTP_PROXY'] = original_http_proxy
                    if original_https_proxy:
                        os.environ['HTTPS_PROXY'] = original_https_proxy
            except Exception as e:
                logger.error(f"[gewechat] Error reading file {clean_path}: {str(e)}")
                raise web.notfound()
                
        return "gewechat callback server is running"

    def POST(self):
        channel = GeWeChatChannel()
        data = json.loads(web.data())
        logger.debug("[gewechat] receive data: {}".format(data))
        
        # gewechat服务发送的回调测试消息
        if isinstance(data, dict) and 'testMsg' in data and 'token' in data:
            logger.debug(f"[gewechat] 收到gewechat服务发送的回调测试消息")
            return "success"
        
        # 检查是否是ModContacts类型的消息
        if data.get('TypeName') == 'ModContacts':
             logger.debug(f"[gewechat] 收到ModContacts消息，跳过处理")
             return "success"
            
        # 解析消息
        gewechat_msg = GeWeChatMessage(data, channel.client)

       
       
        # 判断是否需要忽略语音消息
        if gewechat_msg.ctype == ContextType.VOICE:
            if conf().get("speech_recognition") != True:
                return "success"


        # 微信客户端的状态同步消息
        if gewechat_msg.ctype == ContextType.STATUS_SYNC:
            logger.debug(f"[gewechat] ignore status sync message: {gewechat_msg.content}")
            return "success"

        # 忽略非用户消息（如公众号、系统通知等）
        if gewechat_msg.ctype == ContextType.NON_USER_MSG:
            logger.debug(f"[gewechat] ignore non-user message from {gewechat_msg.from_user_id}: {gewechat_msg.content}")
            return "success"

        # 忽略来自自己的消息
        if gewechat_msg.my_msg:
            logger.debug(f"[gewechat] ignore message from myself: {gewechat_msg.actual_user_id}: {gewechat_msg.content}")
            return "success"

        # 忽略过期的消息
        if int(gewechat_msg.create_time) < int(time.time()) - 60 * 5: # 跳过5分钟前的历史消息
            logger.debug(f"[gewechat] ignore expired message from {gewechat_msg.actual_user_id}: {gewechat_msg.content}")
            return "success"

       # 根据消息类型处理不同的回调消息
        msg_type = gewechat_msg.msg.get('Data', {}).get('MsgType')
        if msg_type == 1:  # 文本消息
            logger.info(f"[gewechat] 收到文本消息: {gewechat_msg.content[:50]}")
        elif msg_type == 3:  # 图片消息
            logger.info(f"[gewechat] 收到图片消息: {gewechat_msg.content[:50]}")
        elif msg_type == 34:  # 语音消息
            logger.info(f"[gewechat] 收到语音消息: {gewechat_msg.content[:50]}")
        elif msg_type == 49:  # 引用消息、小程序、公众号等
            logger.info(f"[gewechat] 收到引用消息或小程序消息: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 系统消息（如撤回消息、拍一拍等）
            if "拍了拍我" in gewechat_msg.content:
                logger.info(f"[gewechat] 收到拍一拍消息: {gewechat_msg.content}")
            else:
                logger.info(f"[gewechat] 收到系统消息: {gewechat_msg.content[:50]}")
        elif msg_type == 10000:  # 群聊通知（如修改群名、更换群主等）
            logger.info(f"[gewechat] 收到群聊通知: {gewechat_msg.content[:50]}")
        elif msg_type == 37:  # 好友添加请求通知
            logger.info(f"[gewechat] 收到好友添加请求: {gewechat_msg.content[:50]}")
        elif msg_type == 42:  # 名片消息
            logger.info(f"[gewechat] 收到名片消息: {gewechat_msg.content[:50]}")
        elif msg_type == 43:  # 视频消息
            logger.info(f"[gewechat] 收到视频消息: {gewechat_msg.content[:50]}")
        elif msg_type == 47:  # 表情消息
            logger.info(f"[gewechat] 收到表情消息: {gewechat_msg.content[:50]}")
        elif msg_type == 48:  # 地理位置消息
            logger.info(f"[gewechat] 收到地理位置消息: {gewechat_msg.content[:50]}")
        elif msg_type == 51:  # 视频号消息
            logger.info(f"[gewechat] 收到视频号消息: {gewechat_msg.content[:50]}")
        elif msg_type == 2000:  # 转账消息
            logger.info(f"[gewechat] 收到转账消息: {gewechat_msg.content[:50]}")
        elif msg_type == 2001:  # 红包消息
            logger.info(f"[gewechat] 收到红包消息: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 撤回消息
            logger.info(f"[gewechat] 收到撤回消息: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 拍一拍消息
            logger.info(f"[gewechat] 收到拍一拍消息: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 群公告
            logger.info(f"[gewechat] 收到群公告: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 群待办
            logger.info(f"[gewechat] 收到群待办: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 踢出群聊通知
            logger.info(f"[gewechat] 收到踢出群聊通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 解散群聊通知
            logger.info(f"[gewechat] 收到解散群聊通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 修改群名称
            logger.info(f"[gewechat] 收到修改群名称通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 更换群主通知
            logger.info(f"[gewechat] 收到更换群主通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 群信息变更通知
            logger.info(f"[gewechat] 收到群信息变更通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 删除好友通知
            logger.info(f"[gewechat] 收到删除好友通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 退出群聊通知
            logger.info(f"[gewechat] 收到退出群聊通知: {gewechat_msg.content[:50]}")
        elif msg_type == 10002:  # 掉线通知
            logger.info(f"[gewechat] 收到掉线通知: {gewechat_msg.content[:50]}")
        else:
            logger.warning(f"[gewechat] 未知消息类型: {msg_type}, 内容: {gewechat_msg.content[:50]}")

         # 检查发送者是否在黑名单中
        # 获取黑名单和白名单
        nick_name_black_list = conf().get("nick_name_black_list", [])
        nick_name_white_list = conf().get("nick_name_white_list", [])

        # 获取发送者的信息
        sender_nickname = gewechat_msg.actual_user_nickname  # 发送者的昵称
        sender_id = gewechat_msg.actual_user_id  # 发送者的微信ID
        sender_wxid = gewechat_msg.from_user_id  # 发送者的微信号

        # 检查发送者是否在白名单中
        is_in_white_list = (
            sender_nickname in nick_name_white_list
            or sender_id in nick_name_white_list
            or sender_wxid in nick_name_white_list
        )

        # 如果发送者在白名单中，直接放行
        if is_in_white_list:
            logger.debug(f"[gewechat] 白名单用户放行: {sender_nickname} (ID: {sender_id}, 微信号: {sender_wxid})")
            context = channel._compose_context(
                gewechat_msg.ctype,
                gewechat_msg.content,
                isgroup=gewechat_msg.is_group,
                msg=gewechat_msg,
            )
            if context:
                channel.produce(context)
            return "success"

        # 检查是否所有用户都被列入黑名单
        if "ALL_USER" in nick_name_black_list:
            logger.debug(f"[gewechat] 所有用户被列入黑名单，忽略消息: {sender_nickname} (ID: {sender_id}, 微信号: {sender_wxid})")
            return "success"

        # 检查发送者是否在黑名单中
        is_in_black_list = (
            sender_nickname in nick_name_black_list
            or sender_id in nick_name_black_list
            or sender_wxid in nick_name_black_list
        )

        # 如果发送者在黑名单中，忽略消息
        if is_in_black_list:
            logger.debug(f"[gewechat] 忽略来自黑名单用户的消息: {sender_nickname} (ID: {sender_id}, 微信号: {sender_wxid})")
            return "success"

        # 如果发送者不在黑名单中，处理消息
        context = channel._compose_context(
            gewechat_msg.ctype,
            gewechat_msg.content,
            isgroup=gewechat_msg.is_group,
            msg=gewechat_msg,
        )
        if context:
            channel.produce(context)
        return "success"
