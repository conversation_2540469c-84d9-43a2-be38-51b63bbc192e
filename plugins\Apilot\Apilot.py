import random
import plugins
import requests
import re
import json
import io
from urllib.parse import urlparse
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from channel import channel
from common.log import logger
from plugins import *
from datetime import datetime, timedelta
import time
import os
from requests_html import HTMLSession

BASE_URL_VVHAN = "https://api.vvhan.com/api/"
BASE_URL_ALAPI = "https://v3.alapi.cn/api/"
BASE_URL_XLB = "https://api.ios.lat/api/"

@plugins.register(
    name="Apilot",
    desire_priority=88,
    hidden=False,
    desc="A plugin to handle specific keywords",
    version="1.3",
    author="sofs2005",
)
class Apilot(Plugin):
    def __init__(self):
        super().__init__()
        try:
            self.conf = super().load_config()
            self.condition_2_and_3_cities = None  # 天气查询，存储重复城市信息，Initially set to None
            if not self.conf:
                logger.warn("[Apilot] inited but alapi_token not found in config")
                self.alapi_token = None # Setting a default value for alapi_token
                self.morning_news_text_enabled = False
            else:
                logger.info("[Apilot] inited and alapi_token loaded successfully")
                self.alapi_token = self.conf["alapi_token"]
                try:
                    self.morning_news_text_enabled = self.conf["morning_news_text_enabled"]
                except:
                    self.morning_news_text_enabled = False
            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
        except Exception as e:
            raise self.handle_error(e, "[Apiot] init failed, ignore ")

    def on_handle_context(self, e_context: EventContext):
        if e_context["context"].type not in [
            ContextType.TEXT
        ]:
            return
        content = e_context["context"].content.strip()
        logger.debug("[Apilot] on_handle_context. content: %s" % content)

        # 添加网易新闻处理逻辑
        news_match = re.match(r'^(.*?)新闻$', content)
        if news_match or content == "新闻":
            news_type = news_match.group(1) if news_match and news_match.group(1) else "综合"
            news_content = self.get_netease_news(self.alapi_token, news_type)
            reply = self.create_reply(ReplyType.TEXT, news_content)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "早报":
            news = self.get_morning_news(self.alapi_token, self.morning_news_text_enabled)
            reply_type = ReplyType.IMAGE if isinstance(news, io.BytesIO) else ReplyType.TEXT
            reply = self.create_reply(reply_type, news)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return
        if content == "摸鱼":
            moyu = self.get_moyu_calendar()
            reply_type = ReplyType.IMAGE_URL if self.is_valid_url(moyu) else ReplyType.TEXT
            reply = self.create_reply(reply_type, moyu)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "摸鱼视频":
            moyu = self.get_moyu_calendar_video()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(moyu) else ReplyType.TEXT
            reply = self.create_reply(reply_type, moyu)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "八卦":
            bagua = self.get_mx_bagua()
            reply_type = ReplyType.IMAGE_URL if self.is_valid_url(bagua) else ReplyType.TEXT
            reply = self.create_reply(reply_type, bagua)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "白丝图片":
            bstp = self.get_mx_bstp()
            reply_type = ReplyType.IMAGE_URL if self.is_valid_url(bstp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, bstp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "黑丝图片":
            hstp = self.get_mx_hstp()
            reply_type = ReplyType.IMAGE_URL if self.is_valid_url(hstp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, hstp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "玉足视频":
            yzsp = self.get_yzsp()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(yzsp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, yzsp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "黑丝视频":
            hssp = self.get_hssp()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(hssp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, hssp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "cos视频":
            cos = self.get_cos()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(cos) else ReplyType.TEXT
            reply = self.create_reply(reply_type, cos)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "吊带视频":
            ddsp = self.get_ddsp()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(ddsp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, ddsp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "JK视频":
            jksp = self.get_jksp()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(jksp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, jksp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "萝莉视频":
            llsp = self.get_llsp()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(llsp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, llsp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "小姐姐视频":
            xjjsp = self.get_xjjsp()
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(xjjsp) else ReplyType.TEXT
            reply = self.create_reply(reply_type, xjjsp)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        if content == "毒鸡汤":
            dujitang = self.get_soul_dujijtang(self.alapi_token)
            reply = self.create_reply( ReplyType.TEXT, dujitang)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS #事件结束，并跳过处理context的默认逻辑
            return

        # 新增功能
        if content == "祖安语录":
            zuanyulu = self.get_zuanyulu()
            reply = self.create_reply(ReplyType.TEXT, zuanyulu)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "疯狂星期四" or content == "KFC":
            kfc = self.get_kfc()
            reply = self.create_reply(ReplyType.TEXT, kfc)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "搞笑语录":
            gaoxiao = self.get_gaoxiao()
            reply = self.create_reply(ReplyType.TEXT, gaoxiao)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "安慰语录":
            comfort = self.get_comfort()
            reply = self.create_reply(ReplyType.TEXT, comfort)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "名人名言":
            mingyan = self.get_mingyan()
            reply = self.create_reply(ReplyType.TEXT, mingyan)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "朋友圈文案":
            pengyou = self.get_pengyou()
            reply = self.create_reply(ReplyType.TEXT, pengyou)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "60秒读懂世界":
            news_60s = self.get_60s_news()
            reply = self.create_reply(ReplyType.TEXT, news_60s)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        if content == "电影票房":
            piaofang = self.get_movie_box_office()
            reply = self.create_reply(ReplyType.TEXT, piaofang)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # AI角色预设
        ai_role_match = re.match(r'^AI角色(\d+)$', content)
        if ai_role_match:
            role_id = ai_role_match.group(1)
            ai_role = self.get_ai_role(role_id)
            reply = self.create_reply(ReplyType.TEXT, ai_role)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 小人举牌
        if content.startswith("举牌"):
            text = content[2:].strip()
            if text:
                jupai = self.get_jupai(text)
                reply_type = ReplyType.IMAGE_URL if self.is_valid_url(jupai) else ReplyType.TEXT
                reply = self.create_reply(reply_type, jupai)
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                return

        # 天气简报
        weather_brief_match = re.match(r'^(.+)天气简报$', content)
        if weather_brief_match:
            city = weather_brief_match.group(1)
            weather_brief = self.get_weather_brief(city)
            reply = self.create_reply(ReplyType.TEXT, weather_brief)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 生活指数
        life_index_match = re.match(r'^(.+)生活指数$', content)
        if life_index_match:
            city = life_index_match.group(1)
            life_index = self.get_life_index(city)
            reply = self.create_reply(ReplyType.TEXT, life_index)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 一周天气
        week_weather_match = re.match(r'^(.+)一周天气$', content)
        if week_weather_match:
            city = week_weather_match.group(1)
            week_weather = self.get_week_weather(city)
            reply = self.create_reply(ReplyType.TEXT, week_weather)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 音乐搜索
        music_match = re.match(r'^搜索音乐\s+(.+)$', content)
        if music_match:
            song_name = music_match.group(1)
            music_result = self.get_music_search(song_name)
            reply = self.create_reply(ReplyType.TEXT, music_result)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 王者荣耀战力查询
        wzry_match = re.match(r'^王者战力\s+(.+)$', content)
        if wzry_match:
            hero_name = wzry_match.group(1)
            wzry_result = self.get_wzry_power(hero_name)
            reply = self.create_reply(ReplyType.TEXT, wzry_result)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 奥运会排行榜
        if content == "奥运会排行榜":
            olympic = self.get_olympic_ranking()
            reply = self.create_reply(ReplyType.TEXT, olympic)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 口吐芬芳系列
        ridicule_types = {
            "滚刀": "1",
            "殴打": "2",
            "散扣": "3",
            "嘲讽": "4",
            "口吐": "5"
        }
        if content in ridicule_types:
            ridicule = self.get_ridicule(ridicule_types[content])
            reply = self.create_reply(ReplyType.TEXT, ridicule)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 图片功能
        image_functions = {
            "摸鱼日报": "moyuribao",
            "情感花园": "qingganhuayuan",
            "微语简报": "weiyujianbao",
            "内涵段子": "neihanduanzi",
            "摸鱼人日历": "moyu",
            "小姐姐图片": "xjjimg",
            "bing每日一图": "bing",
            "二次元动漫": "dm",
            "高清壁纸": "gqbz",
            "MC动漫": "mcdm",
            "360壁纸": "360bz",
            "黑丝图片": "heisi",
            "白丝图片": "baisi",
            "精选网红图片": "jxwhimg",
            "精选明星图片": "jxmximg",
            "精选风景图片": "jxfjimg",
            "精选游戏图片": "jxyximg",
            "精选动物图片": "jxdwimg",
            "精选热舞图片": "jxrwimg",
            "微视封面": "weishiimg",
            "ACG图片": "ACG"
        }
        if content in image_functions:
            image_result = self.get_image_content(image_functions[content], content)
            reply_type = ReplyType.IMAGE_URL if self.is_valid_url(image_result) else ReplyType.TEXT
            reply = self.create_reply(reply_type, image_result)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 壁纸系列
        wallpaper_match = re.match(r'^(.+)壁纸$', content)
        if wallpaper_match:
            wallpaper_type = wallpaper_match.group(1)
            wallpaper_types = {
                "美女": "1", "动漫": "2", "风景": "3", "游戏": "4", "文字": "5",
                "视觉": "6", "情感": "7", "设计": "8", "明星": "9", "物语": "10",
                "男人": "11", "机械": "12", "城市": "13", "动物": "14"
            }
            if wallpaper_type in wallpaper_types:
                wallpaper = self.get_wallpaper(wallpaper_types[wallpaper_type], wallpaper_type)
                reply_type = ReplyType.IMAGE_URL if self.is_valid_url(wallpaper) else ReplyType.TEXT
                reply = self.create_reply(reply_type, wallpaper)
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                return

        # 视频功能
        video_functions = {
            "小姐姐视频": "xjjvideo",
            "小哥哥视频": "xggvideo",
            "懒洋洋翻唱": "lyy",
            "小新翻唱": "xiaoxin",
            "你的欲梦": "ndym",
            "双倍快乐": "sbkl",
            "快手变装": "ksbianzhuang",
            "玉足美腿": "yuzu",
            "甜妹系列": "tianmei",
            "JK洛丽塔": "jksp",
            "热舞系列": "rewu",
            "萝莉系列": "luoli",
            "漫画芋": "manhuayu",
            "蛇姐系列": "shejie",
            "汉服系列": "hanfu",
            "狱卒系列": "jpmt",
            "慢摇系列": "manyao",
            "掉带系列": "diaodai",
            "清纯系列": "qingchun",
            "COS系列": "COS",
            "纯情女高": "nvgao",
            "街拍系列": "jiepai",
            "动漫视频": "dmsp",
            "精选网红视频": "jxwh",
            "精选明星视频": "jxmx",
            "精选风景视频": "jxfj",
            "精选游戏视频": "jxyx",
            "精选动物视频": "jxdw",
            "精选热舞视频": "jxrw",
            "微视短视频": "weishivideo",
            "Barbin视频": "barbin"
        }
        if content in video_functions:
            video_result = self.get_video_content(video_functions[content], content)
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(video_result) else ReplyType.TEXT
            reply = self.create_reply(reply_type, video_result)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        # 美拍视频 (带参数)
        meipai_match = re.match(r'^美拍(.*)$', content)
        if meipai_match:
            video_type = meipai_match.group(1).strip() or "热门"
            meipai_result = self.get_meipai_video(video_type)
            reply_type = ReplyType.VIDEO_URL if self.is_valid_url(meipai_result) else ReplyType.TEXT
            reply = self.create_reply(reply_type, meipai_result)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS
            return

        history_match = re.match(r"^历史上的今天(\d+)月(\d+)日", content)
        if content == "历史上的今天" or history_match:
            month, day = '', ''
            if history_match:
                month, day = history_match.group(1), history_match.group(2)
            history_event = self.get_today_on_history(self.alapi_token, month, day)
            reply = self.create_reply(ReplyType.TEXT, history_event)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS # 事件结束，并跳过处理context的默认逻辑

        if content == '舔狗':
            dog_diary = self.get_dog_diary(self.alapi_token)
            reply = self.create_reply(ReplyType.TEXT, dog_diary)
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS # 事件结束， 并跳过处理context的默认逻辑

        if content == '一言':
            hitokoto = self.get_hitokoto(self.alapi_token)
            reply = self.create_reply(ReplyType.TEXT, hitokoto)
            e_context['reply'] = reply
            e_context.action = EventAction.BREAK_PASS #事件结束，并跳过处理context默认逻辑

        horoscope_match = re.match(r'^([\u4e00-\u9fa5]{2}座)$', content)
        if horoscope_match:
            if content in ZODIAC_MAPPING:
                zodiac_english = ZODIAC_MAPPING[content]
                content = self.get_horoscope(self.alapi_token, zodiac_english)
                reply = self.create_reply(ReplyType.TEXT, content)
            else:
                reply = self.create_reply(ReplyType.TEXT, "请重新输入星座名称")
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

        hot_trend_match = re.search(r'(.{1,6})热榜$', content)
        if hot_trend_match:
            hot_trends_type = hot_trend_match.group(1).strip()  # 提取匹配的组并去掉可能的空格
            content = self.get_hot_trends(hot_trends_type)
            reply = self.create_reply(ReplyType.TEXT, content)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return


        # 天气查询
        weather_match = re.match(r'^(?:(.{2,7}?)(?:市|县|区|镇)?|(\d{7,9}))(:?今天|明天|后天|7天|七天)?(?:的)?天气$', content)
        if weather_match:
            # 如果匹配成功，提取第一个捕获组
            city_or_id = weather_match.group(1) or weather_match.group(2)
            date = weather_match.group(3)
            if not self.alapi_token:
                self.handle_error("alapi_token not configured", "天气请求失败")
                reply = self.create_reply(ReplyType.TEXT, "请先配置alapi的token")
            else:
                content = self.get_weather(self.alapi_token, city_or_id, date, content)
                reply = self.create_reply(ReplyType.TEXT, content)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
            return

    def get_help_text(self, verbose=False, **kwargs):
        short_help_text = " 发送特定指令以获取早报、热榜、查询天气、星座运势等！"

        if not verbose:
            return short_help_text

        help_text = "📚 发送关键词获取特定信息！\n"

        # 娱乐和信息类
        help_text += "\n🎉 娱乐与资讯：\n"
        help_text += '  🌅 早报: 发送"早报"获取早报(图片或文字)。\n'
        help_text += '  🐟 摸鱼: 发送"摸鱼"获取摸鱼人日历图片。\n'
        help_text += '  🎬 摸鱼视频: 发送"摸鱼视频"获取摸鱼日历视频。\n'
        help_text += '  🔥 热榜: 发送"xx热榜"查看支持的热榜(如微博热榜)。\n'
        help_text += '  🌟 八卦: 发送"八卦"获取明星八卦图片。\n'
        help_text += '  📰 新闻: 发送"新闻"获取综合新闻，或"xx新闻"获取特定类型新闻(如娱乐新闻)。\n'
        help_text += '  ☠️ 毒鸡汤: 发送"毒鸡汤"获取心灵毒鸡汤。\n'
        help_text += '  📜 历史上的今天: 发送"历史上的今天"或"历史上的今天x月x日"获取历史事件\n'
        help_text += '  🐕 舔狗日记: 发送"舔狗"获取舔狗日记\n'
        help_text += '  ✨ 一言: 发送"一言"获取Hitokoto一言\n'

        # 新增功能
        help_text += "\n🆕 新增功能：\n"
        help_text += '  🔥 祖安语录: 发送"祖安语录"获取祖安语录(仅供娱乐)\n'
        help_text += '  🍗 疯狂星期四: 发送"疯狂星期四"或"KFC"获取KFC文案\n'
        help_text += '  😂 搞笑语录: 发送"搞笑语录"获取搞笑内容\n'
        help_text += '  🤗 安慰语录: 发送"安慰语录"获取安慰话语\n'
        help_text += '  📚 名人名言: 发送"名人名言"获取励志名言\n'
        help_text += '  📱 朋友圈文案: 发送"朋友圈文案"获取文案素材\n'
        help_text += '  📰 60秒读懂世界: 发送"60秒读懂世界"获取每日新闻\n'
        help_text += '  🎬 电影票房: 发送"电影票房"获取票房排行榜\n'
        help_text += '  🤖 AI角色: 发送"AI角色1"到"AI角色134"获取角色预设\n'
        help_text += '  📝 小人举牌: 发送"举牌+文字"生成举牌图片\n'

        # 天气功能增强
        help_text += "\n🌤️ 天气功能增强：\n"
        help_text += '  🌤️ 天气简报: 发送"城市名+天气简报"(如"北京天气简报")\n'
        help_text += '  🏠 生活指数: 发送"城市名+生活指数"(如"上海生活指数")\n'
        help_text += '  📅 一周天气: 发送"城市名+一周天气"(如"广州一周天气")\n'

        # 娱乐工具
        help_text += "\n🎮 娱乐工具：\n"
        help_text += '  🎵 音乐搜索: 发送"搜索音乐 歌曲名"搜索音乐\n'
        help_text += '  ⚔️ 王者战力: 发送"王者战力 英雄名"查询战力\n'
        help_text += '  🏅 奥运排行: 发送"奥运会排行榜"查看奥运排行\n'
        help_text += '  💢 口吐芬芳: 发送"滚刀/殴打/散扣/嘲讽/口吐"(仅供娱乐)\n'

        # 图片功能
        help_text += "\n📸 图片功能：\n"
        help_text += '  📊 摸鱼日报: 发送"摸鱼日报"获取摸鱼日报图片\n'
        help_text += '  💝 情感花园: 发送"情感花园"获取情感图片\n'
        help_text += '  📰 微语简报: 发送"微语简报"获取微语简报\n'
        help_text += '  😄 内涵段子: 发送"内涵段子"获取内涵段子图片\n'
        help_text += '  👧 小姐姐图片: 发送"小姐姐图片"获取随机图片\n'
        help_text += '  🌅 bing每日一图: 发送"bing每日一图"获取必应壁纸\n'
        help_text += '  🎨 二次元动漫: 发送"二次元动漫"获取动漫图片\n'
        help_text += '  🖼️ 高清壁纸: 发送"高清壁纸"获取高清壁纸\n'
        help_text += '  🎮 MC动漫: 发送"MC动漫"获取MC动漫图片\n'
        help_text += '  🌐 360壁纸: 发送"360壁纸"获取360壁纸\n'
        help_text += '  🖤 黑丝图片: 发送"黑丝图片"获取黑丝图片\n'
        help_text += '  🤍 白丝图片: 发送"白丝图片"获取白丝图片\n'
        help_text += '  📱 微视封面: 发送"微视封面"获取微视短视频封面\n'
        help_text += '  🎭 ACG图片: 发送"ACG图片"获取随机ACG图片\n'
        help_text += '  ✨ 精选系列: 发送"精选网红图片/精选明星图片/精选风景图片/精选游戏图片/精选动物图片/精选热舞图片"\n'
        help_text += '  🖼️ 壁纸系列: 发送"类型+壁纸"(如"美女壁纸"、"风景壁纸")\n'
        help_text += '      支持：美女/动漫/风景/游戏/文字/视觉/情感/设计/明星/物语/男人/机械/城市/动物\n'

        # 视频功能
        help_text += "\n🎬 视频功能：\n"
        help_text += '  👧 小姐姐视频: 发送"小姐姐视频"获取小姐姐视频\n'
        help_text += '  👦 小哥哥视频: 发送"小哥哥视频"获取小哥哥视频\n'
        help_text += '  🎵 翻唱系列: 发送"懒洋洋翻唱"或"小新翻唱"获取翻唱视频\n'
        help_text += '  🌙 你的欲梦: 发送"你的欲梦"获取相关视频\n'
        help_text += '  😄 双倍快乐: 发送"双倍快乐"获取搞笑视频\n'
        help_text += '  💄 变装系列: 发送"快手变装"获取变装视频\n'
        help_text += '  👠 玉足美腿: 发送"玉足美腿"获取相关视频\n'
        help_text += '  😊 甜妹系列: 发送"甜妹系列"获取甜妹视频\n'
        help_text += '  👘 JK洛丽塔: 发送"JK洛丽塔"获取JK视频\n'
        help_text += '  💃 热舞系列: 发送"热舞系列"获取热舞视频\n'
        help_text += '  🎀 萝莉系列: 发送"萝莉系列"获取萝莉视频\n'
        help_text += '  📚 漫画芋: 发送"漫画芋"获取漫画相关视频\n'
        help_text += '  🐍 蛇姐系列: 发送"蛇姐系列"获取蛇姐视频\n'
        help_text += '  👘 汉服系列: 发送"汉服系列"获取汉服视频\n'
        help_text += '  🔒 狱卒系列: 发送"狱卒系列"获取狱卒视频\n'
        help_text += '  🎶 慢摇系列: 发送"慢摇系列"获取慢摇视频\n'
        help_text += '  👗 掉带系列: 发送"掉带系列"获取掉带视频\n'
        help_text += '  😇 清纯系列: 发送"清纯系列"获取清纯视频\n'
        help_text += '  🎭 COS系列: 发送"COS系列"获取COS视频\n'
        help_text += '  🎓 纯情女高: 发送"纯情女高"获取女高视频\n'
        help_text += '  📷 街拍系列: 发送"街拍系列"获取街拍视频\n'
        help_text += '  🎬 动漫视频: 发送"动漫视频"获取动漫视频\n'
        help_text += '  ✨ 精选视频: 发送"精选网红视频/精选明星视频/精选风景视频/精选游戏视频/精选动物视频/精选热舞视频"\n'
        help_text += '  📱 微视短视频: 发送"微视短视频"获取微视短视频\n'
        help_text += '  🎪 Barbin视频: 发送"Barbin视频"获取Barbin视频\n'
        help_text += '  📹 美拍视频: 发送"美拍+类型"(如"美拍热门"、"美拍舞蹈")\n'
        help_text += '      支持：热门/直播/搞笑/爱豆/高颜值/舞蹈/音乐/美食/美妆/萌宠/旅行/吃秀/穿秀/运动/手工/游戏\n'

        # 图片和视频类
        help_text += "\n🎬 图片与视频：\n"
        help_text += '  📸 白丝图片: 发送"白丝图片"获取相关图片\n'
        help_text += '  📸 黑丝图片: 发送"黑丝图片"获取相关图片\n'
        help_text += '  🎥 玉足视频: 发送"玉足视频"获取相关视频\n'
        help_text += '  🎥 黑丝视频: 发送"黑丝视频"获取相关视频\n'
        help_text += '  🎥 cos视频: 发送"cos视频"获取cosplay视频\n'
        help_text += '  🎥 吊带视频: 发送"吊带视频"获取相关视频\n'
        help_text += '  🎥 JK视频: 发送"JK视频"获取JK制服视频\n'
        help_text += '  🎥 萝莉视频: 发送"萝莉视频"获取相关视频\n'
        help_text += '  🎥 小姐姐视频: 发送"小姐姐视频"获取相关视频\n'

        # 查询类
        help_text += "\n🔍 查询工具：\n"
        help_text += '  🌦️ 天气: 发送"城市+天气"查天气(如"北京天气")，支持"今天/明天/后天/7天"查询\n'
        help_text += '  🌌 星座: 发送星座名称查看今日运势(如"白羊座")\n'

        return help_text

    def get_hitokoto(self, alapi_token):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "yan"
        try:
            hitokoto_data = self.make_request(url, method="GET")
            if isinstance(hitokoto_data, dict) and hitokoto_data.get("code") == 200:
                data = hitokoto_data["data"]
                format_data = (
                    f"【✨ 一言 ✨】\n"
                    f"🎆 {data}\n"
                    f"🌟 来源：XLB API"
                )
                return format_data
            else:
                # 备用ALAPI
                return self._get_hitokoto_backup(alapi_token)
        except Exception as e:
            return self._get_hitokoto_backup(alapi_token)

    def _get_hitokoto_backup(self, alapi_token):
        if not alapi_token:
            return "一言获取失败，请稍后再试~"

        url = BASE_URL_ALAPI + "hitokoto"
        hitokoto_type = 'abcdefghijkl'
        random_type = random.randint(0, len(hitokoto_type) - 1)
        payload = {
            "token": alapi_token,
            "type": hitokoto_type[random_type]
        }
        headers = {"Content-Type": "application/json"}
        try:
            hitokoto_data = self.make_request(url, method="POST", headers=headers, json_data=payload)
            if isinstance(hitokoto_data, dict) and hitokoto_data.get("code") == 200:
                data = hitokoto_data["data"]
                # 需要定义hitokoto_type_dict
                type_names = {
                    'a': '动画', 'b': '漫画', 'c': '游戏', 'd': '文学', 'e': '原创',
                    'f': '来自网络', 'g': '其他', 'h': '影视', 'i': '诗词', 'j': '网易云',
                    'k': '哲学', 'l': '抖机灵'
                }
                type_name = type_names.get(hitokoto_type[random_type], '未知')
                format_data = (
                    f"【✨ 一言 ✨】\n"
                    f"🎆 {data['hitokoto']}\n"
                    f"🎐 类型: {type_name}\n"
                    f"🥷 作者: {data['from']}"
                )
                return format_data
            else:
                return self.handle_error(hitokoto_data, "出错啦，稍后再试")
        except Exception as e:
            return self.handle_error(e, "出错啦，稍后再试~")

    def get_dog_diary(self, alapi_token):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "dog"
        try:
            dog_diary_data = self.make_request(url, method='GET')
            if isinstance(dog_diary_data, dict) and dog_diary_data.get('code') == 200:
                data = dog_diary_data['data']
                format_output = (
                    "【（づ￣3￣）づ╭❤️～舔狗日记】\n"
                    f"🐶 {data}"
                )
                return format_output
            else:
                # 备用ALAPI
                return self._get_dog_diary_backup(alapi_token)
        except Exception as e:
            return self._get_dog_diary_backup(alapi_token)

    def _get_dog_diary_backup(self, alapi_token):
        if not alapi_token:
            return "舔狗日记获取失败，请稍后再试~"

        url = BASE_URL_ALAPI + "dog"
        payload = {
            "token": alapi_token,
            "format": 'json'
        }
        headers = {"Content-Type": "application/json"}
        try:
            dog_diary_data = self.make_request(url, method='POST', headers=headers, json_data=payload)
            if isinstance(dog_diary_data, dict) and dog_diary_data.get('code') == 200:
                data = dog_diary_data['data']['content']
                format_output = (
                    "【（づ￣3￣）づ╭❤️～舔狗日记】\n"
                    f"🐶 {data}"
                )
                return format_output
            else:
                return self.handle_error(dog_diary_data, "出错啦，稍后再试~")
        except Exception as e:
            return self.handle_error(e, "出错啦，稍后再试~")

    def get_today_on_history(self, alapi_token, month = "", day = ""):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "lssdjt"
        try:
            history_event_data = self.make_request(url, method="GET")
            if isinstance(history_event_data, dict) and history_event_data.get('code') == 200:
                data = history_event_data['data']
                result = (
                    f"📆【历史上的今天】📆\n"
                    f"📅 {data}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                # 备用ALAPI
                return self._get_history_backup(alapi_token, month, day)
        except Exception:
            return self._get_history_backup(alapi_token, month, day)

    def _get_history_backup(self, alapi_token, month="", day=""):
        if not alapi_token:
            return "历史上的今天获取失败，请稍后再试~"

        url = BASE_URL_ALAPI + "eventHistory"
        payload = {
            "token": alapi_token,
            "month": month,
            "day": day
        }
        headers = {"Content-Type": "application/json"}
        try:
            history_event_data = self.make_request(url, method="POST", headers=headers, json_data=payload)
            if isinstance(history_event_data, dict) and history_event_data.get('code') == 200:
                current_date = ""
                if month and day:
                    current_date = f"{month}月{day}日"
                else:
                    today = datetime.now()
                    current_date = today.strftime("%m月%d日")

                format_output = [f"【📆 历史上的今天 {current_date} 📆】\n"]
                data = history_event_data['data']
                history_count = len(data)

                # 随机选择历史事件
                output_count = random.randint(6, 10)  # 随机选择6-10条事件
                selected_indices = set()

                # 设置消息长度限制
                total_length = len(format_output[0])
                message_limit = 2000  # 设置消息长度限制（微信单条消息大约2000字左右）

                # 随机选择并添加事件，直到达到数量或长度限制
                attempt_count = 0
                while len(selected_indices) < min(output_count, history_count) and attempt_count < 50:
                    attempt_count += 1
                    idx = random.randint(0, history_count - 1)
                    if idx in selected_indices:
                        continue

                    event = data[idx]
                    # 提取年份显示为单独的标签
                    year = event['date'].split('年')[0] if '年' in event['date'] else ""
                    year_display = f"📅 {year}" if year else ""

                    # 截断过长的描述
                    desc = event['desc']
                    if len(desc) > 60:  # 缩短描述长度
                        desc = desc[:57] + "..."

                    # 使用更美观的emoji和格式
                    history = (
                        f"🔹 事件 {len(selected_indices) + 1}: {event['title']}\n"
                        f"   {year_display}  📍 {event['date']}\n"
                        f"   📝 {desc}\n"
                    )

                    # 检查添加当前事件后消息是否会超出长度限制
                    if total_length + len(history) + 50 > message_limit:  # 预留50字符给提示信息
                        break

                    selected_indices.add(idx)
                    format_output.append(history)
                    total_length += len(history)

                # 添加有多少事件未显示的提示
                if history_count > len(selected_indices):
                    remaining = history_count - len(selected_indices)
                    format_output.append(f"\n还有 {remaining} 条历史事件未显示")

                format_output.append("\n💡 发送\"历史上的今天X月X日\"可查询特定日期")
                return "\n".join(format_output)

            else:
                return self.handle_error(history_event_data, "出错啦，稍后再试~")

        except Exception as e:
            return self.handle_error(e, "出错啦，稍后再试~")

    def get_soul_dujijtang(self, alapi_token):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "dujitang"
        try:
            soul_data = self.make_request(url, method="GET")
            if isinstance(soul_data, dict) and soul_data.get('code') == 200:
                data = soul_data['data']
                result = (
                    f"💡【今日心灵毒鸡汤】\n"
                    f"🥃 {data}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                # 备用ALAPI
                return self._get_soul_backup(alapi_token)
        except Exception as e:
            return self._get_soul_backup(alapi_token)

    def _get_soul_backup(self, alapi_token):
        if not alapi_token:
            return "毒鸡汤获取失败，请稍后再试~"

        url = BASE_URL_ALAPI + "soul"
        payload = {"token": alapi_token}
        headers = {'Content-Type': "application/json"}
        try:
            soul_data = self.make_request(url, method="POST", headers=headers, json_data=payload)
            if isinstance(soul_data, dict) and soul_data.get('code') == 200:
                data = soul_data['data']['content']
                result = (
                    f"💡【今日心灵毒鸡汤】\n"
                    f"🥃 {data}"
                )
                return result
            else:
                return self.handle_error(soul_data, "心灵毒鸡汤获取失败，请检查 token 是否有误")
        except Exception as e:
            return self.handle_error(e, "出错啦，稍后再试")


    def get_morning_news(self, alapi_token, morning_news_text_enabled):
        # 优先使用新的XLB API
        if morning_news_text_enabled:
            # 文字版使用60秒读懂世界文字API
            return self.get_60s_news()
        else:
            # 图片版使用60秒读懂世界图片API
            url = BASE_URL_XLB + "60s"
            try:
                data = self.make_request(url, method="GET")
                if isinstance(data, dict) and data.get('code') == 200:
                    # 如果返回的是图片URL
                    if 'data' in data and self.is_valid_url(str(data['data'])):
                        return self.download_image(data['data'])
                    else:
                        return self._get_morning_news_backup(alapi_token, morning_news_text_enabled)
                else:
                    return self._get_morning_news_backup(alapi_token, morning_news_text_enabled)
            except Exception:
                return self._get_morning_news_backup(alapi_token, morning_news_text_enabled)

    def _get_morning_news_backup(self, alapi_token, morning_news_text_enabled):
        if not alapi_token:
            url = "https://api.03c3.cn/api/zb"  # 修改为更稳定的API
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "application/json"
            }
            try:
                morning_news_info = self.make_request(url, method="GET", headers=headers)
                if isinstance(morning_news_info, dict) and morning_news_info.get('code') == 200:
                    if morning_news_text_enabled:
                        # 提取并格式化新闻
                        news_list = morning_news_info.get('data', {}).get('news', [])
                        if news_list:
                            formatted_news = f"☕ 今日早报\n\n"
                            for idx, news in enumerate(news_list, 1):
                                formatted_news += f"{idx}. {news}\n"
                            return f"{formatted_news}\n图片链接：{morning_news_info.get('data', {}).get('imageurl', '')}"
                    else:
                        # 下载图片而不是返回URL
                        image_url = morning_news_info.get('data', {}).get('imageurl')
                        if image_url:
                            return self.download_image(image_url)
                return self.handle_error(morning_news_info, '早报信息获取失败，可配置"alapi token"切换至 Alapi 服务，或者稍后再试')
            except Exception as e:
                return self.handle_error(e, "出错啦，稍后再试")
        else:
            url = BASE_URL_ALAPI + "zaobao"
            data = {
                "token": alapi_token,
                "format": "json"
            }
            headers = {'Content-Type': "application/x-www-form-urlencoded"}
            try:
                morning_news_info = self.make_request(url, method="POST", headers=headers, data=data)
                if isinstance(morning_news_info, dict) and morning_news_info.get('code') == 200:
                    img_url = morning_news_info['data']['image']
                    if morning_news_text_enabled:
                        news_list = morning_news_info['data']['news']
                        weiyu = morning_news_info['data']['weiyu']

                        # 整理新闻为有序列表
                        formatted_news = f"☕ {morning_news_info['data']['date']}  今日早报\n"
                        formatted_news = formatted_news + "\n".join(news_list)
                        # 组合新闻和微语
                        return f"{formatted_news}\n\n{weiyu}\n\n 图片url：{img_url}"
                    else:
                        # 下载图片而不是返回URL
                        return self.download_image(img_url)
                else:
                    return self.handle_error(morning_news_info, "早报获取失败，请检查 token 是否有误")
            except Exception as e:
                return self.handle_error(e, "早报获取失败")

    def download_image(self, image_url):
        """使用requests-html模拟浏览器下载图片"""
        try:
            # 首先需要安装requests-html库
            # pip install requests-html
            from requests_html import HTMLSession
            import random
            import time
            from urllib.parse import urlparse

            # 创建会话
            session = HTMLSession()

            # 多种User-Agent随机选择
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0"
            ]

            # 解析URL获取域名
            parsed_url = urlparse(image_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            # 先访问首页获取cookies
            logger.info(f"[早报] 先访问主域名: {base_url}")
            headers = {
                "User-Agent": random.choice(user_agents),
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }
            session.get(base_url, headers=headers)

            # 随机延迟模拟人类行为
            time.sleep(random.uniform(1, 2))

            # 访问图片URL
            logger.info(f"[早报] 下载图片: {image_url}")
            headers = {
                "User-Agent": random.choice(user_agents),
                "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Referer": base_url,
                "Connection": "keep-alive",
                "Sec-Fetch-Dest": "image",
                "Sec-Fetch-Mode": "no-cors",
                "Sec-Fetch-Site": "same-origin",
                "Pragma": "no-cache",
                "Cache-Control": "no-cache"
            }

            response = session.get(image_url, headers=headers, timeout=15)

            if response.status_code == 200:
                img_io = io.BytesIO(response.content)
                img_io.seek(0)
                logger.info(f"[早报] 图片下载成功: {len(response.content)/1024:.2f} KB")
                return img_io
            else:
                logger.error(f"[早报] 请求失败，状态码: {response.status_code}")
                return self._try_backup_apis(image_url)

        except Exception as e:
            logger.error(f"[早报] 模拟浏览器下载失败: {e}")
            return self._try_backup_apis(image_url)

    def _try_backup_apis(self, original_url=None):
        """尝试从备用API获取早报图片"""
        try:
            logger.info("尝试使用备用API获取早报图片")

            # 备用API列表
            backup_apis = [
                "https://api.03c3.cn/api/zb",
                "https://api.vvhan.com/api/60s",
                "https://api.pearktrue.cn/api/60s/image"
            ]

            for api_url in backup_apis:
                try:
                    logger.info(f"尝试从备用API获取: {api_url}")

                    headers = {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"
                    }

                    # 判断API是否返回JSON数据
                    if "api/zb" in api_url or "api/60s" in api_url:
                        response = requests.get(api_url, headers=headers, timeout=10)
                        response.raise_for_status()

                        # 检查是否返回JSON数据
                        if response.headers.get('Content-Type', '').startswith('application/json'):
                            data = response.json()
                            if "api/zb" in api_url and 'data' in data and 'imageurl' in data['data']:
                                img_url = data['data']['imageurl']
                                img_response = requests.get(img_url, headers=headers, timeout=10)
                                img_response.raise_for_status()
                                img_io = io.BytesIO(img_response.content)
                                img_io.seek(0)
                                logger.info(f"成功从备用API {api_url} 获取早报图片")
                                return img_io
                            elif "api/60s" in api_url and 'imgUrl' in data:
                                img_url = data['imgUrl']
                                img_response = requests.get(img_url, headers=headers, timeout=10)
                                img_response.raise_for_status()
                                img_io = io.BytesIO(img_response.content)
                                img_io.seek(0)
                                logger.info(f"成功从备用API {api_url} 获取早报图片")
                                return img_io
                        # 如果是直接返回图片
                        else:
                            img_io = io.BytesIO(response.content)
                            img_io.seek(0)
                            logger.info(f"成功从备用API {api_url} 获取早报图片")
                            return img_io
                    # 直接返回图片的API
                    else:
                        response = requests.get(api_url, headers=headers, timeout=10)
                        response.raise_for_status()
                        img_io = io.BytesIO(response.content)
                        img_io.seek(0)
                        logger.info(f"成功从备用API {api_url} 获取早报图片")
                        return img_io

                except Exception as e:
                    logger.warning(f"从备用API {api_url} 获取早报图片失败: {e}")
                    continue

            # 如果所有备用API都失败
            logger.error("所有备用API均获取失败")
            return self.handle_error("所有图片来源均获取失败", "下载图片失败，请稍后再试")
        except Exception as e:
            logger.error(f"尝试备用API失败: {e}")
            return self.handle_error("尝试备用API失败", "下载图片失败，请稍后再试")

    def get_moyu_calendar(self):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "moyu"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是图片URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return self._get_moyu_backup()
            else:
                return self._get_moyu_backup()
        except Exception:
            return self._get_moyu_backup()

    def _get_moyu_backup(self):
        url = BASE_URL_VVHAN + "moyu?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        moyu_calendar_info = self.make_request(url, method="POST", headers=headers, data=payload)
        # 验证请求是否成功
        if isinstance(moyu_calendar_info, dict) and moyu_calendar_info['success']:
            return moyu_calendar_info['url']
        else:
            url = "https://dayu.qqsuu.cn/moyuribao/apis.php?type=json"
            payload = "format=json"
            headers = {'Content-Type': "application/x-www-form-urlencoded"}
            moyu_calendar_info = self.make_request(url, method="POST", headers=headers, data=payload)
            if isinstance(moyu_calendar_info, dict) and moyu_calendar_info['code'] == 200:
                moyu_pic_url = moyu_calendar_info['data']
                if self.is_valid_image_url(moyu_pic_url):
                    return moyu_pic_url
                else:
                    return "周末无需摸鱼，愉快玩耍吧"
            else:
                return '暂无可用"摸鱼"服务，认真上班'

    def get_moyu_calendar_video(self):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "moyushipin"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是视频URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return self._get_moyu_video_backup()
            else:
                return self._get_moyu_video_backup()
        except Exception:
            return self._get_moyu_video_backup()

    def _get_moyu_video_backup(self):
        url = "https://dayu.qqsuu.cn/moyuribaoshipin/apis.php?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        moyu_calendar_info = self.make_request(url, method="POST", headers=headers, data=payload)
        logger.debug(f"[Apilot] moyu calendar video response: {moyu_calendar_info}")
        # 验证请求是否成功
        if isinstance(moyu_calendar_info, dict) and moyu_calendar_info['code'] == 200:
            moyu_video_url = moyu_calendar_info['data']
            if self.is_valid_image_url(moyu_video_url):
                return moyu_video_url

        # 未成功请求到视频时，返回提示信息
        return "视频版没了，看看文字版吧"

    def get_horoscope(self, alapi_token, astro_sign: str, time_period: str = "today"):
        # 优先使用新的XLB API (图片版星座运势)
        url = BASE_URL_XLB + "xingzuoyunshi"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是图片URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return self._get_horoscope_backup(alapi_token, astro_sign, time_period)
            else:
                return self._get_horoscope_backup(alapi_token, astro_sign, time_period)
        except Exception:
            return self._get_horoscope_backup(alapi_token, astro_sign, time_period)

    def _get_horoscope_backup(self, alapi_token, astro_sign: str, time_period: str = "today"):
        if not alapi_token:
            url = BASE_URL_VVHAN + "horoscope"
            params = {
                'type': astro_sign,
                'time': time_period
            }
            try:
                horoscope_data = self.make_request(url, "GET", params=params)
                if isinstance(horoscope_data, dict) and horoscope_data['success']:
                    data = horoscope_data['data']

                    result = (
                        f"{data['title']} ({data['time']}):\n\n"
                        f"💡【每日建议】\n宜：{data['todo']['yi']}\n忌：{data['todo']['ji']}\n\n"
                        f"📊【运势指数】\n"
                        f"总运势：{data['index']['all']}\n"
                        f"爱情：{data['index']['love']}\n"
                        f"工作：{data['index']['work']}\n"
                        f"财运：{data['index']['money']}\n"
                        f"健康：{data['index']['health']}\n\n"
                        f"🍀【幸运提示】\n数字：{data['luckynumber']}\n"
                        f"颜色：{data['luckycolor']}\n"
                        f"星座：{data['luckyconstellation']}\n\n"
                        f"✍【简评】\n{data['shortcomment']}\n\n"
                        f"📜【详细运势】\n"
                        f"总运：{data['fortunetext']['all']}\n"
                        f"爱情：{data['fortunetext']['love']}\n"
                        f"工作：{data['fortunetext']['work']}\n"
                        f"财运：{data['fortunetext']['money']}\n"
                        f"健康：{data['fortunetext']['health']}\n"
                    )

                    return result

                else:
                    return self.handle_error(horoscope_data, '星座信息获取失败，可配置"alapi token"切换至 Alapi 服务，或者稍后再试')

            except Exception as e:
                return self.handle_error(e, "出错啦，稍后再试")
        else:
            # 使用 ALAPI 的 URL 和提供的 token
            url = BASE_URL_ALAPI + "star"
            payload = f"token={alapi_token}&star={astro_sign}"
            headers = {'Content-Type': "application/x-www-form-urlencoded"}
            try:
                horoscope_data = self.make_request(url, method="POST", headers=headers, data=payload)
                if isinstance(horoscope_data, dict) and horoscope_data.get('code') == 200:
                    data = horoscope_data['data']['day']

                    # 格式化并返回 ALAPI 提供的星座信息
                    result = (
                        f"📅 日期：{data['date']}\n\n"
                        f"💡【每日建议】\n宜：{data['yi']}\n忌：{data['ji']}\n\n"
                        f"📊【运势指数】\n"
                        f"总运势：{data['all']}\n"
                        f"爱情：{data['love']}\n"
                        f"工作：{data['work']}\n"
                        f"财运：{data['money']}\n"
                        f"健康：{data['health']}\n\n"
                        f"🔔【提醒】：{data['notice']}\n\n"
                        f"🍀【幸运提示】\n数字：{data['lucky_number']}\n"
                        f"颜色：{data['lucky_color']}\n"
                        f"星座：{data['lucky_star']}\n\n"
                        f"✍【简评】\n总运：{data['all_text']}\n"
                        f"爱情：{data['love_text']}\n"
                        f"工作：{data['work_text']}\n"
                        f"财运：{data['money_text']}\n"
                        f"健康：{data['health_text']}\n"
                    )
                    return result
                else:
                    return self.handle_error(horoscope_data, "星座获取信息获取失败，请检查 token 是否有误")
            except Exception as e:
                return self.handle_error(e, "出错啦，稍后再试")

    def get_hot_trends(self, hot_trends_type):
        # 使用新的XLB API
        new_hot_trend_apis = {
            "微博": "weibors",
            "知乎": "zhihurs",
            "哔哩哔哩": "bilirs",
            "36氪": "36kers",
            "百度": "baidurs",
            "虎扑": "hupurs",
            "抖音": "douyinrs",
            "豆瓣": "doubanrs",
            "少数派": "sspairs",
            "IT资讯": "itInfors",
            "IT最新": "itNewsrs",
            "贴吧": "tiebars"
        }

        api_endpoint = new_hot_trend_apis.get(hot_trends_type, None)
        if api_endpoint is not None:
            url = BASE_URL_XLB + api_endpoint
            try:
                data = self.make_request(url, "GET")
                if isinstance(data, dict) and data.get('code') == 200:
                    content = data['data']
                    result = (
                        f"🔥【{hot_trends_type}热榜】🔥\n"
                        f"📊 {content}\n"
                        f"🌟 来源：XLB API"
                    )
                    return result
                else:
                    return self._get_hot_trends_backup(hot_trends_type)
            except Exception:
                return self._get_hot_trends_backup(hot_trends_type)
        else:
            supported_types = "/".join(new_hot_trend_apis.keys())
            final_output = (
                f"👉 已支持的类型有：\n\n    {supported_types}\n"
                f"\n📝 请按照以下格式发送：\n    类型+热榜  例如：微博热榜"
            )
            return final_output

    def _get_hot_trends_backup(self, hot_trends_type):
        # 备用原有API
        hot_trends_type_en = hot_trend_types.get(hot_trends_type, None)
        if hot_trends_type_en is not None:
            url = BASE_URL_VVHAN + "hotlist/" + hot_trends_type_en
            try:
                data = self.make_request(url, "GET", {
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                })
                if isinstance(data, dict) and data['success'] == True:
                    output = []
                    topics = data['data']
                    output.append(f'更新时间：{data["update_time"]}\n')
                    for i, topic in enumerate(topics[:15], 1):
                        hot = topic.get('hot', '无热度参数, 0')
                        formatted_str = f"{i}. {topic['title']} ({hot} 浏览)\nURL: {topic['url']}\n"
                        output.append(formatted_str)
                    return "\n".join(output)
                else:
                    return self.handle_error(data, "热榜获取失败，请稍后再试")
            except Exception as e:
                return self.handle_error(e, "出错啦，稍后再试")
        return "热榜获取失败，请稍后再试"

    def get_weather(self, alapi_token, city_or_id: str, date: str, content):
        url = BASE_URL_ALAPI + 'tianqi'
        isFuture = date in ['明天', '后天', '七天', '7天']
        if isFuture:
            url = BASE_URL_ALAPI + 'tianqi/seven'
        # 判断使用id还是city请求api
        if city_or_id.isnumeric():  # 判断是否为纯数字，也即是否为 city_id
            params = {
                'city_id': city_or_id,
                'token': f'{alapi_token}'
            }
        else:
            city_info = self.check_multiple_city_ids(city_or_id)
            if city_info:
                data = city_info['data']
                formatted_city_info = "\n".join(
                    [f"{idx + 1}) {entry['province']}--{entry['leader']}, ID: {entry['city_id']}"
                     for idx, entry in enumerate(data)]
                )
                return f'查询 <{city_or_id}> 具有多条数据：\n{formatted_city_info}\n请使用id查询，发送"id天气"'

            params = {
                'city': city_or_id,
                'token': f'{alapi_token}'
            }
        try:
            weather_data = self.make_request(url, "GET", params=params)
            if isinstance(weather_data, dict) and weather_data.get('code') == 200:
                data = weather_data['data']
                if isFuture:
                    formatted_output = []
                    for num, d in enumerate(data):
                        if num == 0:
                            formatted_output.append(f"🏙️ 城市: {d['city']} ({d['province']})\n")
                        if date == '明天' and num != 1:
                            continue
                        if date == '后天' and num != 2:
                            continue
                        basic_info = [
                            f"🕒 日期: {d['date']}",
                            f"🌞 天气: 🌞{d['wea_day']}| 🌛{d['wea_night']}",
                            f"🌡️ 温度: 🌞{d['temp_day']}℃| 🌛{d['temp_night']}℃",
                            f"🌅 日出/日落: {d['sunrise']} / {d['sunset']}",
                        ]
                        for i in d['index']:
                            basic_info.append(f"{i['name']}: {i['level']}")
                        formatted_output.append("\n".join(basic_info) + '\n')
                    return "\n".join(formatted_output)
                update_time = data['update_time']
                dt_object = datetime.strptime(update_time, "%Y-%m-%d %H:%M:%S")
                formatted_update_time = dt_object.strftime("%m-%d %H:%M")
                # Basic Info
                if not city_or_id.isnumeric() and data['city'] not in content:  # 如果返回城市信息不是所查询的城市，重新输入
                    return "输入不规范，请输<国内城市+(今天|明天|后天|七天|7天)+天气>，比如 '广州天气'"
                formatted_output = []
                # 重新组织和分类天气信息
                # 1. 基本位置和时间信息
                location_info = (
                    f"🏙️ 城市: {data['city']} ({data['province']})\n"
                    f"🕒 更新: {formatted_update_time}\n"
                )

                # 2. 天气状况信息
                weather_info = (
                    f"🌦️ 天气: {data['weather']}\n"
                    f"🌡️ 温度: ↓{data['min_temp']}℃| 现{data['temp']}℃| ↑{data['max_temp']}℃\n"
                )

                # 3. 风力信息
                wind_info = (
                    f"🌬️ 风向: {data['wind']} | 风速: {data.get('wind_speed', 'N/A')} | 风力: {data.get('wind_power', 'N/A')}\n"
                )

                # 4. 环境指标分开显示
                humidity_info = f"💦 湿度: {data['humidity']}"
                visibility_info = f"👁️ 能见度: {data.get('visibility', 'N/A')}"
                pressure_info = f"🔄 气压: {data.get('pressure', 'N/A')}"

                environment_info = f"{humidity_info} | {visibility_info} | {pressure_info}\n"

                # 5. 太阳信息
                sun_info = f"🌅 日出/日落: {data['sunrise']} / {data['sunset']}\n"

                # 组合所有信息
                formatted_output.append(location_info + weather_info + wind_info + environment_info + sun_info)

                # 空气质量信息
                if data.get('aqi'):
                    aqi_data = data['aqi']
                    air_level = aqi_data.get('air_level', '')
                    level_emoji = '🟢'  # 默认良好
                    if '优' in air_level:
                        level_emoji = '🟢'  # 绿色表示优
                    elif '良' in air_level:
                        level_emoji = '🔵'  # 蓝色表示良
                    elif '轻度' in air_level:
                        level_emoji = '🟡'  # 黄色表示轻度污染
                    elif '中度' in air_level:
                        level_emoji = '🟠'  # 橙色表示中度污染
                    elif '重度' in air_level:
                        level_emoji = '🔴'  # 红色表示重度污染
                    elif '严重' in air_level:
                        level_emoji = '🟣'  # 紫色表示严重污染

                    aqi_info = "💨 空气质量： \n"
                    aqi_info += (
                        f"{level_emoji} 质量指数: {aqi_data.get('air', 'N/A')} ({aqi_data.get('air_level', 'N/A')})\n"
                        f"😷 PM2.5: {aqi_data.get('pm25', 'N/A')} | PM10: {aqi_data.get('pm10', 'N/A')}\n"
                        f"⚗️ CO: {aqi_data.get('co', 'N/A')} | NO₂: {aqi_data.get('no2', 'N/A')} | SO₂: {aqi_data.get('so2', 'N/A')} | O₃: {aqi_data.get('o3', 'N/A')}\n"
                        f"💡 提示: {aqi_data.get('air_tips', 'N/A')}\n"
                    )
                    formatted_output.append(aqi_info)

                # 天气指标 Weather indicators
                weather_indicators = data.get('index')
                if weather_indicators:
                    indicators_info = '⚠️ 天气指标： \n'
                    for weather_indicator in weather_indicators:
                        # 根据指标类型选择合适的emoji
                        indicator_type = weather_indicator['type']
                        indicator_emoji = "🔍"  # 默认emoji

                        if "diaoyu" in indicator_type:
                            indicator_emoji = "🎣"  # 钓鱼指数
                        elif "ganmao" in indicator_type:
                            indicator_emoji = "🤧"  # 感冒指数
                        elif "guoming" in indicator_type or "allergy" in indicator_type:
                            indicator_emoji = "😷"  # 过敏指数
                        elif "xiche" in indicator_type:
                            indicator_emoji = "🚗"  # 洗车指数
                        elif "yundong" in indicator_type:
                            indicator_emoji = "🏃"  # 运动指数
                        elif "ziwanxian" in indicator_type or "uv" in indicator_type:
                            indicator_emoji = "☀️"  # 紫外线指数
                        elif "chuanyi" in indicator_type:
                            indicator_emoji = "👕"  # 穿衣指数
                        elif "lvyou" in indicator_type:
                            indicator_emoji = "🏖️"  # 旅游指数
                        elif "daisan" in indicator_type:
                            indicator_emoji = "☂️"  # 带伞指数

                        # 根据指标等级选择颜色emoji
                        level = weather_indicator['level']
                        level_emoji = "⚪"  # 默认白色

                        # 根据指标类型选择特定的判断逻辑
                        if "ziwanxian" in indicator_type or "uv" in indicator_type:  # 紫外线指数
                            if any(keyword in level for keyword in ["弱", "最弱"]):
                                level_emoji = "🟢"  # 绿色表示弱
                            elif "中等" in level:
                                level_emoji = "🟡"  # 黄色表示中等
                            elif "强" in level and "很强" not in level and "极强" not in level:
                                level_emoji = "🟠"  # 橙色表示强
                            elif "很强" in level:
                                level_emoji = "🔴"  # 红色表示很强
                            elif "极强" in level:
                                level_emoji = "🟣"  # 紫色表示极强
                        elif "ganmao" in indicator_type:  # 感冒指数
                            if any(keyword in level for keyword in ["少发", "不易发"]):
                                level_emoji = "🟢"  # 绿色表示少发
                            elif "较易发" in level:
                                level_emoji = "🟡"  # 黄色表示较易发
                            elif "易发" in level:
                                level_emoji = "🔴"  # 红色表示易发
                        elif "xiche" in indicator_type:  # 洗车指数
                            if "适宜" in level and "不" not in level and "较" not in level:
                                level_emoji = "🟢"  # 绿色表示适宜
                            elif "较适宜" in level:
                                level_emoji = "🟡"  # 黄色表示较适宜
                            elif "不适宜" in level:
                                level_emoji = "🔴"  # 红色表示不适宜
                        elif "yundong" in indicator_type:  # 运动指数
                            if "适宜" in level and "不" not in level and "较" not in level:
                                level_emoji = "🟢"  # 绿色表示适宜
                            elif "较适宜" in level:
                                level_emoji = "🟡"  # 黄色表示较适宜
                            elif "不适宜" in level:
                                level_emoji = "🔴"  # 红色表示不适宜
                        elif "chuanyi" in indicator_type:  # 穿衣指数
                            if any(keyword in level for keyword in ["炎热", "短袖"]):
                                level_emoji = "🔴"  # 红色表示炎热
                            elif any(keyword in level for keyword in ["舒适", "薄外套"]):
                                level_emoji = "🟢"  # 绿色表示舒适
                            elif any(keyword in level for keyword in ["较冷", "毛衣", "夹克"]):
                                level_emoji = "🟡"  # 黄色表示较冷
                            elif any(keyword in level for keyword in ["寒冷", "棉衣", "羽绒服"]):
                                level_emoji = "🔵"  # 蓝色表示寒冷
                        elif "lvyou" in indicator_type:  # 旅游指数
                            if "非常适宜" in level:
                                level_emoji = "🟢"  # 绿色表示非常适宜
                            elif "适宜" in level and "不" not in level:
                                level_emoji = "🔵"  # 蓝色表示适宜
                            elif "一般" in level:
                                level_emoji = "🟡"  # 黄色表示一般
                            elif "不适宜" in level:
                                level_emoji = "🔴"  # 红色表示不适宜
                        elif "diaoyu" in indicator_type:  # 钓鱼指数
                            if "适宜" in level and "不" not in level and "较" not in level:
                                level_emoji = "🟢"  # 绿色表示适宜
                            elif "较适宜" in level:
                                level_emoji = "🟡"  # 黄色表示较适宜
                            elif "不适宜" in level:
                                level_emoji = "🔴"  # 红色表示不适宜
                        elif "guoming" in indicator_type or "allergy" in indicator_type:  # 过敏指数
                            if any(keyword in level for keyword in ["不易过敏", "1级"]):
                                level_emoji = "🟢"  # 绿色表示1级不易过敏
                            elif any(keyword in level for keyword in ["过敏少发", "2级"]):
                                level_emoji = "🔵"  # 蓝色表示2级过敏少发
                            elif any(keyword in level for keyword in ["较易过敏", "3级"]):
                                level_emoji = "🟡"  # 黄色表示3级较易过敏
                            elif any(keyword in level for keyword in ["易过敏", "4级"]):
                                level_emoji = "🟠"  # 橙色表示4级易过敏
                            elif any(keyword in level for keyword in ["极易过敏", "5级"]):
                                level_emoji = "🔴"  # 红色表示5级极易过敏
                            # 兼容旧版本格式
                            elif "低" in level:
                                level_emoji = "🟢"  # 绿色表示低
                            elif "中" in level:
                                level_emoji = "🟡"  # 黄色表示中
                            elif "高" in level:
                                level_emoji = "🔴"  # 红色表示高
                        else:  # 通用判断逻辑
                            if any(keyword in level for keyword in ["适宜", "良好", "最弱", "不需要", "不易", "舒适"]):
                                level_emoji = "🟢"  # 绿色表示良好
                            elif any(keyword in level for keyword in ["较适宜", "中等", "弱", "偏高", "一般"]):
                                level_emoji = "🟡"  # 黄色表示中等
                            elif any(keyword in level for keyword in ["较不宜", "较强", "少量"]):
                                level_emoji = "🟠"  # 橙色表示较差
                            elif any(keyword in level for keyword in ["不宜", "很强", "不建议", "高发", "易发", "极强", "不适宜"]):
                                level_emoji = "🔴"  # 红色表示不佳

                        # 合并到一行显示
                        indicators_info += f"{indicator_emoji} {weather_indicator['name']} {level_emoji} {level}：{weather_indicator['content'][:60]}{'...' if len(weather_indicator['content']) > 60 else ''}\n\n"

                    formatted_output.append(indicators_info)


                # Next 7 hours weather
                ten_hours_later = dt_object + timedelta(hours=10)

                future_weather = []
                for hour_data in data['hour']:
                    forecast_time_str = hour_data['time']
                    forecast_time = datetime.strptime(forecast_time_str, "%Y-%m-%d %H:%M:%S")

                    if dt_object < forecast_time <= ten_hours_later:
                        future_weather.append(f"     {forecast_time.hour:02d}:00 - {hour_data['wea']} - {hour_data['temp']}°C")

                future_weather_info = "⏳ 未来10小时的天气预报:\n" + "\n".join(future_weather)
                formatted_output.append(future_weather_info)

                # Alarm Info
                if data.get('alarm'):
                    alarm_info = "⚠️ 预警信息:\n"
                    for alarm in data['alarm']:
                        alarm_info += (
                            f"🔴 标题: {alarm['title']}\n"
                            f"🟠 等级: {alarm['level']}\n"
                            f"🟡 类型: {alarm['type']}\n"
                            f"🟢 提示: {alarm['tips']}\n"
                            f"🔵 内容: {alarm['content']}\n\n"
                        )
                    formatted_output.append(alarm_info)

                return "\n".join(formatted_output)
            else:
                return self.handle_error(weather_data, "获取失败，请查看服务器log")

        except Exception as e:
            return self.handle_error(e, "获取天气信息失败")

    def get_mx_bagua(self):
        # 优先使用新的XLB API
        url = BASE_URL_XLB + "mingxingbagua"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是图片URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return self._get_bagua_backup()
            else:
                return self._get_bagua_backup()
        except Exception:
            return self._get_bagua_backup()

    def _get_bagua_backup(self):
        url = "https://dayu.qqsuu.cn/mingxingbagua/apis.php?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        bagua_info = self.make_request(url, method="POST", headers=headers, data=payload)
        # 验证请求是否成功
        if isinstance(bagua_info, dict) and bagua_info['code'] == 200:
            bagua_pic_url = bagua_info["data"]
            if self.is_valid_image_url(bagua_pic_url):
                return bagua_pic_url
            else:
                return "周末不更新，请微博吃瓜"
        else:
            logger.error(f"错误信息：{bagua_info}")
            return "暂无明星八卦，吃瓜莫急"

    def make_request(self, url, method="GET", headers=None, params=None, data=None, json_data=None):
        try:
            if method.upper() == "GET":
                response = requests.request(method, url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = requests.request(method, url, headers=headers, data=data, json=json_data)
            else:
                return {"success": False, "message": "Unsupported HTTP method"}

            return response.json()
        except Exception as e:
            return e


    def create_reply(self, reply_type, content):
        reply = Reply()
        reply.type = reply_type
        reply.content = content
        return reply

    def handle_error(self, error, message):
        logger.error(f"{message}，错误信息：{error}")
        return message

    def is_valid_url(self, url):
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except ValueError:
            return False

    def is_valid_image_url(self, url):
        try:
            response = requests.head(url)  # Using HEAD request to check the URL header
            # If the response status code is 200, the URL exists and is reachable.
            return response.status_code == 200
        except requests.RequestException as e:
            # If there's an exception such as a timeout, connection error, etc., the URL is not valid.
            return False

    def load_city_conditions(self):
        if self.condition_2_and_3_cities is None:
            try:
                json_file_path = os.path.join(os.path.dirname(__file__), 'duplicate-citys.json')
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    self.condition_2_and_3_cities = json.load(f)
            except Exception as e:
                return self.handle_error(e, "加载condition_2_and_3_cities.json失败")


    def check_multiple_city_ids(self, city):
        self.load_city_conditions()
        city_info = self.condition_2_and_3_cities.get(city, None)
        if city_info:
            return city_info
        return None

    def get_yzsp(self):
        url = "https://api.xlb.one/api/jpmt?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        yzsp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(yzsp_info, dict) and yzsp_info['code'] == 200:
            yzsp_url = yzsp_info['data']
            if self.is_valid_image_url(yzsp_url):
                return yzsp_url
        return "获取视频失败，请稍后再试"

    def get_hssp(self):
        url = "https://api.yujn.cn/api/heisis.php?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        hssp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(hssp_info, dict) and hssp_info['code'] == 200:
            hssp_url = hssp_info['data']
            if self.is_valid_image_url(hssp_url):
                return hssp_url
        return "获取视频失败，请稍后再试"

    def get_cos(self):
        url = "https://api.xlb.one/api/COS?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        cos_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(cos_info, dict) and cos_info['code'] == 200:
            cos_url = cos_info['data']
            if self.is_valid_image_url(cos_url):
                return cos_url
        return "获取视频失败，请稍后再试"

    def get_ddsp(self):
        url = "https://api.xlb.one/api/diaodai?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        ddsp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(ddsp_info, dict) and ddsp_info['code'] == 200:
            ddsp_url = ddsp_info['data']
            if self.is_valid_image_url(ddsp_url):
                return ddsp_url
        return "获取视频失败，请稍后再试"

    def get_jksp(self):
        url = "https://api.xlb.one/api/jksp?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        jksp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(jksp_info, dict) and jksp_info['code'] == 200:
            jksp_url = jksp_info['data']
            if self.is_valid_image_url(jksp_url):
                return jksp_url
        return "获取视频失败，请稍后再试"

    def get_llsp(self):
        url = "https://api.xlb.one/api/luoli?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        llsp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(llsp_info, dict) and llsp_info['code'] == 200:
            llsp_url = llsp_info['data']
            if self.is_valid_image_url(llsp_url):
                return llsp_url
        return "获取视频失败，请稍后再试"

    def get_xjjsp(self):
        url = "https://api.yujn.cn/api/zzxjj.php?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        xjjsp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        if isinstance(xjjsp_info, dict) and xjjsp_info['code'] == 200:
            xjjsp_url = xjjsp_info['data']
            if self.is_valid_image_url(xjjsp_url):
                return xjjsp_url
        return "获取视频失败，请稍后再试"

    def get_mx_bstp(self):
        url = "https://api.xlb.one/api/baisi?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        bstp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        # 验证请求是否成功
        if isinstance(bstp_info, dict) and bstp_info['code'] == 200:
            bstp_pic_url = bstp_info['image']
            if self.is_valid_image_url(bstp_pic_url):
                return bstp_pic_url
        logger.error(f"白丝图片获取失败，错误信息：{bstp_info}")
        return "获取图片失败，请稍后再试"

    def get_mx_hstp(self):
        url = "https://api.xlb.one/api/heisi?type=json"
        payload = "format=json"
        headers = {'Content-Type': "application/x-www-form-urlencoded"}
        hstp_info = self.make_request(url, method="POST", headers=headers, data=payload)
        # 验证请求是否成功
        if isinstance(hstp_info, dict) and hstp_info['code'] == 200:
            hstp_pic_url = hstp_info['image']
            if self.is_valid_image_url(hstp_pic_url):
                return hstp_pic_url
        logger.error(f"黑丝图片获取失败，错误信息：{hstp_info}")
        return "获取图片失败，请稍后再试"

    # 添加获取网易新闻的方法
    def get_netease_news(self, alapi_token, news_type="综合"):
        url = BASE_URL_ALAPI + "new/toutiao"

        # 根据新闻类型获取对应的type值
        type_value = NEWS_TYPE_MAPPING.get(news_type, '1')  # 默认为综合

        payload = {
            "token": alapi_token,
            "type": type_value,
            "page": 1,
            "limit": 10  # 限制返回10条新闻
        }

        headers = {"Content-Type": "application/json"}

        try:
            news_data = self.make_request(url, method="POST", headers=headers, json_data=payload)

            if isinstance(news_data, dict) and news_data.get('code') == 200:
                news_list = news_data['data']

                # 准备格式化输出
                now = datetime.now()
                formatted_time = now.strftime("%Y-%m-%d %H:%M")
                format_output = [f"📰 网易{news_type}新闻 ({formatted_time})\n"]

                # 添加新闻列表
                for idx, news in enumerate(news_list, 1):
                    # 格式化时间
                    news_time = news.get('time', '')
                    if news_time:
                        try:
                            dt = datetime.strptime(news_time, "%Y-%m-%d %H:%M:%S")
                            news_time = dt.strftime("%m-%d %H:%M")
                        except:
                            pass

                    # 优化输出格式
                    news_title = news.get('title', '无标题')
                    news_source = news.get('source', '未知来源')

                    news_item = (
                        f"📌 {idx}. {news_title}\n"
                        f"   📰 来源: {news_source}  ⏰ {news_time}\n"
                    )

                    # 如果有摘要，添加摘要，但要避免与标题重复
                    news_digest = news.get('digest', '')
                    if news_digest and len(news_digest) > 0:
                        # 检查摘要是否与标题相似或相同
                        if news_digest not in news_title and news_title not in news_digest:
                            # 限制摘要长度
                            if len(news_digest) > 50:
                                news_digest = news_digest[:47] + "..."
                            news_item += f"   💬 {news_digest}\n"

                    # 添加链接
                    news_url = news.get('m_url', '')
                    if news_url:
                        news_item += f"   🔗 {news_url}\n"

                    format_output.append(news_item)

                # 添加提示信息
                supported_types = "、".join(list(NEWS_TYPE_MAPPING.keys())[:10]) + "等"
                format_output.append(f"\n💡 发送\"XX新闻\"获取特定类型新闻，如：{supported_types}")

                return "\n".join(format_output)
            else:
                return self.handle_error(news_data, "新闻获取失败，请检查token是否有效")

        except Exception as e:
            return self.handle_error(e, "获取新闻失败，请稍后再试")

    # 新增功能实现方法
    def get_zuanyulu(self):
        """获取祖安语录"""
        url = BASE_URL_XLB + "zuanyulu"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🔥【祖安语录】🔥\n"
                    f"💀 {content}\n"
                    f"⚠️ 仅供娱乐，请文明用语"
                )
                return result
            else:
                return "祖安语录获取失败，请稍后再试~"
        except Exception:
            return "祖安语录获取失败，请稍后再试~"

    def get_kfc(self):
        """获取KFC疯狂星期四文案"""
        url = BASE_URL_XLB + "kfcyl"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🍗【疯狂星期四】🍗\n"
                    f"🎉 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "疯狂星期四文案获取失败，请稍后再试~"
        except Exception:
            return "疯狂星期四文案获取失败，请稍后再试~"

    def get_gaoxiao(self):
        """获取搞笑语录"""
        url = BASE_URL_XLB + "gaoxiao"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"😂【搞笑语录】😂\n"
                    f"🤣 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "搞笑语录获取失败，请稍后再试~"
        except Exception:
            return "搞笑语录获取失败，请稍后再试~"

    def get_comfort(self):
        """获取安慰语录"""
        url = BASE_URL_XLB + "Comforting"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🤗【安慰语录】🤗\n"
                    f"💝 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "安慰语录获取失败，请稍后再试~"
        except Exception:
            return "安慰语录获取失败，请稍后再试~"

    def get_mingyan(self):
        """获取名人名言"""
        url = BASE_URL_XLB + "mingrenmingyan"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"📚【名人名言】📚\n"
                    f"✨ {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "名人名言获取失败，请稍后再试~"
        except Exception:
            return "名人名言获取失败，请稍后再试~"

    def get_pengyou(self):
        """获取朋友圈文案"""
        url = BASE_URL_XLB + "pengyouquan"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"📱【朋友圈文案】📱\n"
                    f"💭 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "朋友圈文案获取失败，请稍后再试~"
        except Exception:
            return "朋友圈文案获取失败，请稍后再试~"

    def get_60s_news(self):
        """获取60秒读懂世界"""
        url = BASE_URL_XLB + "60miao"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"📰【60秒读懂世界】📰\n"
                    f"🌍 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "60秒读懂世界获取失败，请稍后再试~"
        except Exception:
            return "60秒读懂世界获取失败，请稍后再试~"

    def get_movie_box_office(self):
        """获取电影票房排行榜"""
        url = BASE_URL_XLB + "piaofang"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🎬【电影票房排行榜】🎬\n"
                    f"📊 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "电影票房排行榜获取失败，请稍后再试~"
        except Exception:
            return "电影票房排行榜获取失败，请稍后再试~"

    def get_ai_role(self, role_id):
        """获取AI角色预设"""
        url = BASE_URL_XLB + f"Aiyushe?type={role_id}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🤖【AI角色预设 #{role_id}】🤖\n"
                    f"🎭 {content}\n"
                    f"🌟 来源：XLB API\n"
                    f"💡 提示：角色序号1-134可选"
                )
                return result
            else:
                return f"AI角色预设{role_id}获取失败，请稍后再试~"
        except Exception:
            return f"AI角色预设{role_id}获取失败，请稍后再试~"

    def get_jupai(self, text):
        """获取小人举牌"""
        url = BASE_URL_XLB + f"zt?msg={text}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是图片URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return f"📝【小人举牌】📝\n内容：{text}\n🌟 来源：XLB API"
            else:
                return f"小人举牌生成失败，请稍后再试~"
        except Exception:
            return f"小人举牌生成失败，请稍后再试~"

    def get_weather_brief(self, city):
        """获取天气简报"""
        # 将中文城市名转换为拼音
        city_pinyin = self.get_city_pinyin(city)
        url = BASE_URL_XLB + f"tqjb?city={city_pinyin}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🌤️【{city}天气简报】🌤️\n"
                    f"📊 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return f"{city}天气简报获取失败，请稍后再试~"
        except Exception:
            return f"{city}天气简报获取失败，请稍后再试~"

    def get_life_index(self, city):
        """获取生活指数"""
        city_pinyin = self.get_city_pinyin(city)
        url = BASE_URL_XLB + f"shzs?city={city_pinyin}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🏠【{city}生活指数】🏠\n"
                    f"📋 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return f"{city}生活指数获取失败，请稍后再试~"
        except Exception:
            return f"{city}生活指数获取失败，请稍后再试~"

    def get_week_weather(self, city):
        """获取一周天气预报"""
        city_pinyin = self.get_city_pinyin(city)
        url = BASE_URL_XLB + f"yztianqi?city={city_pinyin}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"📅【{city}一周天气】📅\n"
                    f"🌦️ {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return f"{city}一周天气获取失败，请稍后再试~"
        except Exception:
            return f"{city}一周天气获取失败，请稍后再试~"

    def get_music_search(self, song_name):
        """搜索音乐"""
        url = BASE_URL_XLB + f"Music?port=网易&msg={song_name}&b=1"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🎵【音乐搜索】🎵\n"
                    f"🎶 搜索：{song_name}\n"
                    f"📀 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return f"音乐《{song_name}》搜索失败，请稍后再试~"
        except Exception:
            return f"音乐《{song_name}》搜索失败，请稍后再试~"

    def get_wzry_power(self, hero_name):
        """获取王者荣耀战力查询"""
        url = BASE_URL_XLB + f"zhanli?name={hero_name}&msg=1&type="
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"⚔️【王者荣耀战力】⚔️\n"
                    f"🏆 英雄：{hero_name}\n"
                    f"📊 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return f"英雄《{hero_name}》战力查询失败，请稍后再试~"
        except Exception:
            return f"英雄《{hero_name}》战力查询失败，请稍后再试~"

    def get_olympic_ranking(self):
        """获取奥运会排行榜"""
        url = BASE_URL_XLB + "aoyunhui"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                result = (
                    f"🏅【奥运会排行榜】🏅\n"
                    f"🥇 {content}\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "奥运会排行榜获取失败，请稍后再试~"
        except Exception:
            return "奥运会排行榜获取失败，请稍后再试~"

    def get_ridicule(self, msg_type):
        """获取口吐芬芳"""
        url = BASE_URL_XLB + f"Ridicule?msg={msg_type}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                content = data['data']
                type_names = {"1": "滚刀", "2": "殴打", "3": "散扣", "4": "嘲讽", "5": "口吐"}
                type_name = type_names.get(msg_type, "未知")
                result = (
                    f"💢【口吐芬芳-{type_name}】💢\n"
                    f"🗯️ {content}\n"
                    f"⚠️ 仅供娱乐，请文明用语\n"
                    f"🌟 来源：XLB API"
                )
                return result
            else:
                return "口吐芬芳获取失败，请稍后再试~"
        except Exception:
            return "口吐芬芳获取失败，请稍后再试~"

    def get_city_pinyin(self, city):
        """简单的城市名转拼音映射"""
        city_map = {
            "北京": "beijing", "上海": "shanghai", "广州": "guangzhou", "深圳": "shenzhen",
            "杭州": "hangzhou", "南京": "nanjing", "武汉": "wuhan", "成都": "chengdu",
            "重庆": "chongqing", "天津": "tianjin", "西安": "xian", "苏州": "suzhou",
            "长沙": "changsha", "沈阳": "shenyang", "青岛": "qingdao", "郑州": "zhengzhou",
            "大连": "dalian", "东莞": "dongguan", "宁波": "ningbo", "厦门": "xiamen"
        }
        return city_map.get(city, city.lower())

    def get_image_content(self, api_endpoint, content_name):
        """获取图片内容"""
        url = BASE_URL_XLB + api_endpoint
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是图片URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return f"📸【{content_name}】📸\n🌟 来源：XLB API"
            else:
                return f"{content_name}获取失败，请稍后再试~"
        except Exception:
            return f"{content_name}获取失败，请稍后再试~"

    def get_wallpaper(self, wallpaper_type, type_name):
        """获取壁纸"""
        url = BASE_URL_XLB + f"bizhi?msg={wallpaper_type}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是图片URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return f"🖼️【{type_name}壁纸】🖼️\n🌟 来源：XLB API"
            else:
                return f"{type_name}壁纸获取失败，请稍后再试~"
        except Exception:
            return f"{type_name}壁纸获取失败，请稍后再试~"

    def get_video_content(self, api_endpoint, content_name):
        """获取视频内容"""
        url = BASE_URL_XLB + api_endpoint
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是视频URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return f"🎬【{content_name}】🎬\n🌟 来源：XLB API"
            else:
                return f"{content_name}获取失败，请稍后再试~"
        except Exception:
            return f"{content_name}获取失败，请稍后再试~"

    def get_meipai_video(self, video_type):
        """获取美拍视频"""
        # 美拍视频类型映射
        meipai_types = {
            "热门": "热门", "直播": "直播", "搞笑": "搞笑", "爱豆": "爱豆",
            "高颜值": "高颜值", "舞蹈": "舞蹈", "音乐": "音乐", "美食": "美食",
            "美妆": "美妆", "萌宠": "萌宠", "旅行": "旅行", "吃秀": "吃秀",
            "穿秀": "穿秀", "运动": "运动", "手工": "手工", "游戏": "游戏"
        }

        # 如果类型不在映射中，使用热门
        if video_type not in meipai_types:
            video_type = "热门"

        url = BASE_URL_XLB + f"meipai?lx={video_type}"
        try:
            data = self.make_request(url, method="GET")
            if isinstance(data, dict) and data.get('code') == 200:
                # 如果返回的是视频URL
                if 'data' in data and self.is_valid_url(str(data['data'])):
                    return data['data']
                else:
                    return f"🎬【美拍{video_type}视频】🎬\n🌟 来源：XLB API"
            else:
                return f"美拍{video_type}视频获取失败，请稍后再试~"
        except Exception:
            return f"美拍{video_type}视频获取失败，请稍后再试~"



ZODIAC_MAPPING = {
        '白羊座': 'aries',
        '金牛座': 'taurus',
        '双子座': 'gemini',
        '巨蟹座': 'cancer',
        '狮子座': 'leo',
        '处女座': 'virgo',
        '天秤座': 'libra',
        '天蝎座': 'scorpio',
        '射手座': 'sagittarius',
        '摩羯座': 'capricorn',
        '水瓶座': 'aquarius',
        '双鱼座': 'pisces'
    }

hot_trend_types = {
    "微博": "wbHot",
    "虎扑": "huPu",
    "知乎": "zhihuHot",
    "知乎日报": "zhihuDay",
    "哔哩哔哩": "bili",
    "36氪": "36Ke",
    "抖音": "douyinHot",
    "IT": "itNews",
    "虎嗅": "huXiu",
    "产品经理": "woShiPm",
    "头条": "toutiao",
    "百度": "baiduRD",
    "豆瓣": "douban",
}

hitokoto_type_dict = {
    'a':'动画',
    'b':'漫画',
    'c':'游戏',
    'd':'文学',
    'e':'原创',
    'f':'来自网络',
    'g':'其他',
    'h':'影视',
    'i':'诗词',
    'j':'网易云',
    'k':'哲学',
    'l':'抖机灵'
}

NEWS_TYPE_MAPPING = {
    '综合': '1',
    '娱乐': '2',
    '体育': '3',
    '财经': '4',
    '科技': '5',
    '搞笑': '6',
    '游戏': '7',
    '读书': '8',
    '生活': '9',
    '直播': '10',
    '历史': '11',
    '国际': '12',
    '影视': '13',
    '国内足球': '14',
    '国际足球': '15',
    '篮球': '16',
    '跑步': '17',
    '手机': '18',
    '电脑': '19',
    '新能源': '20',
    '设计': '21',
    '地方': '22',
    '健康': '23',
    '酒文化': '24',
    '教育': '25',
    '育儿': '26',
    '女性': '27',
    '情感': '28',
    '官方': '29',
    '奇事': '30'
}

