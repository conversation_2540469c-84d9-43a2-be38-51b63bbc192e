# encoding:utf-8

import plugins
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from channel.chat_message import ChatMessage
from common.log import logger
from plugins import *
from config import conf, global_config

# 实现管理员判断工具类
class AdminUtil:
    @staticmethod
    def is_admin(msg):
        """
        判断消息是否由管理员用户发送
        :param msg: 消息对象
        :return: True: 是, False: 否
        """
        actual_user_id = msg.actual_user_id
        for admin_user in global_config["admin_users"]:
            if actual_user_id:
                if isinstance(admin_user, str):
                    if actual_user_id in admin_user:
                        return True
                else:
                    if actual_user_id == admin_user:
                        return True
        return False

@plugins.register(
    name="HelloPlus",
    desire_priority=1,
    hidden=True,
    desc="欢迎plus版",
    version="0.1",
    author="wangcl",
)


class HelloPlus(Plugin):

    group_welc_prompt = "请你随机使用一种风格说一句问候语来欢迎新用户\"{nickname}\"加入群聊。"
    group_exit_prompt = "请你随机使用一种风格介绍你自己，并告诉用户输入#help可以查看帮助信息。"
    patpat_prompt = "请你随机使用一种风格跟其他群用户说他违反规则\"{nickname}\"退出群聊。"
    exit_url="https://baike.baidu.com/item/%E9%80%80%E5%87%BA/28909"
    redirect_link = "https://baike.baidu.com/item/welcome/2135227"
    say_exit = "有缘再见"
    sleep_time = 60
    welc_text = False
    auth_token = "12345679"
    
    def __init__(self):
        super().__init__()
        try:
            self.config = super().load_config()
            if not self.config:
                self.config = self._load_config_template()
            self.group_welc_fixed_msg = self.config.get("group_welc_fixed_msg", {})
            self.group_welc_prompt = self.config.get("group_welc_prompt", self.group_welc_prompt)
            self.group_exit_prompt = self.config.get("group_exit_prompt", self.group_exit_prompt)
            self.group_exit_prompt_str="\"{nickname}\"退出群聊。请你随机使用一种风格跟他说再见。"
            self.patpat_prompt = self.config.get("patpat_prompt", self.patpat_prompt)
            self.redirect_link = self.config.get("redirect_link", self.redirect_link)
            self.exit_url = self.config.get("exit_url", self.exit_url)
            self.say_exit = self.config.get("say_exit", self.say_exit)
            self.sleep_time=self.config.get("sleep_time", self.sleep_time)
            self.auth_token=self.config.get("auth_token", self.auth_token)
            self.welc_text=self.config.get("welc_text", self.welc_text)
            self.appid = conf().get("gewechat_app_id", "")
            self.gewetk = conf().get("gewechat_token", "")
            self.base_url = conf().get("gewechat_base_url")
            self.group_members={}
            self.memberList = []
            self.monitor_threads = {}  # 存储监控线程
            self.monitoring_groups = set()  # 存储正在监控的群组ID
            self.monitoring_groups_name = {}  # 存储正在监控的群组name
            self.headers = {
                'X-GEWE-TOKEN': self.gewetk,
                'Content-Type': 'application/json'
            }
            logger.info("[HelloPlus] inited")
            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
        except Exception as e:
            print(f"[HelloPlus]初始化异常-----：{e}")
            logger.error(f"[HelloPlus]初始化异常：{e}")
            raise "[HelloPlus] init failed, ignore "

    def on_handle_context(self, e_context: EventContext):
        if e_context["context"].type not in [
            ContextType.TEXT,
            ContextType.JOIN_GROUP,
            ContextType.PATPAT,
            ContextType.EXIT_GROUP
        ]:
            return
        msg: ChatMessage = e_context["context"]["msg"]
        group_name = msg.from_user_nickname
        if e_context["context"].type == ContextType.JOIN_GROUP:
            if "group_welcome_msg" in conf() or group_name in self.group_welc_fixed_msg:
                
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    if group_name in self.group_welc_fixed_msg:
                        reply.content = self.group_welc_fixed_msg.get(group_name, "")
                    else:
                        reply.content = conf().get("group_welcome_msg", "")
                    e_context["reply"] = reply
                    e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
                    return
            print('----welcome----')
            try:
                import time
                time.sleep(2)
                qm, imgurl, nickName = self.get_info(msg.other_user_id, msg.actual_user_nickname)
                
                # 检查是否有足够信息发送欢迎卡片
                card_sent = False
                if imgurl is not None:  # 只要有头像URL就尝试发送卡片
                    ret = self.welcome(msg, qm, imgurl)
                    if ret == 200:
                        card_sent = True
                        logger.info(f"[HelloPlus] 成功发送欢迎卡片给用户: {msg.actual_user_nickname}")
                        # 如果不需要额外发送文本欢迎且卡片发送成功，则直接结束
                        if not self.welc_text:
                            e_context.action = EventAction.BREAK_PASS
                            return
                    else:
                        logger.warning(f"[HelloPlus] 发送欢迎卡片失败，返回码: {ret}")
                else:
                    logger.warning(f"[HelloPlus] 无法获取用户头像URL，无法发送欢迎卡片")
                
                # 如果需要发送文本欢迎（welc_text=True或卡片发送失败）
                if self.welc_text or not card_sent:
                    if card_sent:
                        time.sleep(2)  # 如果已发送卡片，等待一下再发文本
                    e_context["context"].type = ContextType.TEXT
                    e_context["context"].content = self.group_welc_prompt.format(nickname=msg.actual_user_nickname)
                    e_context.action = EventAction.BREAK
                
            except Exception as e:
                logger.error(f"[HelloPlus] 处理欢迎信息异常: {str(e)}")
                e_context["context"].type = ContextType.TEXT
                e_context["context"].content = self.group_welc_prompt.format(nickname=msg.actual_user_nickname)
                e_context.action = EventAction.BREAK
            
            if not self.config or not self.config.get("use_character_desc"):
                e_context["context"]["generate_breaked_by"] = EventAction.BREAK
            return
        
        if e_context["context"].type == ContextType.EXIT_GROUP:
            
            if "group_exit_msg" in conf():
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = conf().get("group_exit_msg", "")
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
                return
            if conf().get("group_chat_exit_group"):
                e_context["context"].type = ContextType.TEXT
                e_context["context"].content = self.group_exit_prompt.format(nickname=msg.actual_user_nickname)
                e_context.action = EventAction.BREAK  # 事件结束，进入默认处理逻辑
                return
            e_context.action = EventAction.BREAK
            return
            
        if e_context["context"].type == ContextType.PATPAT:
            logger.debug(f"[HelloPlus] 收到拍一拍消息: {msg.content}")
            e_context["context"].type = ContextType.TEXT
            # 获取发送拍一拍消息的用户昵称
            pat_nickname = msg.actual_user_nickname
            # 替换模板中的{nickname}为实际用户昵称
            e_context["context"].content = self.patpat_prompt.format(nickname=pat_nickname)
            logger.debug(f"[HelloPlus] 拍一拍处理，替换后的内容: {e_context['context'].content}")
            e_context.action = EventAction.BREAK  # 事件结束，进入默认处理逻辑
            if not self.config or not self.config.get("use_character_desc"):
                e_context["context"]["generate_breaked_by"] = EventAction.BREAK
            return

        content = e_context["context"].content
        logger.debug("[Hello] on_handle_context. content: %s" % content)
        if content == "Hello":
            reply = Reply()
            reply.type = ReplyType.TEXT
            if e_context["context"]["isgroup"]:
                reply.content = f"Hello, {msg.actual_user_nickname} from {msg.from_user_nickname}"
            else:
                reply.content = f"Hello, {msg.from_user_nickname}"
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS  # 事件结束，并跳过处理context的默认逻辑
        user_id=msg.actual_user_id
        if content.startswith('群监控管理验证'):
            if e_context["context"]["isgroup"]:
                reply_cont="不支持群聊验证"
                reply = self.create_reply(ReplyType.TEXT, reply_cont)
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS 
                return
            tk = content[7:].strip()
            reply_cont="验证成功,已将您设为群监控管理员。" if self.add_admin_user(tk,user_id) else "验证失败"
            reply = self.create_reply(ReplyType.TEXT, reply_cont)
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK_PASS 
        if e_context["context"]["isgroup"]:
            if content =='查看监控群列表':
                if not AdminUtil.is_admin(msg):
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = "没权限啊"
                    e_context["reply"] = reply
                    e_context.action = EventAction.BREAK_PASS
                    return
                # 获取监控群列表
                # 从self.group_members获取每个群的成员数量
                group_member_count = {}
                for group_id, members in self.group_members.items():
                    group_member_count[group_id] = len(members)
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = "监控群列表：\n"
                for group_id in self.monitoring_groups:
                    reply.content += f" 💬{self.monitoring_groups_name[group_id]} -🙎‍♂️当前成员： {group_member_count[group_id]}人\n"
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                return
            if content =='开启退群监控':
                if not AdminUtil.is_admin(msg):
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = "没权限啊"
                    e_context["reply"] = reply
                    e_context.action = EventAction.BREAK_PASS
                    return
                self.get_member_list(msg)
                reply = Reply()
                reply.type = ReplyType.TEXT
                reply.content = f"当前群[{msg.other_user_nickname}]已开启退群监控"
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                return
            if content == "关闭退群监控":
                if not AdminUtil.is_admin(msg):
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = "没权限啊"
                    e_context["reply"] = reply
                    e_context.action = EventAction.BREAK_PASS
                    return
                group_id = msg.other_user_id
                if group_id in self.monitoring_groups:
                    self.monitoring_groups.remove(group_id)
                    self.monitoring_groups_name.pop(group_id)
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = f"当前群[{msg.other_user_nickname}]已关闭退群监控"
                    e_context["reply"] = reply
                else:
                    reply = Reply()
                    reply.type = ReplyType.TEXT
                    reply.content = "当前群未开启退群监控"
                    e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                return
        if content == "Hi":
            reply = Reply()
            reply.type = ReplyType.TEXT
            reply.content = "Hi"
            e_context["reply"] = reply
            e_context.action = EventAction.BREAK  # 事件结束，进入默认处理逻辑，一般会覆写reply

        if content == "End":
            # 如果是文本消息"End"，将请求转换成"IMAGE_CREATE"，并将content设置为"The World"
            e_context["context"].type = ContextType.IMAGE_CREATE
            content = "The World"
            e_context.action = EventAction.CONTINUE  # 事件继续，交付给下个插件或默认逻辑

    def get_help_text(self, **kwargs):
        help_text = "输入Hello，我会回复你的名字\n输入End，我会回复你世界的图片\n"
        return help_text

    def _load_config_template(self):
        logger.debug("No Hello plugin config.json, use plugins/hello/config.json.template")
        try:
            plugin_config_path = os.path.join(self.path, "config.json.template")
            if os.path.exists(plugin_config_path):
                with open(plugin_config_path, "r", encoding="utf-8") as f:
                    plugin_conf = json.load(f)
                    return plugin_conf
        except Exception as e:
            logger.exception(e)
    def build_card_content(self, card_type, nickname, signature, timestamp, url, imgurl):
        """
        Unified method to build card content for welcome and exit messages.
        :param card_type: Type of card ('welcome' or 'exit')
        :param nickname: User's nickname
        :param signature: User's signature
        :param timestamp: Formatted timestamp
        :param url: URL for the card
        :param imgurl: URL for the thumbnail image
        :return: Formatted appmsg content string
        """
        safe_nickname = nickname.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;')
        safe_signature = (signature if signature else '').replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;')
        if card_type == 'welcome':
            # 将欢迎信息移到标题
            title = f"👋 欢迎 {safe_nickname} 加入群聊！"
            # 时间和签名信息放在描述部分
            desc = f"⌚ {timestamp}\n📝 签名：{safe_signature if safe_signature and safe_signature.strip() else '暂无签名'}"
        else:  # exit - 保持退群卡片格式
            title = f"{safe_nickname} 离开群聊！"
            safe_exit_msg = self.say_exit.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;')
            desc = f"⌚ {timestamp}\n{safe_exit_msg}"
        
        # 保持原有的XML结构
        return f'''<appmsg appid="" sdkver="1">
<title>{title}</title>
<des>{desc}</des>
<action>view</action>
<type>5</type>
<showtype>0</showtype>
<content></content>
<url>{url}</url>
<dataurl></dataurl>
<lowurl></lowurl>
<lowdataurl></lowdataurl>
<recorditem></recorditem>
<thumburl>{imgurl}</thumburl>
<messageaction></messageaction>
<laninfo></laninfo>
<extinfo></extinfo>
<sourceusername></sourceusername>
<sourcedisplayname></sourcedisplayname>
<commenturl></commenturl>
<appattach>
<totallen>0</totallen>
<attachid></attachid>
<emoticonmd5></emoticonmd5>
<fileext></fileext>
<aeskey></aeskey>
</appattach>
<webviewshared>
<publisherId></publisherId>
<publisherReqId>0</publisherReqId>
</webviewshared>
<weappinfo>
<pagepath></pagepath>
<username></username>
<appid></appid>
<appservicetype>0</appservicetype>
</weappinfo>
<websearch></websearch>
</appmsg>'''
    def welcome(self, msg, qm, imgurl):
        import requests
        import json
        from datetime import datetime
        post_url = f"{self.base_url}/message/postAppMsg"
        now = datetime.now().strftime("%Y年%m月%d日 %H时%M分%S秒")
        url = self.redirect_link
        # Use the nickname from get_info if available, otherwise fallback
        nickname = msg.actual_user_nickname
        if hasattr(msg, 'nickName') and msg.nickName:
            nickname = msg.nickName
        appmsg_content = self.build_card_content('welcome', nickname, qm, now, url, imgurl if imgurl else '')
        try:
            # 构建请求数据
            payload = json.dumps({
                "appId": self.appid,
                "toWxid": msg.other_user_id,
                "appmsg": appmsg_content
            })
            
            # 调试输出
            print(f"发送欢迎卡片: {payload[:100]}...")
            
            # 发送请求
            response = requests.request("POST", post_url, data=payload, headers=self.headers)
            result = response.json()
            print(f"欢迎卡片返回: {result}")
            
            return result.get('ret', 0)  # 使用get方法获取ret，如果不存在返回0
        except Exception as e:
            logger.error(f"[HelloPlus] 发送欢迎卡片异常: {str(e)}")
            return 0  # 出现异常返回0，表示发送失败
    def exit(self, group_id, imgurl, nickName):
        import requests
        import json
        from datetime import datetime
        post_url = f"{self.base_url}/message/postAppMsg"
        now = datetime.now().strftime("%Y年%m月%d日 %H时%M分%S秒")
        appmsg_content = self.build_card_content('exit', nickName, None, now, self.exit_url, imgurl if imgurl else '')
        # 构建请求数据
        payload = json.dumps({
            "appId": self.appid,
            "toWxid": group_id,
            "appmsg": appmsg_content
        })
        
        # 调试输出
        print(f"发送退出卡片: {payload[:100]}...")
        
        # 发送请求
        response = requests.request("POST", post_url, data=payload, headers=self.headers)
        result = response.json()
        print(f"退出卡片返回: {result}")
        
        return result['ret']
    def get_info(self,group_id,nickname):
        import requests
        import json
        print('----get_info----')
        wxid=self.get_list(group_id,nickname)
        if wxid==None:
            return None, None, None  # 返回三元组而不是单个None，避免解包错误
        payload = json.dumps({
            "appId": self.appid,
            "chatroomId": group_id,
            "memberWxids": [
               wxid
            ]
        })
        data=requests.request("POST", f"{self.base_url}/group/getChatroomMemberDetail", data=payload, headers=self.headers).json()
        print('----get_info----',data["data"][0]["signature"],data["data"][0]["smallHeadImgUrl"],data["data"][0]["nickName"])
        return data["data"][0]["signature"],data["data"][0]["smallHeadImgUrl"],data["data"][0]["nickName"]
    def get_list(self,group_id,nickname):
        print('----get_list----')
        print('----group_id----',group_id,nickname)
        import requests
        import json
        payload = json.dumps({
           "appId": self.appid,
            "chatroomId": group_id,
        })
        
        data=requests.request("POST", f"{self.base_url}/group/getChatroomMemberList", data=payload, headers=self.headers).json()
        ret=data['ret']
        if ret!=200:
            return None
        wxid=None
       
        for member in data["data"]["memberList"]:
            if member["nickName"] == nickname:
                wxid=member["wxid"]
        print('----get_list----',wxid)
        return wxid  
    def get_member_list(self, msg):
        """
        获取群成员列表并监控退群行为
        Args:
            msg: 消息对象,包含群id等信息
        Returns:
            list: 群成员列表,None表示获取失败
        """
        print('----get_member_list----')
        import requests
        import json
        import time
        import threading
        
        def monitor_group(group_id):
            
            while group_id in self.monitoring_groups:  # 检查监控状态
                try:
                    # 构造请求参数
                    payload = json.dumps({
                        "appId": self.appid,
                        "chatroomId": group_id,
                    })
                    
                    # 请求群成员列表
                    data = requests.request("POST", f"{self.base_url}/group/getChatroomMemberList", 
                                         data=payload, headers=self.headers).json()
                    
                    # 检查请求是否成功
                    if data.get('ret') != 200:
                        continue
                        
                    current_members = data["data"]["memberList"]
                    
                    # 如果是第一次获取,直接保存
                    if group_id not in self.group_members:
                        self.group_members[group_id] = current_members
                    else:
                        # 对比新旧成员列表,找出退群成员
                        old_members = self.group_members[group_id]
                        old_wxids = {m["wxid"] for m in old_members}
                        new_wxids = {m["wxid"] for m in current_members}
                        
                        left_members = old_wxids - new_wxids
                        if left_members:
                            for wxid in left_members:
                                # 找到退群成员昵称
                                member = next(m for m in old_members if m["wxid"] == wxid)
                                print(f"用户 {member['nickName']} 退出了群聊")
                               
                                self.exit(group_id, member['smallHeadImgUrl'],member['nickName'])
                        
                        # 更新本地群成员数据        
                        self.group_members[group_id] = current_members
                    print(f"开始监控群 {group_id}")
                    self.memberList = current_members
                    time.sleep(self.sleep_time)  # 每self.sleep_time
                except Exception as e:
                    print(f"监控群 {group_id} 异常: {e}")
                    if group_id not in self.monitoring_groups:  # 如果已关闭监控则退出
                        break
                    time.sleep(self.sleep_time)
                    continue
            print(f"停止监控群 {group_id}")

        # 如果该群还没有监控线程,则创建新线程
        if msg.other_user_id not in self.monitor_threads:
            self.monitoring_groups.add(msg.other_user_id)  # 添加到监控集合
            self.monitoring_groups_name[msg.other_user_id]=msg.other_user_nickname  # 添加到监控集合
            t = threading.Thread(target=monitor_group, args=(msg.other_user_id,))
            t.daemon = True
            t.start()
            self.monitor_threads[msg.other_user_id] = t
            
        return self.memberList
    
    def add_admin_user(self, token, wxid):
        """
        验证token并将用户添加到全局管理员列表
        :param token: 验证令牌
        :param wxid: 用户ID
        :return: True 成功, False 失败
        """
        if token == self.auth_token:
            print('--**验证成功')
            if wxid not in global_config["admin_users"]:
                global_config["admin_users"].append(wxid)
            print(global_config["admin_users"])
            return True
        return False
    
    def create_reply(self, reply_type, content):
        reply = Reply()
        reply.type = reply_type
        reply.content = content
        return reply
