# 智趣问答游戏插件

一个有趣的微信游戏插件，包含成语猜猜乐、脑筋急转弯、猜广告词和猜歇后语四种游戏模式，支持智能提示和排行榜功能。

## 功能特点

1. 支持成语猜猜乐、脑筋急转弯、猜广告词和猜歇后语四种游戏模式
2. 每轮游戏包含5道题目
3. 每题限时20秒
4. 支持智能提示和追问功能
5. 实时排行榜系统
6. 支持群聊和私聊
7. 支持OpenAI智能解析和提示
8. 支持管理员认证和排行榜管理

## 游戏命令

1. `猜成语` - 开始成语猜猜乐游戏
2. `脑筋急转弯` - 开始脑筋急转弯游戏 
3. `猜广告词` - 开始猜广告词游戏
4. `猜歇后语` - 开始猜歇后语游戏
5. `提示` - 获取智能提示
6. `问+问题` - 获取更多提示（例如：问意思、问出处）
7. `我猜xxx` - 提交答案
8. `下一题` - 跳过当前题目
9. `结束游戏` - 结束当前游戏
10. `历史排行榜` - 查看成绩排行
11. `智趣帮助` - 查看帮助信息

## 管理员命令

1. `智趣认证+密码` - 进行管理员认证
2. `重置历史排行榜` - 清空所有排行榜数据（仅限管理员）

## 游戏规则

1. 每轮游戏包含5道题目
2. 每题限时20秒，超时自动进入下一题
3. 答对一题得1分
4. 可以使用提示和追问功能获取帮助
5. 支持跳过难题，直接进入下一题
6. 游戏结束后显示得分和排行榜

## 配置说明

在 `config.json` 中可以配置以下参数：

```json
{
    "api_url": "https://xiaoapi.cn/API/game_ktccy.php",
    "brain_teaser_api_url": "https://api.dragonlongzhu.cn/api/yl_njjzw.php",
    "ad_slogan_api_url": "https://api.dragonlongzhu.cn/api/yl_guanggao.php",
    "xiehouyu_api_url": "https://api.dragonlongzhu.cn/api/yl_xiehouyu.php",
    "cache_timeout": 300,
    "questions_per_round": 5,
    "leaderboard_size": 10,
    "gewechat_base_url": "",
    "gewechat_token": "",
    "gewechat_app_id": "",
    "auth_password": "1122",
    "default_game_mode": "idiom",
    "enable_openai": true,
    "openai_api_key": "",
    "openai_model": "gpt-3.5-turbo",
    "openai_timeout": 10,
    "openai_api_base": "https://api.openai.com/v1",
    "game_settings": {
        "time_limit": 20,
        "auto_next_delay": 1,
        "correct_answer_delay": 3
    }
}
```

### 配置参数说明

- `api_url`: 成语题目API地址
- `brain_teaser_api_url`: 脑筋急转弯API地址
- `ad_slogan_api_url`: 广告词API地址
- `xiehouyu_api_url`: 歇后语API地址
- `cache_timeout`: 缓存超时时间（秒）
- `questions_per_round`: 每轮题目数量
- `leaderboard_size`: 排行榜显示人数
- `gewechat_base_url`: GeWeChat API 基础地址
- `gewechat_token`: GeWeChat API Token
- `gewechat_app_id`: GeWeChat APP ID
- `auth_password`: 管理员认证密码
- `default_game_mode`: 默认游戏模式 (idiom-成语猜猜乐, brainteaser-脑筋急转弯, adslogan-猜广告词, xiehouyu-猜歇后语)
- `enable_openai`: 是否启用OpenAI功能
- `openai_api_key`: OpenAI API密钥
- `openai_model`: OpenAI模型名称
- `openai_timeout`: OpenAI请求超时时间(秒)
- `openai_api_base`: OpenAI API基础地址
- `game_settings`:
  - `time_limit`: 每题限时（秒）
  - `auto_next_delay`: 自动进入下一题的延迟（秒）
  - `correct_answer_delay`: 答对后进入下一题的延迟（秒）

## 安装说明

1. 将插件文件夹复制到 `plugins` 目录下
2. 配置 `config.json` 文件
3. 重启应用即可使用

## 注意事项

1. 确保API地址可以正常访问
2. 如需使用OpenAI功能，请配置有效的API密钥
3. 建议定期备份排行榜数据
4. 游戏进行中可随时结束或跳过当前题目
5. 管理员功能需要先进行认证才能使用

## 更新日志

### v1.1
- 新增猜广告词游戏模式
- 新增猜歇后语游戏模式
- 优化排行榜系统
- 改进游戏提示机制

### v1.0
- 初始版本发布
- 支持成语猜猜乐和脑筋急转弯两种游戏模式
- 添加排行榜系统
- 集成OpenAI智能提示功能
- 支持管理员认证和排行榜管理
- 优化游戏体验和提示系统 
