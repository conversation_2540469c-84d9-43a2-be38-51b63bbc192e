import json
import os
import time
import uuid
import random
import requests
import binascii
import hmac
import hashlib
import datetime
import urllib.parse
from ..module.token_manager import TokenManager
from common.log import logger
import zlib

class VideoGenerator:
    def __init__(self, config, token_manager):
        self.config = config
        self.token_manager = token_manager
        self.video_api_config = config.get("video_api", {})
        
    def upload_image(self, image_path):
        """仅上传图片并返回image_uri信息
        Args:
            image_path: 图片路径
        Returns:
            dict: 包含image_uri的字典，如{"image_uri": "tos-cn-i-xxx/xxx"}
        """
        try:
            logger.info("[<PERSON><PERSON>] 开始上传图片")
            
            # 获取上传token
            token_info = self.token_manager.get_upload_token()
            if not token_info:
                logger.error("[Jim<PERSON>] 获取上传token失败")
                return None
            
            # 获取文件大小
            file_size = os.path.getsize(image_path)
            
            # 申请上传
            upload_info = self.apply_image_upload(token_info, file_size)
            if not upload_info or "Result" not in upload_info:
                logger.error(f"[Jimeng] 申请上传失败: {upload_info}")
                return None
            
            logger.info("[Jimeng] 图片上传申请成功，开始上传图片")
            
            # 上传图片
            file_response = self.upload_image_file(upload_info, image_path)
            if not file_response:
                logger.error(f"[Jimeng] 上传图片失败: {file_response}")
                return None
            
            logger.info("[Jimeng] 图片上传成功，开始提交上传信息")
            
            # 提交上传
            commit_response = self.commit_image_upload(token_info, upload_info)
            if not commit_response or "Result" not in commit_response:
                logger.error(f"[Jimeng] 提交上传失败: {commit_response}")
                return None
            
            # 从响应中提取image_uri
            try:
                plugin_result = commit_response['Result']['PluginResult'][0]
                image_uri = plugin_result['ImageUri']
                
                # 确保image_uri格式正确
                if not image_uri.startswith("tos-cn-i-"):
                    # 完整的ImageUri应该是类似"tos-cn-i-tb4s082cfz/xxx"的格式
                    if "/" not in image_uri:
                        # 如果返回的只是ID部分，则构建完整路径
                        image_uri = f"tos-cn-i-tb4s082cfz/{image_uri}"
                
                logger.info(f"[Jimeng] 图片上传成功，获取到image_uri: {image_uri}")
                return {"image_uri": image_uri}
            except Exception as e:
                logger.error(f"[Jimeng] 从上传响应中提取image_uri失败: {str(e)}")
                return None
            
        except Exception as e:
            logger.error(f"[Jimeng] 上传图片过程出错: {str(e)}")
            return None
        
    def upload_image_and_generate_video(self, image_path, prompt="人物表情慢慢变沮丧痛哭流涕", model="s2.0"):
        """上传图片并生成视频
        Args:
            image_path: 图片路径
            prompt: 提示词
            model: 视频模型，默认s2.0
        Returns:
            tuple: (success, result)
        """
        try:
            logger.info("[Jimeng] 开始上传图片并生成视频")
            
            # 获取上传token
            token_info = self.token_manager.get_upload_token()
            if not token_info:
                return False, "获取上传token失败"
            
            # 获取文件大小
            file_size = os.path.getsize(image_path)
            
            # 申请上传
            upload_info = self.apply_image_upload(token_info, file_size)
            if not upload_info or "Result" not in upload_info:
                return False, f"申请上传失败: {upload_info}"
            
            logger.info("[Jimeng] 图片上传申请成功，开始上传图片")
            logger.debug(f"[Jimeng] 上传申请结果: {json.dumps(upload_info, ensure_ascii=False)}")
            
            # 上传图片
            file_response = self.upload_image_file(upload_info, image_path)
            if not file_response:
                return False, f"上传图片失败: {file_response}"
            
            logger.info("[Jimeng] 图片上传成功，开始提交上传信息")
            
            # 提交上传
            commit_response = self.commit_image_upload(token_info, upload_info)
            if not commit_response or "Result" not in commit_response:
                return False, f"提交上传失败: {commit_response}"
            
            logger.info("[Jimeng] 上传信息提交成功，开始生成视频")
            logger.debug(f"[Jimeng] 图片信息: {json.dumps(commit_response, ensure_ascii=False)}")
            
            # 生成视频，传递 model 参数
            video_response = self.generate_video(commit_response, prompt=prompt, model=model)
            if not video_response or video_response.get("ret") != "0":
                return False, f"生成视频失败: {video_response}"
            
            # 获取task_id
            task_id = video_response.get("data", {}).get("aigc_data", {}).get("task", {}).get("task_id")
            if not task_id:
                return False, "未获取到任务ID"
            
            logger.info("[Jimeng] 视频生成任务创建成功，等待生成完成")
            
            # 检查视频状态
            success, result = self.check_video_status(task_id)
            return success, result
            
        except Exception as e:
            logger.error(f"[Jimeng] 视频生成过程出错: {str(e)}")
            return False, str(e)
        
    def apply_image_upload(self, token_info, file_size):
        """申请图片上传
        Args:
            token_info: 上传token信息
            file_size: 文件大小
        Returns:
            dict: 上传信息
        """
        try:
            # Get current timestamp
            t = datetime.datetime.utcnow()
            amz_date = t.strftime('%Y%m%dT%H%M%SZ')
            datestamp = t.strftime('%Y%m%d')
            
            # Request parameters - 保持固定顺序
            request_parameters = {
                'Action': 'ApplyImageUpload',
                'FileSize': str(file_size),
                'ServiceId': token_info['space_name'],
                'Version': '2018-08-01'
            }
            
            # 构建规范请求字符串
            canonical_querystring = '&'.join([f'{k}={urllib.parse.quote(str(v))}' for k, v in sorted(request_parameters.items())])
            
            # 构建规范请求
            canonical_uri = '/'
            canonical_headers = (
                f'host:imagex.bytedanceapi.com\n'
                f'x-amz-date:{amz_date}\n'
                f'x-amz-security-token:{token_info["session_token"]}\n'
            )
            signed_headers = 'host;x-amz-date;x-amz-security-token'
            
            # 计算请求体哈希
            payload_hash = hashlib.sha256(b'').hexdigest()
            
            # 构建规范请求
            canonical_request = '\n'.join([
                'GET',
                canonical_uri,
                canonical_querystring,
                canonical_headers,
                signed_headers,
                payload_hash
            ])
            
            # 获取授权头
            authorization = self.get_authorization(
                token_info['access_key_id'],
                token_info['secret_access_key'],
                'cn-north-1',
                'imagex',
                amz_date,
                token_info['session_token'],
                signed_headers,
                canonical_request
            )
            
            # 设置请求头
            headers = {
                'Authorization': authorization,
                'X-Amz-Date': amz_date,
                'X-Amz-Security-Token': token_info['session_token'],
                'Host': 'imagex.bytedanceapi.com'
            }
            
            url = f'https://imagex.bytedanceapi.com/?{canonical_querystring}'
            
            response = requests.get(url, headers=headers)
            return response.json()
            
        except Exception as e:
            logger.error(f"[Jimeng] Error in apply_image_upload: {str(e)}")
            return None
        
    def upload_image_file(self, upload_info, image_path):
        """上传图片文件
        Args:
            upload_info: 上传信息
            image_path: 图片路径
        Returns:
            dict: 上传结果
        """
        try:
            store_info = upload_info['Result']['UploadAddress']['StoreInfos'][0]
            upload_host = upload_info['Result']['UploadAddress']['UploadHosts'][0]
            
            url = f"https://{upload_host}/upload/v1/{store_info['StoreUri']}"
            
            # 计算文件的CRC32
            with open(image_path, 'rb') as f:
                content = f.read()
                crc32 = format(binascii.crc32(content) & 0xFFFFFFFF, '08x')
            
            headers = {
                'accept': '*/*',
                'authorization': store_info['Auth'],
                'content-type': 'application/octet-stream',
                'content-disposition': 'attachment; filename="undefined"',
                'content-crc32': crc32,
                'origin': 'https://jimeng.jianying.com',
                'referer': 'https://jimeng.jianying.com/'
            }
            
            response = requests.post(url, headers=headers, data=content)
            return response.json()
            
        except Exception as e:
            logger.error(f"[Jimeng] Error in upload_image_file: {str(e)}")
            return None
        
    def commit_image_upload(self, token_info, upload_info):
        """提交图片上传"""
        amz_date = time.strftime("%Y%m%dT%H%M%SZ", time.gmtime())
        session_key = upload_info['Result']['UploadAddress']['SessionKey']
        
        url = f"https://{token_info['upload_domain']}"
        params = {
            "Action": "CommitImageUpload",
            "Version": "2018-08-01",
            "ServiceId": token_info['space_name']
        }
        
        data = {"SessionKey": session_key}
        payload = json.dumps(data)
        content_sha256 = hashlib.sha256(payload.encode('utf-8')).hexdigest()
        
        # 构建规范请求
        canonical_uri = "/"
        canonical_querystring = "&".join([f"{k}={v}" for k, v in sorted(params.items())])
        signed_headers = "x-amz-content-sha256;x-amz-date;x-amz-security-token"
        canonical_headers = f"x-amz-content-sha256:{content_sha256}\nx-amz-date:{amz_date}\nx-amz-security-token:{token_info['session_token']}\n"
        
        canonical_request = f"POST\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{content_sha256}"
        
        authorization = self.get_authorization(
            token_info['access_key_id'],
            token_info['secret_access_key'],
            'cn-north-1',
            'imagex',
            amz_date,
            token_info['session_token'],
            signed_headers,
            canonical_request
        )
        
        headers = {
            'accept': '*/*',
            'content-type': 'application/json',
            'authorization': authorization,
            'x-amz-content-sha256': content_sha256,
            'x-amz-date': amz_date,
            'x-amz-security-token': token_info['session_token'],
            'origin': 'https://jimeng.jianying.com',
            'referer': 'https://jimeng.jianying.com/'
        }
        
        response = requests.post(f"{url}?{canonical_querystring}", headers=headers, data=payload)
        return response.json()
        
    def generate_video(self, commit_info, prompt="人物表情慢慢变沮丧痛哭流涕", model="s2.0"):
        """生成视频
        Args:
            commit_info: 提交上传后的信息
            prompt: 提示词
            model: 视频模型，默认s2.0
        Returns:
            dict: 视频生成任务的响应
        """
        try:
            # 记录调试信息
            logger.info(f"[Jimeng] 开始生成视频，使用模型: {model}")
            
            # 获取token信息
            token_info = self.token_manager.get_token("/mweb/v1/generate_video")
            if not token_info:
                return None
                
            # 获取账号信息
            account = self.token_manager.get_current_account()
            if not account:
                return None

            # 从配置文件获取模型信息
            video_models = self.config.get("video_models", {})
            model_info = video_models.get(model.lower(), video_models.get("s2.0", {}))
            
            # 默认参数设置 - 修改这里的参数设置
            default_params = {
                "s2.0": {
                    "benefit_type": "basic_video_operation_vgfm_lite",
                    "fps": 24,
                    "feature_entrance": "to_video",  # 修改为to_video
                    "feature_entrance_detail": "to_video-image_to_video",
                    "video_mode": 2  # 修改为2
                },
                "s2.0p": {
                    "benefit_type": "basic_video_operation_vgfm",
                    "fps": 24,
                    "feature_entrance": "to_video",
                    "feature_entrance_detail": "to_video-image_to_video",
                    "video_mode": 2
                },
                "p2.0p": {
                    "benefit_type": "basic_video_operation_lab_14",
                    "fps": 12,
                    "feature_entrance": "to_video",
                    "feature_entrance_detail": "to_video-image_to_video",
                    "video_mode": 2
                }
            }
            
            params_info = default_params.get(model.lower(), default_params["s2.0"])
            
            model_req_key = model_info.get("model_req_key", "dreamina_ic_generate_video_model_vgfm_lite")
            benefit_type = params_info["benefit_type"]
            fps = params_info["fps"]
            feature_entrance = params_info["feature_entrance"]
            feature_entrance_detail = params_info["feature_entrance_detail"]
            video_mode = params_info["video_mode"]

            plugin_result = commit_info['Result']['PluginResult'][0]
            
            # 构建请求参数
            babi_param = {
                "scenario": "image_video_generation",
                "feature_key": "image_to_video",
                "feature_entrance": feature_entrance,
                "feature_entrance_detail": feature_entrance_detail
            }
            
            params = {
                "aid": "513695",
                "babi_param": json.dumps(babi_param),
                "device_platform": "web",
                "region": "CN",
                "web_id": self.token_manager.get_web_id(),
                "msToken": token_info.get("msToken", ""),
                "a_bogus": token_info.get("a_bogus", "")
            }

            # 构建请求体 - 简化first_frame_image结构
            data = {
                "submit_id": str(uuid.uuid4()),
                "task_extra": json.dumps({
                    "promptSource": "custom",
                    "originSubmitId": str(uuid.uuid4()),
                    "isDefaultSeed": 1,
                    "originTemplateId": "",
                    "imageNameMapping": {},
                    "isUseAiGenPrompt": False,
                    "batchNumber": 1
                }),
                "http_common_info": {
                    "aid": "513695"
                },
                "input": {
                    "seed": random.randint(1000000000, 9999999999),
                    "video_gen_inputs": [{
                        "prompt": prompt,
                        "first_frame_image": {
                            "width": plugin_result['ImageWidth'],
                            "height": plugin_result['ImageHeight'],
                            "image_uri": plugin_result['ImageUri']
                            # 移除额外的字段
                        },
                        "fps": fps,
                        "duration_ms": 5000,
                        "video_mode": video_mode,
                        "template_id": ""
                    }],
                    "priority": 0,
                    "model_req_key": model_req_key
                },
                "mode": "workbench",
                "history_option": {},
                "commerce_info": {
                    "resource_id": "generate_video",
                    "resource_id_type": "str",
                    "resource_sub_type": "aigc",
                    "benefit_type": benefit_type
                },
                "client_trace_data": {}
            }

            # 根据模型添加额外的请求头
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'app-sdk-version': '48.0.0',
                'appid': '513695',
                'appvr': '5.8.0',
                'content-type': 'application/json',
                'cookie': token_info["cookie"],
                'device-time': token_info["device_time"],
                'lan': 'zh-Hans',
                'loc': 'cn',
                'origin': 'https://jimeng.jianying.com',
                'pf': '7',
                'priority': 'u=1, i',
                'referer': 'https://jimeng.jianying.com/ai-tool/video/generate',
                'sign': token_info["sign"],
                'sign-ver': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            }

            url = "https://jimeng.jianying.com/mweb/v1/generate_video"

            # 打印详细的请求信息
            logger.debug(f"[Jimeng] 生成视频请求URL: {url}")
            logger.debug(f"[Jimeng] 生成视频请求参数: {json.dumps(params, ensure_ascii=False)}")
            logger.debug(f"[Jimeng] 生成视频请求头: {json.dumps(headers, ensure_ascii=False)}")
            logger.debug(f"[Jimeng] 生成视频请求体: {json.dumps(data, ensure_ascii=False)}")

            response = requests.post(url, headers=headers, params=params, json=data)
            if response.status_code != 200:
                error_msg = f"视频生成请求失败: HTTP {response.status_code} - {response.text}"
                logger.error(f"[Jimeng] {error_msg}")
                return None, error_msg

            result = response.json()
            if result.get("ret") != "0":
                # 打印完整的错误信息
                error_msg = f"API错误: {result.get('ret')} - {result.get('errmsg')}"
                details = result.get("data", {})
                logger.error(f"[Jimeng] {error_msg}")
                logger.error(f"[Jimeng] 错误详情: {json.dumps(details, ensure_ascii=False)}")
                return None, error_msg

            return result

        except Exception as e:
            logger.error(f"[Jimeng] Error in generate_video: {str(e)}")
            return None
        
    def check_video_status(self, task_id, max_attempts=120, check_interval=3):
        """检查视频生成状态
        Args:
            task_id: 任务ID
            max_attempts: 最大尝试次数
            check_interval: 检查间隔(秒)
        Returns:
            tuple: (success, result)
        """
        try:
            attempt = 0
            last_status = None
            status_unchanged_count = 0
            
            while attempt < max_attempts:
                attempt += 1
                try:
                    # 获取token信息
                    token_info = self.token_manager.get_token("/mweb/v1/mget_generate_task")
                    if not token_info:
                        return False, "获取token失败"
                        
                    # 获取账号信息
                    account = self.token_manager.get_current_account()
                    if not account:
                        return False, "获取账号信息失败"

                    headers = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'app-sdk-version': '48.0.0',
                        'appid': '513695',
                        'appvr': '5.8.0',
                        'content-type': 'application/json',
                        'cookie': token_info["cookie"],
                        'device-time': token_info["device_time"],
                        'sign': token_info["sign"],
                        'sign-ver': '1',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    }

                    data = {
                        "task_id_list": [task_id],
                        "http_common_info": {
                            "aid": "513695",
                            "app_id": "513695",
                            "user_id": account.get("user_id", ""),
                            "device_id": account.get("web_id", "")
                        }
                    }

                    try:
                        response = requests.post(
                            f"https://jimeng.jianying.com/mweb/v1/mget_generate_task?aid=513695",
                            headers=headers,
                            json=data,
                            timeout=10
                        )
                    except requests.exceptions.Timeout:
                        logger.error(f"[Jimeng] Request timeout when checking status")
                        time.sleep(check_interval)
                        continue
                    except requests.exceptions.RequestException as e:
                        logger.error(f"[Jimeng] Request error when checking status: {str(e)}")
                        time.sleep(check_interval)
                        continue
                    
                    if response.status_code != 200:
                        logger.error(f"[Jimeng] 检查视频状态失败: HTTP {response.status_code}")
                        time.sleep(check_interval)
                        continue
                        
                    result = response.json()
                    if result.get("ret") != "0":
                        logger.error("[Jimeng] 检查视频状态API返回错误")
                        time.sleep(check_interval)
                        continue

                    task_map = result.get("data", {}).get("task_map", {})
                    task_info = task_map.get(task_id)
                    
                    if not task_info:
                        logger.error("[Jimeng] 未找到视频任务信息")
                        time.sleep(check_interval)
                        continue
                    
                    status = task_info.get("status", 0)

                    # 只在状态变化时输出日志
                    if status != last_status:
                        status_msg = {
                            0: "初始化中",
                            10: "排队中",
                            20: "准备中",
                            30: "生成中",
                            40: "处理中",
                            50: "生成成功",
                            60: "生成失败"
                        }.get(status, f"未知状态({status})")
                        logger.info(f"[Jimeng] 视频状态: {status_msg}")
                    
                    # 检查状态是否连续多次未变化
                    if status == last_status:
                        status_unchanged_count += 1
                        if status_unchanged_count >= 40:  # 如果状态连续40次未变化(约2分钟)
                            if status == 0:
                                return False, "视频生成初始化超时"
                            elif status == 10:
                                return False, "视频生成排队超时"
                            elif status == 20:
                                return False, "视频生成准备超时"
                            elif status == 30:
                                return False, "视频生成失败，积分已返还，请重试"
                            elif status == 40:
                                return False, "视频处理超时"
                    else:
                        status_unchanged_count = 0
                        last_status = status
                    
                    # 状态码说明:
                    # 0: 初始化
                    # 10: 排队中
                    # 20: 准备中
                    # 30: 生成中
                    # 40: 处理中
                    # 50: 成功
                    # 60: 失败
                    
                    if status == 50:  # 成功
                        item_list = task_info.get("item_list", [])
                        if not item_list:
                            return False, "视频生成成功但未返回URL"
                        
                        video = item_list[0].get("video", {})
                        if not video:
                            return False, "视频数据为空"
                            
                        # 获取视频链接，优先使用原始清晰度
                        video_info = video.get("transcoded_video", {}).get("origin", {})
                        if not video_info:
                            return False, "未获取到视频信息"

                        video_url = video_info.get("video_url")
                        if not video_url:
                            return False, "未获取到视频URL"
                            
                        logger.info(f"[Jimeng] 视频生成完成，清晰度: {video_info.get('definition', 'origin')}, 大小: {video_info.get('size', 0)} bytes")
                        return True, video_url
                        
                    elif status == 60:  # 失败
                        fail_code = task_info.get("task_payload", {}).get("fail_code", "")
                        error_msg = f"视频生成失败，错误码：{fail_code}" if fail_code else "视频生成失败"
                        logger.error(f"[Jimeng] {error_msg}")
                        return False, error_msg
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"[Jimeng] 检查视频状态出错: {str(e)}")
                    time.sleep(check_interval)
                    
            return False, "视频生成超时，请稍后重试"
            
        except Exception as e:
            logger.error(f"[Jimeng] Error in check_video_status: {str(e)}")
            return False, str(e)
        
    def get_authorization(self, access_key, secret_key, region, service, amz_date, security_token, signed_headers, canonical_request):
        """生成AWS授权头"""
        algorithm = 'AWS4-HMAC-SHA256'
        datestamp = amz_date[:8]
        credential_scope = f"{datestamp}/{region}/{service}/aws4_request"
        
        # Create string to sign
        string_to_sign = '\n'.join([
            algorithm,
            amz_date,
            credential_scope,
            hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()
        ])
        
        # Calculate signature
        k_date = self.sign(('AWS4' + secret_key).encode('utf-8'), datestamp)
        k_region = self.sign(k_date, region)
        k_service = self.sign(k_region, service)
        k_signing = self.sign(k_service, 'aws4_request')
        signature = hmac.new(k_signing,
                           string_to_sign.encode('utf-8'),
                           hashlib.sha256).hexdigest()
        
        # Create authorization header
        authorization_header = (
            f"{algorithm} "
            f"Credential={access_key}/{credential_scope}, "
            f"SignedHeaders={signed_headers}, "
            f"Signature={signature}"
        )
        
        return authorization_header
        
    def sign(self, key, msg):
        """计算HMAC-SHA256签名"""
        return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest() 
        
    def text_to_video(self, prompt, model="s2.0", ratio="4:3"):
        """文字转视频
        Args:
            prompt: 提示词
            model: 视频模型，默认s2.0
            ratio: 视频比例，默认4:3
        Returns:
            tuple: (success, result)
        """
        try:
            url = "https://jimeng.jianying.com/mweb/v1/generate_video"
            
            params = {
                "aid": "513695",
                "babi_param": json.dumps({
                    "scenario": "image_video_generation",
                    "feature_key": "image_to_video",
                    "feature_entrance": "to_video",
                    "feature_entrance_detail": "to_video-image_to_video"
                }),
                "device_platform": "web",
                "region": "CN"
            }
            
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'app-sdk-version': '48.0.0',
                'appid': '513695',
                'appvr': '5.8.0',
                'content-type': 'application/json',
                'cookie': self.video_api_config.get("cookie", ""),
                'device-time': str(int(time.time())),
                'lan': 'zh-Hans',
                'loc': 'cn',
                'origin': 'https://jimeng.jianying.com',
                'pf': '7',
                'priority': 'u=1, i',
                'referer': 'https://jimeng.jianying.com/ai-tool/video/generate',
                'sign': self.video_api_config.get("sign", ""),
                'sign-ver': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'msToken': self.video_api_config.get("msToken", ""),
                'a_bogus': self.video_api_config.get("a_bogus", "")
            }
            
            # 获取模型的请求key
            model_req_key = self.config.get("video_models", {}).get(model.lower(), {}).get("model_req_key", "dreamina_ic_generate_video_model_vgfm1.0")
            
            # 获取比例的宽高
            ratio_config = self.config.get("video_ratios", {}).get(ratio, {"width": 720, "height": 540})
            width = ratio_config["width"]
            height = ratio_config["height"]
            
            data = {
                "submit_id": str(uuid.uuid4()),
                "task_extra": json.dumps({
                    "promptSource": "custom",
                    "originSubmitId": str(uuid.uuid4()),
                    "isDefaultSeed": 1,
                    "originTemplateId": "",
                    "imageNameMapping": {},
                    "isUseAiGenPrompt": False,
                    "batchNumber": 1
                }),
                "http_common_info": {"aid": 513695},
                "input": {
                    "seed": random.randint(1000000000, 9999999999),
                    "video_gen_inputs": [{
                        "prompt": prompt,
                        "first_frame_image": {
                            "width": width,
                            "height": height,
                            "image_uri": ""
                        },
                        "fps": 24,
                        "duration_ms": 5000,
                        "video_mode": 2,
                        "template_id": ""
                    }],
                    "priority": 0,
                    "model_req_key": model_req_key
                },
                "mode": "workbench",
                "history_option": {},
                "commerce_info": {
                    "resource_id": "generate_video",
                    "resource_id_type": "str",
                    "resource_sub_type": "aigc",
                    "benefit_type": "basic_video_operation_vgfm"
                },
                "client_trace_data": {}
            }
            
            response = requests.post(url, headers=headers, params=params, json=data)
            video_response = response.json()
            
            if not video_response or video_response.get("ret") != "0":
                return False, f"生成视频失败: {video_response}"
            
            # 获取task_id
            task_id = video_response.get("data", {}).get("aigc_data", {}).get("task", {}).get("task_id")
            if not task_id:
                return False, "未获取到任务ID"
            
            # 检查视频状态
            success, result = self.check_video_status(task_id)
            return success, result
            
        except Exception as e:
            logger.error(f"[Jimeng] Error in text_to_video: {str(e)}")
            return False, str(e) 